# Nuclei Project
这是HT052项目的算子开发工程

## 1. 目录说明
.
├── application		所有用户代码的添加位置, 包括算子库和用户代码
├── Build			Nuclei-SDK提供的构建脚本库, 请勿修改
├── build_dir		构建产物目录
├── Components		Nuclei-SDK提供的覆盖率和性能测试组件
├── LICENSE			
├── Makefile		构建项目所用的Makefile
├── NMSIS			Nuclei MCU Software Interface Standard
├── NMSIS_VERSION
├── npk.yml
├── package.json
├── README.md
├── SConscript
├── sdk.commit
├── setup.bat
├── SoC 			SDK中SoC相关代码
└── tools


## 2. 如何使用
在application目录中添加相关代码.

application
├── inc 				用户包含目录
├── operators_library	算子库目录
├── src 				用户源码目录
└── tests 				用户代码测试目录


在本项目根目录下执行`make`:

make (all) 	构建
make clean	清除构建(删除build_dir)
make dasm	生成dump/dasm/hex/srec/verilog格式
make bin    生成bin文件
make run_qemu 在qemu上运行(目前存在问题)

