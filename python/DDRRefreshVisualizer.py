import json
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from datetime import datetime
import os


class DDRRefreshVisualizer:
    """
    DDR刷新行为可视化工具
    """
    
    def __init__(self, log_dir="python/logs"):
        self.log_dir = log_dir
        self.log_file = os.path.join(log_dir, "ddr_refresh.log")
        self.stats_file = os.path.join(log_dir, "ddr_refresh_stats.json")
        self.output_dir = os.path.join(log_dir, "ddr_refresh_plots")
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 数据存储
        self.access_data = None
        self.stats_data = None
        
    def load_data(self):
        """加载DDR刷新日志和统计数据"""
        # 加载访问日志
        if os.path.exists(self.log_file):
            try:
                self.access_data = pd.read_csv(self.log_file, comment='#')
                print(f"Loaded {len(self.access_data)} access records")
            except Exception as e:
                print(f"Error loading access log: {e}")
                return False
        else:
            print(f"Access log file not found: {self.log_file}")
            return False
            
        # 加载统计数据
        if os.path.exists(self.stats_file):
            try:
                with open(self.stats_file, 'r') as f:
                    self.stats_data = json.load(f)
                print("Loaded statistics data")
            except Exception as e:
                print(f"Error loading stats: {e}")
                return False
        else:
            print(f"Stats file not found: {self.stats_file}")
            return False
            
        return True
    
    def plot_refresh_timeline(self, period_cycles=100000, window_cycles=780, 
                            start_cycle=0, duration_cycles=500000):
        """
        绘制DDR刷新时间轴，显示刷新窗口和访存请求
        
        Args:
            period_cycles: 刷新周期M
            window_cycles: 刷新窗口N
            start_cycle: 开始周期
            duration_cycles: 显示持续时间
        """
        if self.access_data is None:
            print("No data loaded. Call load_data() first.")
            return
            
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10))
        
        # 上半部分：刷新窗口时间轴
        end_cycle = start_cycle + duration_cycles
        
        # 绘制刷新窗口
        current_cycle = start_cycle
        while current_cycle < end_cycle:
            refresh_start = (current_cycle // period_cycles) * period_cycles
            if refresh_start < current_cycle:
                refresh_start += period_cycles
            
            if refresh_start < end_cycle:
                refresh_end = min(refresh_start + window_cycles, end_cycle)
                ax1.add_patch(patches.Rectangle((refresh_start, -0.5), 
                                              refresh_end - refresh_start, 1,
                                              facecolor='red', alpha=0.3, 
                                              label='Refresh Window' if current_cycle == start_cycle else ""))
            current_cycle = refresh_start + period_cycles
        
        ax1.set_xlim(start_cycle, end_cycle)
        ax1.set_ylim(-0.5, 0.5)
        ax1.set_ylabel('Refresh\nWindow')
        ax1.set_title(f'DDR Refresh Timeline (Period={period_cycles}, Window={window_cycles})')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下半部分：各core的访存请求
        filtered_data = self.access_data[
            (self.access_data['begin_cycle'] >= start_cycle) & 
            (self.access_data['begin_cycle'] <= end_cycle)
        ]
        
        # 为每个core分配y轴位置
        cores = sorted(filtered_data[['core_x', 'core_y']].drop_duplicates().values.tolist())
        core_positions = {f"{x}_{y}": i for i, (x, y) in enumerate(cores)}
        
        # 绘制访存请求
        colors = {'TLD': 'blue', 'TST': 'green', 'NOC_READ': 'cyan', 
                 'NOC_WRITE': 'orange', 'NOC_SRC': 'purple', 'NOC_DEST': 'brown'}
        
        for _, row in filtered_data.iterrows():
            core_key = f"{row['core_x']}_{row['core_y']}"
            y_pos = core_positions[core_key]
            
            # 访存请求的矩形
            color = colors.get(row['access_type'], 'gray')
            alpha = 0.8 if row['in_refresh_window'] else 0.4
            
            ax2.add_patch(patches.Rectangle((row['begin_cycle'], y_pos - 0.4),
                                          row['access_cycles'], 0.8,
                                          facecolor=color, alpha=alpha,
                                          edgecolor='black', linewidth=0.5))
            
            # 如果有等待时间，用红色标记
            if row['actual_stall'] > 0:
                ax2.add_patch(patches.Rectangle((row['begin_cycle'] + row['access_cycles'], y_pos - 0.4),
                                              row['actual_stall'], 0.8,
                                              facecolor='red', alpha=0.6,
                                              edgecolor='black', linewidth=0.5))
        
        ax2.set_xlim(start_cycle, end_cycle)
        ax2.set_ylim(-0.5, len(cores) - 0.5)
        ax2.set_ylabel('Core ID')
        ax2.set_xlabel('Simulation Cycle')
        ax2.set_yticks(range(len(cores)))
        ax2.set_yticklabels([f"({x},{y})" for x, y in cores])
        
        # 添加图例
        legend_elements = [patches.Patch(color=color, label=access_type) 
                          for access_type, color in colors.items()]
        legend_elements.append(patches.Patch(color='red', alpha=0.6, label='Stall Time'))
        ax2.legend(handles=legend_elements, loc='upper right')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'refresh_timeline.png'), dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_statistics_summary(self):
        """绘制DDR刷新统计摘要"""
        if self.stats_data is None:
            print("No statistics data loaded. Call load_data() first.")
            return
            
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 刷新命中率
        hit_rate = self.stats_data.get('refresh_hit_rate', 0) * 100
        ax1.bar(['Refresh Hit Rate'], [hit_rate], color='red', alpha=0.7)
        ax1.set_ylabel('Percentage (%)')
        ax1.set_title(f'DDR Refresh Hit Rate: {hit_rate:.2f}%')
        ax1.set_ylim(0, 100)
        
        # 2. 访问类型分布
        access_types = self.stats_data.get('access_types', {})
        if access_types:
            types = list(access_types.keys())
            counts = list(access_types.values())
            ax2.pie(counts, labels=types, autopct='%1.1f%%', startangle=90)
            ax2.set_title('Access Type Distribution')
        
        # 3. 等待时间分布
        stall_dist = self.stats_data.get('stall_distribution', {})
        if stall_dist:
            stall_ranges = sorted([int(k) for k in stall_dist.keys()])
            stall_counts = [stall_dist[str(k)] for k in stall_ranges]
            ax3.bar([f"{k}-{k+9}" for k in stall_ranges], stall_counts, alpha=0.7)
            ax3.set_xlabel('Stall Cycles Range')
            ax3.set_ylabel('Count')
            ax3.set_title('Stall Time Distribution')
            plt.setp(ax3.get_xticklabels(), rotation=45)
        
        # 4. 每个core的统计
        core_stats = self.stats_data.get('core_stats', {})
        if core_stats:
            core_names = list(core_stats.keys())
            hit_rates = [core_stats[core].get('refresh_hits', 0) / 
                        max(core_stats[core].get('total_accesses', 1), 1) * 100 
                        for core in core_names]
            
            ax4.bar(range(len(core_names)), hit_rates, alpha=0.7)
            ax4.set_xlabel('Core ID')
            ax4.set_ylabel('Hit Rate (%)')
            ax4.set_title('Refresh Hit Rate by Core')
            ax4.set_xticks(range(len(core_names)))
            ax4.set_xticklabels([name.replace('core_', '') for name in core_names], rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'statistics_summary.png'), dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_report(self):
        """生成DDR刷新分析报告"""
        if self.stats_data is None or self.access_data is None:
            print("No data loaded. Call load_data() first.")
            return
            
        report_file = os.path.join(self.output_dir, 'ddr_refresh_report.txt')
        
        with open(report_file, 'w') as f:
            f.write("DDR Refresh Analysis Report\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 总体统计
            f.write("Overall Statistics:\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total Accesses: {self.stats_data.get('total_accesses', 0)}\n")
            f.write(f"Refresh Hits: {self.stats_data.get('refresh_hits', 0)}\n")
            f.write(f"Hit Rate: {self.stats_data.get('refresh_hit_rate', 0)*100:.2f}%\n")
            f.write(f"Total Stall Cycles: {self.stats_data.get('total_stall_cycles', 0)}\n")
            f.write(f"Average Stall per Hit: {self.stats_data.get('avg_stall_cycles', 0):.2f} cycles\n")
            f.write(f"Maximum Stall: {self.stats_data.get('max_stall', 0)} cycles\n\n")
            
            # 访问类型统计
            f.write("Access Type Breakdown:\n")
            f.write("-" * 25 + "\n")
            access_types = self.stats_data.get('access_types', {})
            for access_type, count in access_types.items():
                percentage = count / max(self.stats_data.get('total_accesses', 1), 1) * 100
                f.write(f"{access_type}: {count} ({percentage:.1f}%)\n")
            f.write("\n")
            
            # 每个core的详细统计
            f.write("Per-Core Statistics:\n")
            f.write("-" * 20 + "\n")
            core_stats = self.stats_data.get('core_stats', {})
            for core_name, stats in core_stats.items():
                total = stats.get('total_accesses', 0)
                hits = stats.get('refresh_hits', 0)
                hit_rate = hits / max(total, 1) * 100
                avg_stall = stats.get('total_stall', 0) / max(hits, 1)
                
                f.write(f"{core_name}:\n")
                f.write(f"  Total Accesses: {total}\n")
                f.write(f"  Refresh Hits: {hits} ({hit_rate:.1f}%)\n")
                f.write(f"  Average Stall: {avg_stall:.2f} cycles\n")
                f.write(f"  Total Stall: {stats.get('total_stall', 0)} cycles\n\n")
        
        print(f"Report saved to: {report_file}")


def main():
    """主函数，演示可视化功能"""
    visualizer = DDRRefreshVisualizer()
    
    if visualizer.load_data():
        print("Generating visualizations...")
        
        # 生成时间轴图
        visualizer.plot_refresh_timeline(start_cycle=0, duration_cycles=1000000)
        
        # 生成统计摘要图
        visualizer.plot_statistics_summary()
        
        # 生成分析报告
        visualizer.generate_report()
        
        print(f"All visualizations saved to: {visualizer.output_dir}")
    else:
        print("Failed to load data. Make sure DDR refresh logging is enabled and simulation has run.")


if __name__ == "__main__":
    main()
