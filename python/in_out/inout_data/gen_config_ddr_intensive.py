#!/usr/bin/env python3
"""
DDR Intensive测试配置生成脚本
生成用于测试test_ddr_intensive函数的输入输出配置文件
基于C代码中的tensor定义和内存布局
"""

import json
from typing import List, Dict, Any, NamedTuple
import torch
import os

###############################################################################
# Private Function Tools                                                      #
###############################################################################

WORD_BITS = 256
WORD_BYTES = WORD_BITS // 8

def min_strides(shape, width):
    (dim2, dim1, dim0) = shape
    dim0a = WORD_BITS // width
    dim0b = (dim0 + dim0a - 1) // dim0a
    stride0b = 1
    stride1 = dim0b
    stride2 = stride1 * dim1
    return (stride2, stride1, stride0b)

def dtype_convert(dtype="INT4"):
    _map = {
        "INT4"      :   (4, torch.int8      ), # use int8 for calculation
        "INT8"      :   (8, torch.int8      ),
        "INT16"     :   (16,torch.int16     ),
        "INT32"     :   (32,torch.int32     ),
        "FP16"      :   (16,torch.float16   ),
        "FP32"      :   (32,torch.float32   ),
        "BF16"      :   (16,torch.bfloat16  )
    }
    
    if dtype not in _map:
        raise ValueError(f"Unsupported dtype: {dtype}")
    return _map[dtype]

def create_min_tensor(name: str, group_id: int, core_id: int, address: int, shape: tuple, dtype: str) -> Dict[str, Any]:
    width, _ = dtype_convert(dtype)
    (dim2, dim1, dim0) = shape
    stride2, stride1, stride0b = min_strides(shape, width)
    template = {
        "name": f"{name}_{group_id}_{core_id}",
        "address": f"0x{address:08x}",
        "datatype": dtype,
        "dimension": {
            "dim0": dim0,
            "dim1": dim1,
            "dim2": dim2
        },
        "stride": {
            "stride0b": stride0b,
            "stride1": stride1,
            "stride2": stride2
        }
    }
    return template

def mask2core_id(mask: List[int]) -> List[tuple]:
    config = []
    for group_id in range(4):
        for core_id in range(4):
            if mask[group_id] & (1 << core_id):
                config.append((group_id, core_id))
    return config

def get_all_cores() -> List[tuple]:
    """获取所有16个核心的坐标"""
    cores = []
    for group_id in range(4):
        for core_id in range(4):
            cores.append((group_id, core_id))
    return cores

TensorDescriptor = NamedTuple("TensorDescriptor", [
    ("name", str),
    ("address", int),
    ("shape", tuple),
    ("dtype", str),
])

###############################################################################
# DDR Intensive Test Configurations                                          #
###############################################################################

# 地址定义（与C代码一致）
DRAM_ADDR = 0x10000000
SCRATCHPAD0_ADDR = 0x00000000
SCRATCHPAD1_ADDR = 0x00100000
SCRATCHPAD2_ADDR = 0x00200000
SCRATCHPAD3_ADDR = 0x00300000

# 测试参数（与C代码一致）
TEST_TOTAL_ROWS = 1024
TEST_COLS = 128
TEST_TILE_ROWS = 32

def generate_ddr_intensive_configs():
    """生成DDR Intensive测试的配置"""
    
    print("🔧 生成DDR Intensive测试配置...")
    print(f"   总数据大小: {TEST_TOTAL_ROWS}×{TEST_COLS} = {TEST_TOTAL_ROWS * TEST_COLS * 2 / (1024*1024):.1f}MB")
    print(f"   Tile大小: {TEST_TILE_ROWS}×{TEST_COLS} = {TEST_TILE_ROWS * TEST_COLS * 2 / 1024:.1f}KB")
    
    # 使用所有16个核心（与C代码中的mask 0xf, 0xf, 0xf, 0xf一致）
    all_cores = get_all_cores()
    
    # DDR张量定义（基于C代码）
    ddr_tensors = [
        TensorDescriptor(
            name="ddr-src", 
            address=DRAM_ADDR, 
            shape=(1, TEST_TOTAL_ROWS, TEST_COLS),  # (1, 1024, 128)
            dtype="BF16"
        ),
        TensorDescriptor(
            name="ddr-dst", 
            address=DRAM_ADDR + TEST_TOTAL_ROWS * TEST_COLS * 2,  # 偏移256KB
            shape=(1, TEST_TOTAL_ROWS, TEST_COLS),  # (1, 1024, 128)
            dtype="BF16"
        ),
        TensorDescriptor(
            name="ddr-temp", 
            address=DRAM_ADDR + TEST_TOTAL_ROWS * TEST_COLS * 2 * 2,  # 偏移512KB
            shape=(1, TEST_TOTAL_ROWS, TEST_COLS),  # (1, 1024, 128)
            dtype="BF16"
        ),
    ]
    
    # SPAD工作张量定义（基于C代码）
    spad_tensors = [
        TensorDescriptor(
            name="spad-work1", 
            address=SCRATCHPAD0_ADDR, 
            shape=(1, TEST_TILE_ROWS, TEST_COLS),  # (1, 32, 128)
            dtype="BF16"
        ),
        TensorDescriptor(
            name="spad-work2", 
            address=SCRATCHPAD1_ADDR, 
            shape=(1, TEST_TILE_ROWS, TEST_COLS),  # (1, 32, 128)
            dtype="BF16"
        ),
        TensorDescriptor(
            name="spad-work3", 
            address=SCRATCHPAD2_ADDR, 
            shape=(1, TEST_TILE_ROWS, TEST_COLS),  # (1, 32, 128)
            dtype="BF16"
        ),
    ]
    
    in_configs = []
    out_configs = []
    
    # 为每个核心生成配置
    for group_id, core_id in all_cores:
        # 输入配置：DDR源数据
        for tensor in ddr_tensors:
            if tensor.name == "ddr-src":  # 只有源数据作为输入
                config = create_min_tensor(
                    tensor.name, group_id, core_id,
                    tensor.address, tensor.shape, tensor.dtype
                )
                in_configs.append(config)

        # # 输入配置：SPAD工作区（TLD的目标）
        # for tensor in spad_tensors:
        #     config = create_min_tensor(
        #         tensor.name, group_id, core_id,
        #         tensor.address, tensor.shape, tensor.dtype
        #     )
        #     in_configs.append(config)

        # 输出配置：DDR目标数据 + DDR临时数据
        for tensor in ddr_tensors:
            if tensor.name in ["ddr-dst", "ddr-temp"]:  # 目标和临时数据作为输出
                config = create_min_tensor(
                    tensor.name, group_id, core_id,
                    tensor.address, tensor.shape, tensor.dtype
                )
                out_configs.append(config)

        # # 输出配置：SPAD工作区（计算结果）
        # for tensor in spad_tensors:
        #     config = create_min_tensor(
        #         tensor.name, group_id, core_id,
        #         tensor.address, tensor.shape, tensor.dtype
        #     )
        #     out_configs.append(config)
    
    return in_configs, out_configs, ddr_tensors, spad_tensors

def generate_test_input_data(ddr_tensors, spad_tensors):
    """生成测试输入数据"""
    print("\n🔧 生成DDR Intensive测试输入数据...")
    
    # 创建数据目录
    os.makedirs("in_data", exist_ok=True)
    
    all_cores = get_all_cores()
    
    # 为每个核心生成相同的源数据
    for group_id, core_id in all_cores:
        # 创建DDR源数据张量
        # 使用渐变数据便于验证：每行填充行号+1的值
        tensor_src = torch.zeros((1, TEST_TOTAL_ROWS, TEST_COLS), dtype=torch.bfloat16)
        for row in range(TEST_TOTAL_ROWS):
            # 每行填充不同的值，便于调试
            tensor_src[0, row, :] = float((row % 100) + 1) / 100.0  # 0.01, 0.02, ..., 1.00, 0.01, ...
        
        torch.save(tensor_src, f"in_data/ddr-src_{group_id}_{core_id}.pt")

        # 创建SPAD工作区张量（初始化为零，TLD会填充数据）
        for tensor in spad_tensors:
            tensor_spad = torch.zeros((1, TEST_TILE_ROWS, TEST_COLS), dtype=torch.bfloat16)
            torch.save(tensor_spad, f"in_data/{tensor.name}_{group_id}_{core_id}.pt")

    print(f"✅ DDR Intensive测试输入数据已生成到: in_data/")
    print(f"   - ddr-src: {TEST_TOTAL_ROWS}×{TEST_COLS} 渐变数据")
    print(f"   - spad工作区: {TEST_TILE_ROWS}×{TEST_COLS} 零初始化")
    print(f"   - 每行填充不同值，便于验证DDR读写正确性")

def print_memory_layout(ddr_tensors, spad_tensors):
    """打印内存布局信息"""
    print("\n📊 内存布局:")
    print("DDR张量:")
    for tensor in ddr_tensors:
        size_kb = tensor.shape[1] * tensor.shape[2] * 2 / 1024
        print(f"  - {tensor.name}: 0x{tensor.address:08x} ({size_kb:.0f}KB) {tensor.shape}")
    
    print("SPAD张量:")
    for tensor in spad_tensors:
        size_kb = tensor.shape[1] * tensor.shape[2] * 2 / 1024
        print(f"  - {tensor.name}: 0x{tensor.address:08x} ({size_kb:.0f}KB) {tensor.shape}")
    
    total_ddr = sum(t.shape[1] * t.shape[2] * 2 for t in ddr_tensors) / (1024*1024)
    total_spad = sum(t.shape[1] * t.shape[2] * 2 for t in spad_tensors) / 1024
    print(f"\n总内存使用:")
    print(f"  - DDR: {total_ddr:.1f}MB (限制: 128MB)")
    print(f"  - SPAD: {total_spad:.1f}KB per core (限制: 64KB per scratchpad)")

def main():
    """主函数"""
    print("🔧 DDR Intensive测试配置生成器")
    print("   基于test_ddr_intensive.c中的tensor定义")
    print("="*60)
    
    # 生成配置
    in_configs, out_configs, ddr_tensors, spad_tensors = generate_ddr_intensive_configs()
    
    # 保存输入配置
    in_file = "input_tensor.json"
    with open(in_file, "w", encoding='utf-8') as f:
        json.dump(in_configs, f, indent=4, ensure_ascii=False)
    print(f"\n✅ 输入配置已保存到: {in_file}")
    print(f"   配置数量: {len(in_configs)} (每个核心的ddr_src)")
    
    # 保存输出配置
    out_file = "output_tensor.json"
    with open(out_file, "w", encoding='utf-8') as f:
        json.dump(out_configs, f, indent=4, ensure_ascii=False)
    print(f"✅ 输出配置已保存到: {out_file}")
    print(f"   配置数量: {len(out_configs)} (每个核心的ddr_dst和ddr_temp)")
    
    # 打印内存布局
    print_memory_layout(ddr_tensors, spad_tensors)
    
    # 生成测试数据
    generate_test_input_data(ddr_tensors, spad_tensors)
    
    print("\n" + "="*60)
    print("✅ DDR Intensive测试配置生成完成!")
    print("📝 下一步:")
    print("   1. 运行 python gen_input.py 生成实际输入数据")
    print("   2. 运行仿真测试 test_ddr_intensive")
    print("   3. 检查DDR刷新统计日志")
    print("="*60)

if __name__ == "__main__":
    main()
