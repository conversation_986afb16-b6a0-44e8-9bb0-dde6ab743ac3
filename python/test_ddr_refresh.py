#!/usr/bin/env python3
"""
DDR刷新功能测试脚本
用于验证DDR刷新逻辑和日志记录功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from DDRRefresh import DDRRefreshController, DDRRefreshLogger
import GlobalSettings as GlbSet


def test_refresh_controller():
    """测试DDR刷新控制器基本功能"""
    print("Testing DDR Refresh Controller...")
    
    # 创建控制器（小周期便于测试）
    controller = DDRRefreshController(
        period_cycles=100,
        window_cycles=10,
        mode='entry',
        enabled=True,
        enable_logging=False  # 暂时关闭日志以便单独测试
    )
    
    # 测试刷新窗口检测
    test_cases = [
        (0, True, 10),    # 周期开始，在窗口内
        (5, True, 5),     # 窗口中间
        (9, True, 1),     # 窗口末尾
        (10, False, 0),   # 刚出窗口
        (50, False, 0),   # 周期中间
        (99, False, 0),   # 周期末尾
        (100, True, 10),  # 下一个周期开始
        (105, True, 5),   # 下一个周期中间
    ]
    
    print("Testing refresh window detection:")
    for cycle, expected_in_window, expected_remaining in test_cases:
        in_window = controller.is_in_refresh(cycle)
        remaining = controller.remaining_busy(cycle)
        
        status = "✓" if (in_window == expected_in_window and remaining == expected_remaining) else "✗"
        print(f"  {status} Cycle {cycle:3d}: in_window={in_window}, remaining={remaining:2d}")
    
    # 测试访问延迟计算
    print("\nTesting access stall calculation:")
    access_test_cases = [
        (0, 5, 10),   # 访问开始在窗口内，应该等待10周期
        (5, 3, 5),    # 访问开始在窗口内，应该等待5周期
        (10, 5, 0),   # 访问开始在窗口外，不等待
        (95, 10, 0),  # 访问开始在窗口外，不等待
    ]
    
    for begin_cycle, access_cycles, expected_stall in access_test_cases:
        stall = controller.apply_to_access(begin_cycle, access_cycles)
        status = "✓" if stall == expected_stall else "✗"
        print(f"  {status} Access at cycle {begin_cycle:3d} for {access_cycles} cycles: stall={stall:2d}")
    
    print("DDR Refresh Controller test completed.\n")


def test_refresh_logger():
    """测试DDR刷新日志记录功能"""
    print("Testing DDR Refresh Logger...")
    
    # 创建临时日志目录
    test_log_dir = "python/logs/test"
    os.makedirs(test_log_dir, exist_ok=True)
    
    # 创建日志记录器
    logger = DDRRefreshLogger(log_dir=test_log_dir)
    
    # 模拟一些访问记录
    test_accesses = [
        (0, 0, "TLD", 0, 5, 1024, True, 0, 10, 10, 25),
        (0, 1, "TST", 15, 3, 512, False, 15, 0, 0, 8),
        (1, 0, "NOC_READ", 105, 4, 2048, True, 5, 5, 5, 12),
        (1, 1, "NOC_WRITE", 200, 2, 256, False, 0, 0, 0, 6),
    ]
    
    print("Logging test accesses:")
    for core_x, core_y, access_type, begin_cycle, access_cycles, data_bits, \
        in_refresh_window, refresh_position, remaining_refresh_time, actual_stall, total_latency in test_accesses:
        
        logger.log_access(core_x, core_y, access_type, begin_cycle, access_cycles, data_bits,
                         in_refresh_window, refresh_position, remaining_refresh_time, 
                         actual_stall, total_latency)
        
        print(f"  Logged: Core({core_x},{core_y}) {access_type} at cycle {begin_cycle}")
    
    # 保存统计信息
    logger.save_stats()
    
    # 检查生成的文件
    log_file = os.path.join(test_log_dir, "ddr_refresh.log")
    stats_file = os.path.join(test_log_dir, "ddr_refresh_stats.json")
    
    if os.path.exists(log_file):
        print(f"✓ Log file created: {log_file}")
        with open(log_file, 'r') as f:
            lines = f.readlines()
            print(f"  Contains {len(lines)} lines (including headers)")
    else:
        print("✗ Log file not created")
    
    if os.path.exists(stats_file):
        print(f"✓ Stats file created: {stats_file}")
        import json
        with open(stats_file, 'r') as f:
            stats = json.load(f)
            print(f"  Total accesses: {stats.get('total_accesses', 0)}")
            print(f"  Refresh hits: {stats.get('refresh_hits', 0)}")
            print(f"  Hit rate: {stats.get('refresh_hit_rate', 0)*100:.1f}%")
    else:
        print("✗ Stats file not created")
    
    print("DDR Refresh Logger test completed.\n")


def test_integrated_controller():
    """测试集成的控制器（包含日志功能）"""
    print("Testing Integrated DDR Refresh Controller...")
    
    # 创建临时日志目录
    test_log_dir = "python/logs/test_integrated"
    os.makedirs(test_log_dir, exist_ok=True)
    
    # 创建带日志功能的控制器
    controller = DDRRefreshController(
        period_cycles=50,
        window_cycles=5,
        mode='entry',
        enabled=True,
        enable_logging=True
    )
    
    # 修改日志目录
    if controller.logger:
        controller.logger.log_dir = test_log_dir
        controller.logger.log_file = os.path.join(test_log_dir, "ddr_refresh.log")
        controller.logger.stats_file = os.path.join(test_log_dir, "ddr_refresh_stats.json")
        controller.logger._init_log_file()
    
    # 模拟一系列访问
    print("Simulating memory accesses:")
    test_scenarios = [
        # (begin_cycle, access_cycles, core_x, core_y, access_type, data_bits)
        (0, 3, 0, 0, "TLD", 1024),      # 命中刷新窗口
        (2, 2, 0, 1, "TST", 512),       # 命中刷新窗口
        (10, 4, 1, 0, "TLD", 2048),     # 不命中刷新窗口
        (50, 2, 1, 1, "TST", 256),      # 下一个周期，命中刷新窗口
        (52, 1, 2, 0, "NOC_READ", 128), # 命中刷新窗口
        (60, 3, 2, 1, "NOC_WRITE", 512), # 不命中刷新窗口
    ]
    
    for begin_cycle, access_cycles, core_x, core_y, access_type, data_bits in test_scenarios:
        # 计算基础延迟（模拟）
        base_latency = 10  # 假设基础延迟
        
        # 应用刷新延迟
        extra_stall = controller.apply_to_access(
            begin_cycle, access_cycles,
            core_x=core_x, core_y=core_y,
            access_type=access_type, data_bits=data_bits,
            total_latency=base_latency
        )
        
        total_latency = base_latency + extra_stall
        in_window = controller.is_in_refresh(begin_cycle)
        
        print(f"  Core({core_x},{core_y}) {access_type} at cycle {begin_cycle:2d}: "
              f"stall={extra_stall:2d}, total_latency={total_latency:2d}, "
              f"in_window={in_window}")
    
    # 完成日志记录
    controller.finalize_logging()
    
    # 检查结果
    if controller.logger:
        stats_file = controller.logger.stats_file
        if os.path.exists(stats_file):
            import json
            with open(stats_file, 'r') as f:
                stats = json.load(f)
                print(f"\nFinal Statistics:")
                print(f"  Total accesses: {stats.get('total_accesses', 0)}")
                print(f"  Refresh hits: {stats.get('refresh_hits', 0)}")
                print(f"  Hit rate: {stats.get('refresh_hit_rate', 0)*100:.1f}%")
                print(f"  Total stall cycles: {stats.get('total_stall_cycles', 0)}")
                print(f"  Average stall per hit: {stats.get('avg_stall_cycles', 0):.1f}")
    
    print("Integrated DDR Refresh Controller test completed.\n")


def test_global_settings_integration():
    """测试与GlobalSettings的集成"""
    print("Testing GlobalSettings Integration...")
    
    # 检查全局设置
    print(f"DDR_REFRESH_ENABLE: {GlbSet.DDR_REFRESH_ENABLE}")
    print(f"DDR_REFRESH_PERIOD: {GlbSet.DDR_REFRESH_PERIOD}")
    print(f"DDR_REFRESH_WINDOW: {GlbSet.DDR_REFRESH_WINDOW}")
    print(f"DDR_REFRESH_MODE: {GlbSet.DDR_REFRESH_MODE}")
    
    # 使用全局设置创建控制器
    controller = DDRRefreshController(enable_logging=False)
    
    print(f"Controller enabled: {controller.enabled}")
    print(f"Controller period: {controller.period}")
    print(f"Controller window: {controller.window}")
    print(f"Controller mode: {controller.mode}")
    
    print("GlobalSettings integration test completed.\n")


def main():
    """运行所有测试"""
    print("DDR Refresh Functionality Test Suite")
    print("=" * 50)
    
    try:
        test_refresh_controller()
        test_refresh_logger()
        test_integrated_controller()
        test_global_settings_integration()
        
        print("All tests completed successfully! ✓")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
