# * * * * * * * * * * * * * * * * #
# NPU Core (NPU Bank)
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #

import Modules as Mods
import SIMTemplate as SIMTemp
import GlobalSettings as GlbSet
import copy

from sim_backend_torch import GOLDEN_VM_SYSTEM


class NPUCore:
    def __init__(self, x, y, top, gldres=False, golden_vm_system: GOLDEN_VM_SYSTEM = None):

        self.x = x
        self.y = y
        self.id = None
        self.group = None
        self.monitoring = False
        self.flag_receive_prim = False
        self.top = top
        self.prim_record = {}
        self.global_id = None
        self.gldres = gldres

        # Primitive Pool
        self.prim_pool = {'WaitingPrims': SIMTemp.Queue(queue_name='WaitingPrims'),
                          'RunningPrims': SIMTemp.Queue(queue_name='RunningPrims'),
                          'FinishedPrims': SIMTemp.Queue(queue_name='FinishedPrims')}

        # Return value storage for instructions that need return values
        self.pending_return_values = {}  # {prim_id: {'ret': value, 'cycle': completion_cycle, 'inst_id': instruction_id}}

        # NPU Bank
        self.module_list = []
        self.mem_list = []
        self.func_list = []
        current_id = 0

        # Memory Modules
        self.dram_banks = []
        dram_bank_addr_byte = GlbSet.DRAMBank_Base_Address_Byte
        for i in range(GlbSet.DRAMBank_per_NPUCore):
            self.dram_banks.append(Mods.DRAMBank(name='DRAMBank', id=current_id + i, parent_core=self))
            self.dram_banks[-1].address_space['Addr_Begin'] = dram_bank_addr_byte
            dram_bank_addr_byte += GlbSet.DRAMBank_Capacity_Byte
            self.dram_banks[-1].address_space['Addr_End'] = dram_bank_addr_byte - 1
            self.module_list.append(self.dram_banks[-1])
            self.mem_list.append(self.dram_banks[-1])
        current_id += GlbSet.DRAMBank_per_NPUCore  # default: 1

        self.sram_banks = []
        lmem_id = 0
        for i in range(GlbSet.SRAMBank_per_NPUCore):
            self.sram_banks.append(Mods.SRAMBank(name='Scratchpad', id=current_id + i, parent_core=self))
            self.sram_banks[-1].address_space['Addr_Begin'] = GlbSet.SRAMBank_Base_Address_Lmem + (lmem_id << 20)
            self.sram_banks[-1].address_space['Addr_End'] = GlbSet.SRAMBank_Base_Address_Lmem + (lmem_id << 20) + \
                                                            ((GlbSet.SRAMBank_Capacity_Lmem - 1) << 5)
            lmem_id += 1
            self.module_list.append(self.sram_banks[-1])
            self.mem_list.append(self.sram_banks[-1])
        current_id += GlbSet.SRAMBank_per_NPUCore  # default: 4

        # Compute Modules
        self.vector_processing_unit = Mods.VPU(name='VPU', id=current_id, parent_core=self)
        self.module_list.append(self.vector_processing_unit)
        self.func_list.append(self.vector_processing_unit)
        current_id += GlbSet.VPU_per_NPUCore  # default: 1

        self.cim_cluster = Mods.CIMCluster(name='CIMCluster', id=current_id, parent_core=self)
        self.module_list.append(self.cim_cluster)
        for cur_page in self.cim_cluster.pages:
            self.mem_list.append(cur_page)  # temporary
        self.func_list.append(self.cim_cluster.macarray)
        current_id += GlbSet.CIMCluster_per_NPUCore  # default: 1

        # Tensor Load and Store Modules
        self.tensor_load_store_unit = Mods.LoadStoreUnit(name='TensorLoadStore', id=current_id, parent_core=self)
        self.module_list.append(self.tensor_load_store_unit)
        self.func_list.append(self.tensor_load_store_unit)
        current_id += GlbSet.TensorLoadStore_per_NPUCore  # default: 1

        # Tensor Management Modules
        self.tensor_manipulate_unit = Mods.ManageMod(name='TensorManipulate', id=current_id, parent_core=self)
        self.module_list.append(self.tensor_manipulate_unit)
        self.func_list.append(self.tensor_manipulate_unit)
        current_id += GlbSet.TensorManipulate_per_NPUCore  # default: 1

        # Communication Modules
        if self.top:
            self.noc_router_tx = Mods.NoCRouter(name='NoCRouterTX', id=current_id, noc=self.top.noc, parent_core=self,
                                                port_type='TX')
            self.module_list.append(self.noc_router_tx)
            self.func_list.append(self.noc_router_tx)
            current_id += GlbSet.NoCRouter_per_NPUCore  # default: 1
            self.noc_router_rx = Mods.NoCRouter(name='NoCRouterRX', id=current_id, noc=self.top.noc, parent_core=self,
                                                port_type='RX')
            self.module_list.append(self.noc_router_rx)
            self.func_list.append(self.noc_router_rx)
            current_id += GlbSet.NoCRouter_per_NPUCore  # default: 1

        # Control
        self.ctrl = Mods.Ctrl(name='NPUControl', id=current_id, parent_core=self)
        current_id += 1

        # Other Modules
        # self.mux = Mods.MUX(name='MUX', id=current_id)
        # self.module_list.append(self.mux)
        # self.func_list.append(self.mux)
        # current_id += 1                                        # default: 1
        #
        # self.xbar = Mods.Xbar(name='Xbar', id=current_id)
        # self.module_list.append(self.xbar)
        # self.func_list.append(self.xbar)
        # current_id += 1                                        # default: 1

        self.next_cycle = None

        self.golden_vm_system = golden_vm_system

    def update(self, simulation_cycle):
        # NOTE: The order of the following code blocks should NOT be easily adjusted !!!!

        # Whether WaitingPrims to RunningPrims
        waitprim2runprim_flag = self.prim_pool['WaitingPrims'].not_empty()
        if waitprim2runprim_flag:
            target_prim = self.prim_pool['WaitingPrims'].top()
            if target_prim.type != SIMTemp.PrimName.NOC:
                for current_module in target_prim.module_use:
                    if current_module.state != Mods.ModState.IDLE:
                        waitprim2runprim_flag = False
                        break
            else:           # NOC Primitive, src and dst core has to run NOC prim simultaneously
                if target_prim.noc_settings.first_update_core() == self.global_id:
                    # first check if both core's top prim is NOC prim
                    src_core = target_prim.noc_settings.src_core
                    src_top_prim = src_core.prim_pool['WaitingPrims'].top()
                    dst_core = target_prim.noc_settings.dst_core
                    dst_top_prim = dst_core.prim_pool['WaitingPrims'].top()
                    both_ready = src_top_prim.prim_id == dst_top_prim.prim_id
                    if both_ready:
                        # then check if all modules are IDLE
                        for current_module in target_prim.module_use:
                            if current_module.state != Mods.ModState.IDLE:
                                waitprim2runprim_flag = False
                                break
                    else:
                        waitprim2runprim_flag = False
                    # / * When performing the first core update, label both noc_prim,
                    # indicating that both of them are executable. * /
                    if waitprim2runprim_flag:
                        src_top_prim.noc_settings.executable_label = True
                        dst_top_prim.noc_settings.executable_label = True
                # / * for the second core, check if it's executable * /
                elif not target_prim.noc_settings.executable_label:
                    waitprim2runprim_flag = False

        # Whether RunningPrims to FinishedPrims
        runprim2fsdprim_flag = False
        runprim2fsdprim_index_list = []
        if simulation_cycle == self.next_cycle:
            current_index = 0
            if self.prim_pool['RunningPrims'].not_empty():
                for current_prim in self.prim_pool['RunningPrims'].array:
                    if current_prim.end_cycle == simulation_cycle:
                        runprim2fsdprim_flag = True
                        runprim2fsdprim_index_list.append(current_index)
                    current_index += 1

        # All needed modules are IDLE, start run target prim
        if waitprim2runprim_flag:
            target_prim = self.prim_pool['WaitingPrims'].pop()
            target_prim.begin_cycle = simulation_cycle
            self.prim_pool['RunningPrims'].push(target_prim)

            # Exception: The NOC instruction has been executed by the other core and all modules have been set to BUSY.
            if target_prim.type == SIMTemp.PrimName.NOC and \
                    target_prim.noc_settings.first_update_core() != self.global_id:
                # directly set the end cycle according to the other core
                is_src = target_prim.noc_settings.src_group == self.group and target_prim.noc_settings.src_id == self.id
                is_dst = target_prim.noc_settings.dst_group == self.group and target_prim.noc_settings.dst_id == self.id
                if is_src:
                    other_core = target_prim.noc_settings.dst_core
                elif is_dst:
                    other_core = target_prim.noc_settings.src_core
                else:
                    raise Exception('Not src or dst core ???')
                noc_prim_in_other_core = other_core.prim_pool['RunningPrims'].bottom()
                assert noc_prim_in_other_core.prim_id == target_prim.prim_id
                target_prim.end_cycle = noc_prim_in_other_core.end_cycle
            else:
                for current_module in target_prim.module_use:
                    current_module.run_prim(target_prim)

            if self.monitoring:
                print('NPU Group.ID:', str(self.group) + '.' + str(self.id) + ',', 'Start:', target_prim)
                target_prim.print_module_use()

        # Move finished prims, release module resource
        # Execute Golden Model Here!
        if runprim2fsdprim_flag:
            num_poped = 0
            for cur_id in runprim2fsdprim_index_list:           # Note that the index must be in ascending order
                target_prim = self.prim_pool['RunningPrims'].pop(index=cur_id-num_poped)
                if self.gldres:                                 # Run golden model or not
                    ret_value = target_prim.golden_run(self.global_id, self.golden_vm_system)
                    # Store return value if this primitive has an instruction ID and returns a value
                    if hasattr(target_prim, 'inst_id') and target_prim.inst_id is not None and ret_value is not None and ret_value != -1:
                        self.pending_return_values[target_prim.prim_id] = {
                            'ret': ret_value,
                            'cycle': simulation_cycle,
                            'inst_id': target_prim.inst_id,
                            'inst_type': getattr(target_prim, 'inst_type', 'nice'),
                            'core_id': self.global_id
                        }
                        if self.monitoring:
                            print(f'NPU Core {self.global_id}: Stored return value 0x{ret_value:08x} for prim_id {target_prim.prim_id}, inst_id {target_prim.inst_id}, core.id={self.id}')
                num_poped += 1
                for cur_mod in target_prim.module_use:
                    if cur_mod.parent_core == self:
                        cur_mod.state = Mods.ModState.IDLE
                        cur_mod.set_cycles_zero()
                self.prim_pool['FinishedPrims'].push(target_prim)
                if self.monitoring:
                    print('NPU Group.ID:', str(self.group) + '.' + str(self.id) + ',', 'Finish:', target_prim)

        # Update next cycle
        if waitprim2runprim_flag or runprim2fsdprim_flag:
            self.next_cycle = simulation_cycle + 1
        elif self.prim_pool['RunningPrims'].not_empty():
            next_cycle_find = simulation_cycle + 1e10
            for current_prim in self.prim_pool['RunningPrims'].array:
                next_cycle_find = min(next_cycle_find, current_prim.end_cycle)
            self.next_cycle = next_cycle_find
        else:
            self.next_cycle = simulation_cycle + 1e10

    def prim_receive(self, dispatched_prim):
        if dispatched_prim.type == SIMTemp.PrimName.GROUP_MASK:
            # Affect whether to receive instructions, execute immediately.
            self.ctrl.run_prim(dispatched_prim)
            return

        if dispatched_prim.type == SIMTemp.PrimName.NOC:

            # Current version, both src and dst core receives noc primitive
            is_src = dispatched_prim.noc_settings.src_group == self.group and \
                     dispatched_prim.noc_settings.src_id == self.id
            is_dst = dispatched_prim.noc_settings.dst_group == self.group and \
                     dispatched_prim.noc_settings.dst_id == self.id
            if not (is_src or is_dst):
                return

            # Previous version, only src core receives noc primitive
            # if_src = dispatched_prim.noc_settings.src_group == self.group and \
            #          dispatched_prim.noc_settings.src_id == self.id
            # if not if_src:
            #     return

            # Previous version
            # For NoC Primitive, if its src or dst DOES NOT point to current NPU, ignore it
            # if_src = dispatched_prim.noc_settings.src_group == self.group and \
            #          dispatched_prim.noc_settings.src_id == self.id
            # if_dst = dispatched_prim.noc_settings.dst_group == self.group and \
            #          dispatched_prim.noc_settings.dst_id == self.id
            # related = if_src or if_dst
            # if not related:
            #     return

        if self.flag_receive_prim:
            # print('group:', self.group, ', id:', self.id, ', receiving prim:', dispatched_prim)
            received_prim = copy.deepcopy(dispatched_prim)
            received_prim.layout_translate()
            
            # For NOC primitives, simplified approach - no partner restoration needed
            if received_prim.type in [SIMTemp.PrimName.NOC_SRC, SIMTemp.PrimName.NOC_DEST]:
                # Store reference to top for NOC transfer data access
                if self.top:
                    received_prim.noc_settings.top_simulator = self.top

            self.prim_pool['WaitingPrims'].push(received_prim)

            # find corresponding memory modules for all tensors within this primitive
            self.prim_mem_find(received_prim)

            # analyze the resource utilization of this primitive → update module_use
            self.prim_module_use(received_prim)

            # whether transfer floating point data to CIM ? if so, occupy TMU and align
            out_dst_cim = isinstance(received_prim.tensor_out.memory, Mods.CIMCPage) or \
                    isinstance(received_prim.noc_settings.dst_memory, Mods.CIMCPage)
            out_dst_fp = received_prim.tensor_in1.type == 'FP' or received_prim.tensor_in1.type == 'BF'
            potential_align_list = [SIMTemp.PrimName.TLD, SIMTemp.PrimName.NOC,
                                    SIMTemp.PrimName.MOV, SIMTemp.PrimName.TRANS]
            if out_dst_cim and out_dst_fp and received_prim.type in potential_align_list:
                # make sure TMU occupation
                if self.tensor_manipulate_unit not in received_prim.module_use:
                    received_prim.module_use.append(self.tensor_manipulate_unit)
                # Alignment requires 2X Read
                received_prim.tensor_in1.bits *= 2

    def whether_stall(self):
        return self.prim_pool['WaitingPrims'].is_empty() and self.prim_pool['RunningPrims'].is_empty()

    def energy_calculate(self):
        raise NotImplementedError()

    def show_memory_space(self):
        for memory in self.mem_list:
            print(memory)

    def get_group_id(self):
        return str(self.group) + '.' + str(self.id)

    def record_target_finished_prim(self, prim_name):
        self.prim_record[prim_name] = []
        prim_type_list = []
        if prim_name == 'TP':
            prim_type_list = [SIMTemp.PrimName.GEMM, SIMTemp.PrimName.GEMV]
        elif prim_name == 'VP':
            prim_type_list = [SIMTemp.PrimName.VV_V, SIMTemp.PrimName.VS_V,
                              SIMTemp.PrimName.V_S, SIMTemp.PrimName.V_V]
        elif prim_name == 'LDST':
            prim_type_list = [SIMTemp.PrimName.TLD, SIMTemp.PrimName.TST]
        elif prim_name == 'TM':
            prim_type_list = [SIMTemp.PrimName.TRANS, SIMTemp.PrimName.MOV, SIMTemp.PrimName.BC]
        elif prim_name == 'NOC':
            prim_type_list = [SIMTemp.PrimName.NOC]
        elif prim_name == 'NOC_SRC':
            prim_type_list = [SIMTemp.PrimName.NOC_SRC]
        elif prim_name == 'NOC_DEST':
            prim_type_list = [SIMTemp.PrimName.NOC_DEST]
        for finished_prim in self.prim_pool['FinishedPrims'].array:
            if finished_prim.type in prim_type_list:
                self.prim_record[prim_name].append((finished_prim.begin_cycle, finished_prim.end_cycle))

    def print_target_finished_prim(self, prim_type):
        for finished_prim in self.prim_pool['FinishedPrims'].array:
            if finished_prim.type == prim_type:
                print(finished_prim)

    def record_finished_prim(self):
        self.record_target_finished_prim(prim_name='LDST')
        self.record_target_finished_prim(prim_name='TP')
        self.record_target_finished_prim(prim_name='VP')
        self.record_target_finished_prim(prim_name='TM')
        self.record_target_finished_prim(prim_name='NOC')

    def __repr__(self):
        return f'NPUCore(x={self.x}, y={self.y}, group={self.group}, id={self.id}, gid={self.global_id}, ' \
               f'receive={self.flag_receive_prim}, stall={self.whether_stall()})'

    # ---------- specific functions ----------

    # Input addr, and return which memory.
    # If addr is None, then return None
    def mem_find(self, addr):
        if addr is not None:
            for cur_mem in self.mem_list:
                if cur_mem.address_space['Addr_Begin'] <= addr <= cur_mem.address_space['Addr_End']:
                    return cur_mem
            raise Exception('Wrong Address !!! Can NOT find corresponding memory !!', hex(addr))
        else:
            return None

    # Find memory for all tensors used in input primitive.
    def prim_mem_find(self, prim):
        prim.tensor_in1.memory = self.mem_find(prim.tensor_in1.byte_base)
        prim.tensor_in2.memory = self.mem_find(prim.tensor_in2.byte_base)
        prim.tensor_out.memory = self.mem_find(prim.tensor_out.byte_base)
        prim.tensor_orig.memory = self.mem_find(prim.tensor_orig.byte_base)
        prim.conv_settings.memory = self.mem_find(prim.conv_settings.byte_base_wt)
        if prim.type == SIMTemp.PrimName.NOC:
            prim.noc_settings.src_core = self.top.find_core_group_id(group=prim.noc_settings.src_group,
                                                                     id=prim.noc_settings.src_id)
            prim.noc_settings.src_memory = prim.noc_settings.src_core.mem_find(prim.noc_settings.src_addr)
            prim.noc_settings.dst_core = self.top.find_core_group_id(group=prim.noc_settings.dst_group,
                                                                     id=prim.noc_settings.dst_id)
            prim.noc_settings.dst_memory = prim.noc_settings.dst_core.mem_find(prim.noc_settings.dst_addr)

    # Analyze resource utilization of input primitive
    # Add first, then remove any None and duplicate elements.
    def prim_module_use(self, prim):

        # first add memory modules to occupy
        prim.module_use.append(prim.tensor_in1.memory)
        prim.module_use.append(prim.tensor_in2.memory)
        prim.module_use.append(prim.tensor_out.memory)
        prim.module_use.append(prim.tensor_orig.memory)
        prim.module_use.append(prim.conv_settings.memory)
        prim.module_use.append(prim.noc_settings.src_memory)
        prim.module_use.append(prim.noc_settings.dst_memory)

        tensor_load_store_list = [SIMTemp.PrimName.TLD, SIMTemp.PrimName.TST]
        vector_processing_list = [SIMTemp.PrimName.VV_V, SIMTemp.PrimName.VS_V,
                                  SIMTemp.PrimName.V_S, SIMTemp.PrimName.V_V]
        tensor_processing_list = [SIMTemp.PrimName.GEMV, SIMTemp.PrimName.GEMM]
        tensor_manipulate_list = [SIMTemp.PrimName.TRANS, SIMTemp.PrimName.MOV, SIMTemp.PrimName.BC]
        noc_list = [SIMTemp.PrimName.NOC]
        noc_src_list = [SIMTemp.PrimName.NOC_SRC]
        noc_dest_list = [SIMTemp.PrimName.NOC_DEST]
        noc_fence_list = [SIMTemp.PrimName.NOC_FENCE]
        control_list = [SIMTemp.PrimName.RD_LMEM, SIMTemp.PrimName.WR_LMEM, SIMTemp.PrimName.SWCIMC]

        # then add functional modules to occupy
        if prim.type in tensor_load_store_list:
            prim.module_use.append(self.tensor_load_store_unit)
            # Maybe will write multiple pages consecutively
            if prim.tensor_out.layout['dim2'] > 1 and isinstance(prim.tensor_out.memory, Mods.CIMCPage):
                total_pages_to_write = prim.tensor_out.layout['dim2']
                for cur_page_count in range(total_pages_to_write):
                    cur_page_id = cur_page_count + prim.tensor_out.memory.id
                    if cur_page_count != 0:
                        prim.module_use.append(self.cim_cluster.pages[cur_page_id])
                    bytes_per_page = prim.tensor_out.bits // total_pages_to_write // 8
                    cur_byte_base = prim.tensor_out.byte_base + bytes_per_page * cur_page_count
                    prim.tensor_out.byte_base_list.append(cur_byte_base)
                    assert cur_page_id < GlbSet.CIMM_PG_NUM
        elif prim.type in vector_processing_list:
            prim.module_use.append(self.vector_processing_unit)
        elif prim.type in tensor_processing_list:
            prim.module_use.append(self.cim_cluster.macarray)
        elif prim.type in control_list:
            prim.module_use.append(self.ctrl)
        elif prim.type in tensor_manipulate_list:
            prim.module_use.append(self.tensor_manipulate_unit)
        elif prim.type in noc_list:
            prim.module_use.append(prim.noc_settings.src_core.noc_router_tx)
            prim.module_use.append(prim.noc_settings.dst_core.noc_router_rx)
            # might occupy LSU if memory is DRAM
            if isinstance(prim.noc_settings.src_memory, Mods.DRAMBank):
                prim.module_use.append(prim.noc_settings.src_core.tensor_load_store_unit)
            if isinstance(prim.noc_settings.dst_memory, Mods.DRAMBank):
                prim.module_use.append(prim.noc_settings.dst_core.tensor_load_store_unit)
        # NOC_SRC: Only source core resources
        elif prim.type in noc_src_list:
            # Check if this core is the source core
            if self == prim.noc_settings.src_core:
                prim.module_use.append(self.noc_router_tx)
                prim.module_use.append(prim.noc_settings.src_memory)
                if isinstance(prim.noc_settings.src_memory, Mods.DRAMBank):
                    prim.module_use.append(self.tensor_load_store_unit)
        # NOC_DEST: Only destination core resources
        elif prim.type in noc_dest_list:
            # Check if this core is the destination core
            if self == prim.noc_settings.dst_core:
                prim.module_use.append(self.noc_router_rx)
                prim.module_use.append(prim.noc_settings.dst_memory)
                if isinstance(prim.noc_settings.dst_memory, Mods.DRAMBank):
                    prim.module_use.append(self.tensor_load_store_unit)
        # NOC_FENCE: No resource allocation needed
        elif prim.type in noc_fence_list:
            pass  # Fence doesn't occupy resources
        else:
            raise Exception('DO NOT Support this Primitive NOW!', prim)

        # remove None and duplicate elements
        prim.module_use = list(set(prim.module_use) - {None})

        prim.module_use = sorted(prim.module_use, key=lambda x: (isinstance(x, Mods.CIMCPage), x.id))

    def get_and_clear_return_values(self):
        """
        Get all pending return values and clear the storage.
        Returns: dict of {prim_id: return_value_info}
        """
        return_values = self.pending_return_values.copy()
        self.pending_return_values.clear()
        return return_values


if __name__ == '__main__':
    print('test for prim_receive')
    maskk = 7
    print(format(maskk + pow(2, 8), 'b')[::-1])
    for id in range(8):
        print(id, format(maskk + pow(2, 8), 'b')[::-1][id])

    print('test for memory space')
    core_test = NPUCore(x=0, y=0, top=None)
    core_test.show_memory_space()
