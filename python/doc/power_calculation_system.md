# NPU模拟器功耗计算系统实现文档

## 1. 概述

NPU（神经处理单元）模拟器中的功耗计算系统采用了基于操作的功耗模型（Operation-based Power Model），通过跟踪各硬件模块执行的操作并累积相应的能量消耗来计算总功耗。该系统支持字节级别的精确功耗计算，并为不同类型的硬件模块定义了特定的能耗参数。

## 2. 系统架构

### 2.1 模块层次结构

功耗计算系统基于以下模块层次结构：

```
NPUCore
├── DRAMBank (内存模块)
├── SRAMBank (内存模块)
├── VPU (向量处理单元)
├── CIMCluster (计算存储一体化集群)
│   ├── CIMCPage (CIM页面)
│   └── CIMMACArray (MAC阵列)
├── LoadStoreUnit (加载存储单元)
├── TensorManipulateUnit (张量操作单元)
├── NoCRouter (片上网络路由器)
└── Ctrl (控制单元)
```

### 2.2 基类设计

所有模块都继承自`ModuleTemp`基类，该类定义了功耗计算的基本框架：

```python
class ModuleTemp(ABC):
    def __init__(self, name, id, parent_core):
        self.op_energy = {}    # 操作能耗参数 (pJ/byte)
        self.record = {}       # 操作记录 (byte数)
        
    @abstractmethod
    def calculate_energy(self):
        # 所有模块必须实现的能量计算方法
        pass
```

## 3. 功耗参数定义

### 3.1 能耗参数表 (GlobalSettings.py)

| 模块类型 | 操作类型 | 能耗值 | 单位 | 说明 |
|---------|---------|--------|------|------|
| DRAM | 读取 | 0.8 × 8 = 6.4 | pJ/byte | 0.8 pJ/bit |
| DRAM | 写入 | 0.8 × 8 = 6.4 | pJ/byte | 0.8 pJ/bit |
| SRAM | 读取 | 0.05 × 8 = 0.4 | pJ/byte | 50 fJ/bit |
| SRAM | 写入 | 0.05 × 8 = 0.4 | pJ/byte | 50 fJ/bit |
| VPU | 加法 | 0.00 (TBD) | pJ/byte | 待定 |
| VPU | 乘法 | 0.00 (TBD) | pJ/byte | 待定 |
| VPU | 逻辑运算 | 0.00 (TBD) | pJ/byte | 待定 |
| CIM | MAC运算 | 0.1 | pJ/byte | 10 TOPS/W效率 |
| NoC | 通信 | 4 × 8 = 32 | pJ/byte | 4 pJ/bit per mesh hop |

### 3.2 未定义参数

以下参数在当前实现中设置为0，需要后续完善：
- VPU的加法、乘法和逻辑运算能耗
- DWEngine（深度引擎）的运算能耗
- CIM的读写能耗

## 4. 功耗计算实现

### 4.1 内存模块功耗计算

#### DRAMBank
```python
def calculate_energy(self):
    read_energy = self.op_energy['Read_Byte'] * self.record['Read_Byte_Num']
    write_energy = self.op_energy['Write_Byte'] * self.record['Write_Byte_Num']
    return read_energy + write_energy
```

#### SRAMBank
与DRAMBank相同的计算方式，但使用不同的能耗参数（SRAM能耗更低）。

### 4.2 计算模块功耗计算

#### VPU (向量处理单元)
```python
def calculate_energy(self):
    add_energy = self.op_energy['Add_Byte'] * self.record['Add_Byte_Num']
    mul_energy = self.op_energy['Mul_Byte'] * self.record['Mul_Byte_Num']
    logic_energy = self.op_energy['Logic_Byte'] * self.record['Logic_Byte_Num']
    return add_energy + mul_energy + logic_energy
```

VPU在执行原语时会根据操作类型（加法、乘法、逻辑运算）和数据宽度记录相应的操作字节数。

#### CIMMACArray (CIM MAC阵列)
```python
def calculate_energy(self):
    mac_energy = self.op_energy['MAC_Byte'] * self.record['MAC_Byte_Num']
    return mac_energy
```

CIM的MAC运算能耗基于10 TOPS/W的效率目标设计。

### 4.3 通信模块功耗计算

#### NoCRouter
```python
def calculate_energy(self):
    comm_energy = self.op_energy['Comm_Byte'] * self.record['Comm_Byte_Num']
    return comm_energy
```

NoC的通信能耗考虑了mesh网络中每跳的能耗。

### 4.4 NPUCore总能耗计算

NPUCore的`energy_calculate()`方法需要实现以聚合所有模块的能耗：

```python
def energy_calculate(self):
    """
    计算NPU核心的总能耗，汇总所有模块的能量消耗
    
    返回:
        float: 总能耗（单位：picojoules）
    """
    total_energy = 0.0
    
    # 计算module_list中所有模块的能耗
    for module in self.module_list:
        if hasattr(module, 'calculate_energy'):
            module_energy = module.calculate_energy()
            if module_energy is not None:
                total_energy += module_energy
    
    # 计算CIM页面的能耗
    for page in self.cim_cluster.pages:
        if hasattr(page, 'calculate_energy'):
            page_energy = page.calculate_energy()
            if page_energy is not None:
                total_energy += page_energy
    
    # 计算CIM MAC阵列的能耗
    if hasattr(self.cim_cluster.macarray, 'calculate_energy'):
        mac_energy = self.cim_cluster.macarray.calculate_energy()
        if mac_energy is not None:
            total_energy += mac_energy
    
    return total_energy
```

## 5. 功耗记录机制

### 5.1 操作记录

每个模块在执行原语（primitive）时会记录操作的字节数：

```python
# DRAMBank读操作示例
if primitive.type == SIMTemp.PrimName.TLD:  # Tensor Load
    read_bits = primitive.tensor_in1.bits
    self.record['Read_Byte_Num'] = read_bits // 8
```

### 5.2 多访问支持

SRAMBank支持在单个周期内的多个访问（读取in1、in2、orig，写入out）：

```python
if self.addr_match(primitive.tensor_in1.byte_base):
    self.record['Read_Byte_Num'] += primitive.tensor_in1.bits // 8
if self.addr_match(primitive.tensor_in2.byte_base):
    self.record['Read_Byte_Num'] += primitive.tensor_in2.bits // 8
# ... 其他访问
```

## 6. 当前实现状态

### 6.1 已实现功能

1. **基础框架**：ModuleTemp基类和calculate_energy抽象方法
2. **内存功耗**：DRAM和SRAM的读写功耗计算
3. **CIM功耗**：MAC运算的功耗计算（基于10 TOPS/W效率）
4. **NoC功耗**：片上网络通信功耗计算
5. **操作记录**：各模块的操作字节数记录机制

### 6.2 待完善功能

1. **NPUCore.energy_calculate()**：当前抛出NotImplementedError，需要实现能耗汇总
2. **VPU功耗参数**：加法、乘法、逻辑运算的能耗参数为0
3. **DWEngine功耗**：深度引擎的功耗模型未实现
4. **CIM读写功耗**：CIM的读写操作能耗参数为0
5. **静态功耗**：当前只考虑动态功耗，未包含漏电等静态功耗
6. **DVFS支持**：未实现动态电压频率调节的功耗模型

## 7. 使用示例

```python
# 创建NPU核心
core = NPUCore(x=0, y=0, top=None)

# 执行一些操作（会自动记录能耗）
# ... 运行仿真 ...

# 计算总能耗
total_energy = core.energy_calculate()
print(f"总能耗: {total_energy} pJ")
print(f"总能耗: {total_energy/1e6} μJ")
```

## 8. 优化建议

基于当前的功耗模型，可以考虑以下优化策略：

### 8.1 数据重用
- 减少DRAM访问（6.4 pJ/byte）
- 优先使用SRAM（0.4 pJ/byte）

### 8.2 计算存储一体化
- 利用CIM减少数据移动
- CIM的MAC运算效率为10 TOPS/W

### 8.3 通信优化
- 减少NoC通信（32 pJ/byte）
- 优化数据布局以减少跨核通信

### 8.4 操作融合
- 减少中间结果的存储
- 合并可以连续执行的操作

## 9. 总结

NPU模拟器的功耗计算系统提供了一个灵活的框架来评估不同硬件配置和算法实现的能效。通过字节级别的操作跟踪和基于实际硬件特性的能耗参数，该系统能够为NPU设计优化提供有价值的指导。未来的工作应该集中在完善未定义的功耗参数、实现静态功耗模型，以及支持更复杂的功耗优化技术如DVFS。