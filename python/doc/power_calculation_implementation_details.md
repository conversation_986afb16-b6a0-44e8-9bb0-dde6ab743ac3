# NPU模拟器功耗计算系统实现细节

## 1. 代码结构分析

### 1.1 相关文件

- `GlobalSettings.py`: 定义所有功耗参数
- `Modules.py`: 实现各硬件模块的功耗计算方法
- `NPUCore.py`: NPU核心类，需要实现总能耗计算

### 1.2 类继承关系

```
ModuleTemp (抽象基类)
├── MemoryMod
│   ├── DRAMBank
│   ├── SRAMBank
│   └── CIMCPage
├── ComputeMod
│   ├── VPU
│   ├── DWEngine
│   └── CIMMACArray
├── ManageMod
├── LoadStoreUnit
├── NoCRouter
└── Ctrl
```

## 2. 功耗计算实现细节

### 2.1 ModuleTemp基类 (Modules.py: 21-55行)

```python
class ModuleTemp(ABC):
    def __init__(self, name, id, parent_core):
        self.name = name
        self.id = id
        self.state = ModState.IDLE
        self.op_energy = {}      # 操作能耗参数字典
        self.record = {}         # 操作记录字典
        self.parent_core = parent_core
        
    @abstractmethod
    def calculate_energy(self):
        pass
```

### 2.2 内存模块功耗实现

#### MemoryMod基类 (Modules.py: 58-76行)
```python
class MemoryMod(ModuleTemp):
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {'Read_Byte': 0.0, 'Write_Byte': 0.0}
        self.record = {'Read_Byte_Num': 0.0, 'Write_Byte_Num': 0.0}
        
    def calculate_energy(self):
        read_energy = self.op_energy['Read_Byte'] * self.record['Read_Byte_Num']
        write_energy = self.op_energy['Write_Byte'] * self.record['Write_Byte_Num']
        return read_energy + write_energy
```

#### DRAMBank实现 (Modules.py: 89-178行)
- 能耗参数初始化（92-93行）：
  ```python
  self.op_energy = {'Read_Byte': GlbSet.DRAMBank_Read_Byte,
                    'Write_Byte': GlbSet.DRAMBank_Write_Byte}
  ```
- 操作记录（100-157行）：在`run_prim()`中根据原语类型记录操作

#### SRAMBank实现 (Modules.py: 181-259行)
- 支持多端口访问
- 在单个周期内可以同时记录多个读写操作

### 2.3 计算模块功耗实现

#### VPU (Modules.py: 283-404行)
```python
def calculate_energy(self):
    add_energy = self.op_energy['Add_Byte'] * self.record['Add_Byte_Num']
    mul_energy = self.op_energy['Mul_Byte'] * self.record['Mul_Byte_Num']
    logic_energy = self.op_energy['Logic_Byte'] * self.record['Logic_Byte_Num']
    return add_energy + mul_energy + logic_energy
```

VPU的操作记录基于：
- 操作类型（加法、乘法、逻辑运算）
- 数据精度（INT、FP16、FP32、BF16）
- 向量长度和并行度

#### CIMMACArray (Modules.py: 481-594行)
- 当前`calculate_energy()`只有pass语句（588-590行）
- 需要实现：
  ```python
  def calculate_energy(self):
      mac_energy = self.op_energy['MAC_Byte'] * self.record['MAC_Byte_Num']
      return mac_energy
  ```

### 2.4 通信模块功耗实现

#### NoCRouter (Modules.py: 723-765行)
```python
def calculate_energy(self):
    comm_energy = self.op_energy['Comm_Byte'] * self.record['Comm_Byte_Num']
    return comm_energy
```

## 3. NPUCore能耗汇总实现

### 3.1 当前状态 (NPUCore.py: 301-302行)
```python
def energy_calculate(self):
    raise NotImplementedError()
```

### 3.2 建议实现

```python
def energy_calculate(self):
    """
    计算NPU核心的总能耗
    """
    total_energy = 0.0
    
    # 遍历所有模块
    for module in self.module_list:
        if hasattr(module, 'calculate_energy'):
            energy = module.calculate_energy()
            if energy is not None:
                total_energy += energy
    
    # 特殊处理CIM相关模块
    # CIM页面
    for page in self.cim_cluster.pages:
        if hasattr(page, 'calculate_energy'):
            energy = page.calculate_energy()
            if energy is not None:
                total_energy += energy
    
    # CIM MAC阵列
    if hasattr(self.cim_cluster.macarray, 'calculate_energy'):
        energy = self.cim_cluster.macarray.calculate_energy()
        if energy is not None:
            total_energy += energy
    
    return total_energy
```

## 4. 操作记录机制详解

### 4.1 DRAM操作记录示例

```python
# TLD (Tensor Load) 操作
if primitive.type == SIMTemp.PrimName.TLD:
    read_bits = primitive.tensor_in1.bits
    read_cycles = math.ceil(read_bits / self.bit_width)
    self.cycle_cost['read'] = read_cycles
    self.record['Read_Byte_Num'] = read_bits // 8  # 记录读取字节数
```

### 4.2 VPU操作记录示例

```python
# 根据操作类型记录
if primitive.vector_op in add_prims:
    # 根据数据类型确定加法器宽度
    if primitive.tensor_in1.type == 'INT':
        adder_width = width_max
    elif primitive.tensor_in1.type == 'FP' and primitive.tensor_in1.width == 16:
        adder_width = 11
    # 记录操作字节数
    self.record['Add_Byte_Num'] += math.ceil(vector_length * adder_width / 8)
```

### 4.3 CIM MAC操作记录

```python
# GEMM操作
if primitive.type == SIMTemp.PrimName.GEMM:
    m, p, n = primitive.tensor_in1.layout['dim1'], \
              primitive.tensor_in1.layout['dim0'], \
              primitive.tensor_out.layout['dim0']
    mac_num = 2 * m * p * n  # MAC操作数
    self.record['MAC_Byte_Num'] += math.ceil(
        mac_num * (primitive.tensor_in1.mac_width() / 8) * 
        (primitive.conv_settings.mac_width() / 8)
    )
```

## 5. 功耗参数详解 (GlobalSettings.py: 97-119行)

### 5.1 已定义参数

```python
# DRAM功耗：0.8 pJ/bit
DRAMBank_Read_Byte = 0.8 * 8   # 6.4 pJ/byte
DRAMBank_Write_Byte = 0.8 * 8  # 6.4 pJ/byte

# SRAM功耗：50 fJ/bit  
SRAMBank_Read_Byte = 0.05 * 8   # 0.4 pJ/byte
SRAMBank_Write_Byte = 0.05 * 8  # 0.4 pJ/byte

# CIM MAC功耗：基于10 TOPS/W效率
CIMC_MAC_Byte = 1/10  # 0.1 pJ/byte

# NoC功耗：4 pJ/bit per mesh hop
NoC_Comm_Byte = 4 * 8  # 32 pJ/byte
```

### 5.2 待定义参数（设为0）

```python
VPU_Add_Byte = 0.00     # VPU加法
VPU_Mul_Byte = 0.00     # VPU乘法  
VPU_Logic_Byte = 0.00   # VPU逻辑运算
DWEngine_Add_Byte = 0.00  # 深度引擎加法
DWEngine_Mul_Byte = 0.00  # 深度引擎乘法
CIMC_Read_Byte = 0.00   # CIM读取
CIMC_Write_Byte = 0.00  # CIM写入
```

## 6. 实现检查清单

### 6.1 必须实现
- [ ] NPUCore.energy_calculate() - 第301行需要实现
- [ ] CIMMACArray.calculate_energy() - 第588行需要实现
- [ ] CIMCluster.calculate_energy() - 第655行需要实现

### 6.2 需要完善
- [ ] VPU功耗参数（当前为0）
- [ ] DWEngine功耗模型
- [ ] CIM读写功耗参数
- [ ] 其他控制模块的calculate_energy()实现

### 6.3 可选增强
- [ ] 静态功耗（漏电）模型
- [ ] 温度相关功耗模型
- [ ] DVFS功耗模型
- [ ] 功耗报告生成功能

## 7. 测试验证

建议创建测试脚本验证功耗计算：

```python
# 测试DRAM功耗
dram = core.dram_banks[0]
dram.record['Read_Byte_Num'] = 1024
energy = dram.calculate_energy()
assert energy == 1024 * 6.4  # pJ

# 测试总能耗计算
total_energy = core.energy_calculate()
# 验证是否等于所有模块能耗之和
```

## 8. 注意事项

1. **单位一致性**：所有能耗单位统一为picojoules (pJ)
2. **记录清零**：每个原语执行前需要清零记录
3. **双重计算**：避免CIM相关模块的重复计算
4. **精度处理**：注意浮点数精度对能耗计算的影响