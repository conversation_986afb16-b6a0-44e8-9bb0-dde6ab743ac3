import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
from enum import Enum
import matplotlib.style as mplstyle


class ProcessingUnit(Enum):
    """Enumeration for different processing unit types."""
    LDST = 0
    TP = 1
    VP = 2
    TM = 3
    NOC = 4


@dataclass
class VisualizationConfig:
    colors  : Dict[str, str] = None
    figsize : Tuple[float, float] = (12, 2)
    dpi     : int = 600
    tick_interval   : int = 1000
    label_interval  : int = 5000
    save_path       : str = './logs/'
    
    def __post_init__(self):
        if self.colors is None:
            self.colors = {
                'idle'  : '#FFFFFF',
                'ldst'  : '#F08080',
                'tp'    : '#8FBC8F',
                'vp'    : '#87CEEB',
                'tm'    : '#F4A460',
                'noc'   : '#DDA0DD'
            }


class TimelineVisualizer:
    """A class for creating hardware processing unit timeline visualizations."""
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        """
        Initialize the visualizer with configuration.
        
        Args:
            config: Visualization configuration object
        """
        self.config = config or VisualizationConfig()
        # Set matplotlib parameters for better aesthetics without relying on specific styles
        plt.rcParams.update({
            'figure.facecolor'  : 'white',
            'axes.facecolor'    : 'white',
            'axes.edgecolor'    : '#CCCCCC',
            'axes.linewidth'    : 0.8,
            'grid.alpha'        : 0.3,
            'grid.linestyle'    : '--',
            'grid.color'        : '#E0E0E0'
        })
        
    def _create_timeline_array(self, 
                              total_cycles: int,
                              cycle_ranges: Dict[ProcessingUnit, List[Tuple[int, int]]]) -> np.ndarray:

        timeline = np.zeros((len(ProcessingUnit), int(total_cycles)), dtype=np.uint8)
        
        for unit, ranges in cycle_ranges.items():
            unit_idx = unit.value
            timeline[unit_idx, 0] = unit_idx + 1
            
            for start, end in ranges:
                timeline[unit_idx, start:end + 1] = unit_idx + 1
                
        return timeline
    
    def _setup_plot_aesthetics(self, ax: plt.Axes, total_cycles: int, title: str):
        unit_labels = ['LD/ST', 'TP', 'VP', 'TM', 'NOC']
        ax.set_yticks(range(len(ProcessingUnit)))
        ax.set_yticklabels(unit_labels, fontsize=11, fontweight='medium')
        
        tick_positions = range(0, total_cycles, self.config.tick_interval)
        ax.set_xticks(tick_positions)
        
        tick_labels = [
            f'{i // 1000:.0f}' if i % self.config.label_interval == 0 else ''
            for i in tick_positions
        ]
        ax.set_xticklabels(tick_labels, fontsize=10)
        
        ax.set_xlabel('Cycle (k)', fontsize=12, fontweight='medium')
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
        
        ax.grid(True, axis='x', alpha=0.3, linestyle='--')
        
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
    def _create_legend(self, ax: plt.Axes):
        legend_elements = [
            mpatches.Patch(color=self.config.colors['ldst'] , label='LSU'),
            mpatches.Patch(color=self.config.colors['tp']   , label='MPU'),
            mpatches.Patch(color=self.config.colors['vp']   , label='VPU'),
            mpatches.Patch(color=self.config.colors['tm']   , label='TMU'),
            mpatches.Patch(color=self.config.colors['noc']  , label='NOC'),
            mpatches.Patch(facecolor=self.config.colors['idle'] , label='Idle', edgecolor='black')
        ]
        
        ax.legend(
            handles=legend_elements,
            loc='center left',
            bbox_to_anchor=(1.02, 0.5),
            title='Processing Units',
            title_fontsize=12,
            fontsize=11,
            frameon=True,
            fancybox=True,
            shadow=True
        )
        
    def visualize(self,
                  total_cycles  : int,
                  visual_title  : str,
                  ldst_cycles   : List[Tuple[int, int]] = None,
                  tp_cycles     : List[Tuple[int, int]] = None,
                  vp_cycles     : List[Tuple[int, int]] = None,
                  tm_cycles     : List[Tuple[int, int]] = None,
                  noc_cycles    : List[Tuple[int, int]] = None,
                  save          : bool = True,
                  figsize       : Optional[Tuple[float, float]] = None) -> Optional[plt.Figure]:
        """
        Create a timeline visualization of processing unit utilization.
        
        Args:
            total_cycles: Total number of simulation cycles
            visual_title: Title for the visualization
            ldst_cycles: Active cycle ranges for Load/Store unit
            tp_cycles: Active cycle ranges for Tensor Processing unit
            vp_cycles: Active cycle ranges for Vector Processing unit
            tm_cycles: Active cycle ranges for Tensor Manipulation unit
            noc_cycles: Active cycle ranges for Network on Chip
            save: Whether to save the figure to disk
            figsize: Custom figure size (overrides config)
            
        Returns:
            Figure object if not saved, None otherwise
        """
        ldst_cycles = ldst_cycles   or []
        tp_cycles   = tp_cycles     or []
        vp_cycles   = vp_cycles     or []
        tm_cycles   = tm_cycles     or []
        noc_cycles  = noc_cycles    or []
        
        cycle_ranges = {
            ProcessingUnit.LDST: ldst_cycles,
            ProcessingUnit.TP: tp_cycles,
            ProcessingUnit.VP: vp_cycles,
            ProcessingUnit.TM: tm_cycles,
            ProcessingUnit.NOC: noc_cycles
        }
        
        timeline = self._create_timeline_array(total_cycles, cycle_ranges)
        
        fig_size = figsize or self.config.figsize
        fig, ax = plt.subplots(figsize=(fig_size[0], fig_size[1] * 1.2))
        
        color_list = [
            self.config.colors['idle'],
            self.config.colors['ldst'],
            self.config.colors['tp'],
            self.config.colors['vp'],
            self.config.colors['tm'],
            self.config.colors['noc']
        ]
        cmap = plt.cm.colors.ListedColormap(color_list)
        
        im = ax.imshow(
            timeline,
            aspect='auto',
            cmap=cmap,
            interpolation='nearest',
            origin='upper',
            alpha=0.9
        )
        
        self._setup_plot_aesthetics(ax, total_cycles, visual_title)
        self._create_legend(ax)
        
        plt.tight_layout()
        
        if save:
            save_path = f"{self.config.save_path}{visual_title}.png"
            plt.savefig(
                save_path,
                dpi=self.config.dpi,
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none'
            )
            plt.close(fig)
            return None
        else:
            return fig


def visualize(total_cycles: int,
              visual_title: str,
              ldst_cycles: List[Tuple[int, int]] = None,
              tp_cycles: List[Tuple[int, int]] = None,
              vp_cycles: List[Tuple[int, int]] = None,
              tm_cycles: List[Tuple[int, int]] = None,
              noc_cycles: List[Tuple[int, int]] = None,
              save: bool = True,
              figsize: Tuple[float, float] = (12, 2)) -> Optional[plt.Figure]:

    config = VisualizationConfig(figsize=figsize)
    visualizer = TimelineVisualizer(config)
    
    return visualizer.visualize(
        total_cycles=total_cycles,
        visual_title=visual_title,
        ldst_cycles=ldst_cycles,
        tp_cycles=tp_cycles,
        vp_cycles=vp_cycles,
        tm_cycles=tm_cycles,
        noc_cycles=noc_cycles,
        save=save,
        figsize=figsize
    )


