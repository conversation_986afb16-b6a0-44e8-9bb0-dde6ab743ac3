# * * * * * * * * * * * * * * * * #
# Define basic information of Primitives
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #

import warnings
from enum import Enum, auto
import math
import numpy as np
import copy
import logging
import os

import sys
import GlobalSettings as GlbSet
import Modules
# import GoldenModel as GldMod
import Utility as Utils
import inspect


import sim_backend_torch as sim_backend


# Configure logging for NOC transfers if not already configured
def configure_noc_logging():
    """Configure logging for NOC transfers if not already configured"""
    # Check if NOC logging is enabled via environment variable or config
    noc_logging_enabled = os.getenv('NOC_LOGGING_ENABLED', 'true').lower() in ('true', '1', 'yes', 'on')
    
    if noc_logging_enabled and not logging.getLogger().handlers:
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("logs/noc_transfers.log", mode="a"),
                logging.StreamHandler()
            ]
        )
    elif not noc_logging_enabled:
        # Disable NOC logging by setting a null handler
        logging.getLogger().addHandler(logging.NullHandler())
        logging.getLogger().setLevel(logging.CRITICAL)

# Initialize logging
configure_noc_logging()



class PrimName(Enum):

    # Operation Primitives
    TLD = auto()        # ✔
    TST = auto()        # ✔
    VV_V = auto()       # ✔
    VS_V = auto()       # ✔
    V_S = auto()
    V_V = auto()        # ✔
    BC = auto()         # ✔
    MOV = auto()        # ✔
    TRANS = auto()      # ✔
    GEMV = auto()       # ✔
    GEMM = auto()       # ✔
    NOC = auto()        # ✔

    # Control Primitives
    SYNC = auto()       # ✔     for RV
    GROUP_MASK = auto() # ✔     for RV
    SWCIMC = auto()     # ✔
    RD_LMEM = auto()
    WR_LMEM = auto()

    UNKNOWN = auto()

    # NoC primitives - activated for implementation
    NOC_FENCE = auto()  
    NOC_SRC = auto()    # Source core primitive
    NOC_DEST = auto()   # Destination core primitive
    
    # Other primitives
    TLD_TRANS = auto() 


class DataFormat(Enum):
    INT4 = auto()
    INT8 = auto()
    INT16 = auto()
    INT32 = auto()
    FP16 = auto()
    FP32 = auto()
    BF16 = auto()
    BBF16 = auto()
    UNKNOWN = auto()


class VectorProcess(Enum):
    ADD = auto()
    SUB = auto()
    RSUB = auto()
    MAX = auto()
    MIN = auto()
    COMPARE = auto()
    LOGICAL = auto()
    SHIFT = auto()
    MUL = auto()
    LEFT_SHIFT = auto()
    RIGHT_SHIFT = auto()
    FP_INT = auto()
    INT_FP = auto()
    FP_FP = auto()
    EQUAL = auto()
    NOT_EQUAL = auto()
    LESS = auto()
    LESS_OR_EQUAL = auto()
    GREATER = auto()
    GREATER_OR_EQUAL = auto()
    AND = auto()
    OR = auto()
    XOR = auto()
    UNKNOWN = auto()

    # align to Mosim
    RIGHT_SHIFT_FLOOR = auto()
    RIGHT_SHIFT_CEIL  = auto()
    RIGHT_SHIFT_ROUND = auto()


class TensorInfo:
    def __init__(self, parent_prim):
        self.byte_base = None
        # self.byte_stride = None             # current: (stride_dim2, stride_dim1, stride_dim0b); previous: dim1 stride
        # self.size = None                    # (dim2, dim1, dim0)
        # 下面这一段又改回遵从最新的mosim代码
        self.byte_stride = None             # current: (stride_dim1, stride_dim2)
        self.size = None                    # (dim0, dim1, dim2)
        self.type = None                    # INT/FP/BF/BBF
        self.width = -1                     # 4/8/16/32
        self.layout = {'dim0a': 0, 'dim0b': 0, 'dim1': 0, 'dim2': 0, 'dim0': 0, 'rem': 0}
        self.bits = 0                       # total bits of this tensor
        self.memory = None                  # memory module for this tensor
        self.byte_base_list = []            # for consecutive CIM page access
        self.parent_prim = parent_prim
        self.strides = [-1, -1, -1]         # [stride_dim2, stride_dim1, stride_dim0b]
        self.size_gldres = [-1, -1, -1]

    def layout_translate(self):             # translate when RV sending
        if self.byte_base is not None:
            # translate size, type and width to layout
            # also calculate the bits of tensor during read/write opeations
            self.layout['dim0'] = self.size[0]
            self.layout['dim1'] = self.size[1]
            self.layout['dim2'] = self.size[2]
            # dim0a and dim0b
            assert GlbSet.LMEM_WD % self.width == 0, 'width:' + str(self.width)
            self.layout['dim0a'] = GlbSet.LMEM_WD // self.width
            self.layout['dim0b'] = math.ceil(self.size[0] / self.layout['dim0a'])
            self.bits = self.size[1] * self.size[2] * self.layout['dim0b'] * GlbSet.LMEM_WD
            self.size_gldres = self.size[::-1]
        if self.byte_stride is not None:
            self.strides[0] = self.byte_stride[1] // GlbSet.LMEM_ADDR_WD
            self.strides[1] = self.byte_stride[0] // GlbSet.LMEM_ADDR_WD
            self.strides[2] = 1

    def mac_width(self):

        if self.type == 'BBF':
            return 8
        else:
            return self.width

    def dummy_tensor_gen(self):
        # Generate a dummy tensor filled with zeros, with the shape referenced from self.size
        if self.size_gldres is not None:
            return np.zeros(self.size_gldres, dtype=self.find_dtype())
        else:
            raise ValueError("self.size is not defined. Please set self.size to a valid tuple.")

    def find_datatype(self):
        return self.type + str(self.width)

    def find_dtype(self):
        datatype = self.find_datatype()
        return Utils.datatype2dtype(datatype)

    # Previous version
    def mac_width_v0p0(self):

        # +2: sign and hidden 1 for FP/BF data
        if self.type == 'INT':
            # warnings.warn('Asking mantissa width for INT tensor')
            return self.width
        elif self.type == 'FP':
            if self.width == 16:
                return GlbSet.FP16_Mantissa_Width + 2
            elif self.width == 32:
                return GlbSet.FP32_Mantissa_Width + 2
        elif self.type == 'BF' and self.width == 16:
            return GlbSet.BF16_Mantissa_Width + 2
        elif self.type == 'BBF' and self.width == 16:
            return GlbSet.BBF16_Mantissa_Width + 2
        else:
            raise Exception('Illegal data format when trying to return mantissa width !' +
                            ' (Type, Width) are (' + str(self.type) + ', ' + str(self.width) + ')\n'
                            + str(self.parent_prim.details()))

    def __repr__(self):
        if self.byte_base is not None:
            return f'byte_base: {hex(self.byte_base)}, byte_stride: {self.byte_stride}, ' \
                   f'size: {self.size}, type: {self.type}, width: {self.width}'
        else:
            return f'This is an empty tensor!'


def TensorInfo2Descriptor(tensor_info):
    return sim_backend.TensorDescriptor(
        addr=tensor_info.byte_base,
        shape=(tensor_info.size[2], tensor_info.size[1], tensor_info.size[0]),
        byte_stride=(tensor_info.byte_stride[1], tensor_info.byte_stride[0], 32),
        dtype=tensor_info.find_datatype()
    )                

def VPOP2STR(vpop: VectorProcess) -> str:
    _map = {
        VectorProcess.ADD              : "add" ,
        VectorProcess.SUB              : "sub" ,
        VectorProcess.MAX              : "max" ,
        VectorProcess.MIN              : "min" ,
        VectorProcess.MUL              : "mul" ,
        VectorProcess.EQUAL            : "eq"  ,
        VectorProcess.NOT_EQUAL        : "ne"  ,
        VectorProcess.GREATER          : "gt"  ,
        VectorProcess.LESS             : "lt"  ,
        VectorProcess.GREATER_OR_EQUAL : "ge"  ,
        VectorProcess.LESS_OR_EQUAL    : "le"  ,
    }

    if vpop not in _map:
        raise ValueError(f"Unsupported arithmetic operation: {vpop}")
    return _map[vpop]


class ConvInfo:
    def __init__(self, parent_prim):
        self.byte_base_wt = None
        self.type = None
        self.width = None
        self.accu = None
        self.act = None
        self.shift = None
        self.cfg_k_x = None
        self.cfg_k_y = None
        self.slide_x = None
        self.slide_y = None
        self.pad_w = None
        self.pwd_n = None
        self.pad_val = None

        self.memory = None
        self.parent_prim = parent_prim

    def mac_width(self):
        # +2: sign and hidden 1 for FP/BF data
        if self.type == 'INT':
            # warnings.warn('Asking mantissa width for INT tensor')
            return self.width
        elif self.type == 'FP':
            if self.width == 16:
                return GlbSet.FP16_Mantissa_Width + 2
            elif self.width == 32:
                return GlbSet.FP32_Mantissa_Width + 2
        elif self.type == 'BF' and self.width == 16:
            return GlbSet.BF16_Mantissa_Width + 2
        elif self.type == 'BBF' and self.width == 16:
            return GlbSet.BBF16_Mantissa_Width + 2
        else:
            raise Exception('Illegal data format when trying to return mantissa width !' +
                            ' (Type, Width) are (' + str(self.type) + ', ' + str(self.width) + ')\n'
                            + str(self.parent_prim.details()))

    def find_datatype(self):
        return self.type + str(self.width)

    def find_dtype(self):
        datatype = self.find_datatype()
        return Utils.datatype2dtype(datatype)

    def __repr__(self):
        if self.byte_base_wt is not None:
            return f'type: {self.type}, width: {self.width}, ' \
                f'(Kx, Ky): {self.cfg_k_x, self.cfg_k_y}, (Sx, Sy): {self.slide_x, self.slide_y}'
        else:
            return f'Empty Conv Info!'


class NoCInfo:
    def __init__(self, parent_prim):
        self.src_addr = None
        self.dst_addr = None
        self.src_group = None
        self.src_id = None
        self.dst_group = None
        self.dst_id = None
        self.src_memory = None
        self.src_core = None
        self.dst_memory = None
        self.dst_core = None
        self.parent_prim = parent_prim
        self.executable_label = 0           # 0: not known, 1: executable, 2: not executable
        
        # NoC pairing support
        self.partner_primitive = None       # Link to paired primitive
        
        # New fields for transfer support
        self.transfer_size = 0              # Size in bytes
        self.data_buffer = None             # numpy array for data
        self.route_established = False      # NoC route status
        self.pending_write = None           # Store pending write info for execution order handling

    def first_update_core(self):
        if self.src_core.global_id < self.dst_core.global_id:
            return self.src_core.global_id
        else:
            return self.dst_core.global_id
    
    def __deepcopy__(self, memo):
        # Create a new instance
        cls = self.__class__
        result = cls.__new__(cls)
        
        # List of attributes to skip during deepcopy
        skip_attrs = {'partner_primitive', 'parent_prim', 'src_core', 'dst_core', 'src_memory', 'dst_memory', 'top_simulator'}
        
        # Copy all attributes except those in skip list
        for k, v in self.__dict__.items():
            if k in skip_attrs:
                # Don't copy these references during deepcopy
                setattr(result, k, None)
            else:
                setattr(result, k, copy.deepcopy(v, memo))
        
        return result


class Primitive:
    def __init__(self, prim_id=-1):
        self.prim_id = prim_id
        self.type = PrimName.UNKNOWN
        self.tensor_in1 = TensorInfo(parent_prim=self)          # Input for CONV, GEMV, GEMM
        self.tensor_in2 = TensorInfo(parent_prim=self)          # Weight for CONV, GEMV, GEMM
        self.tensor_out = TensorInfo(parent_prim=self)          # Output for CONV, GEMV, GEMM
        self.tensor_orig = TensorInfo(parent_prim=self)         # Origin for CONV, GEMV, GEMM
        self.vector_op = VectorProcess.UNKNOWN                  # Specific Vector Operation
        self.module_use = []
        self.begin_cycle = None
        self.end_cycle = None
        self.latency = 0                                        # set by XX Unit
        self.loop_num = None                                    # equals dim1 × dim2 of target tensor
        self.conv_settings = ConvInfo(parent_prim=self)
        self.noc_settings = NoCInfo(parent_prim=self)
        self.cimc_mode = None                                   # NPU Control

        self.scalars = []
        
        # NoC pairing support
        self.dispatch_cycle = 0                                 # When primitive was dispatched

        # Global Control
        self.npu_group = None
        self.npu_mask = None

        # Instruction tracking for return values
        self.inst_id = None          # Instruction ID for tracking return values
        self.inst_type = None        # 'nice' or 'vnice'
        self.original_inst = None    # Original instruction word

    def set_end_cycle(self, module_cycle_cost):
        unpipeline_primitives = [PrimName.GEMV]
        if self.type in unpipeline_primitives:
            if self.end_cycle:
                self.end_cycle += module_cycle_cost
            else:
                self.end_cycle = self.begin_cycle + module_cycle_cost
        else:
            if self.end_cycle:
                self.end_cycle = max(self.end_cycle, self.begin_cycle + module_cycle_cost + self.latency)
            else:
                self.end_cycle = self.begin_cycle + module_cycle_cost + self.latency

    def set_latency(self, latency):
        self.latency += latency
        if self.end_cycle is not None:
            self.end_cycle += latency
        else:
            self.end_cycle = latency

    def layout_translate(self):
        self.tensor_in1.layout_translate()
        self.tensor_in2.layout_translate()
        self.tensor_out.layout_translate()
        self.tensor_orig.layout_translate()
        if self.type == PrimName.NOC:
            self.tensor_in1.byte_base = self.noc_settings.src_addr
            self.tensor_in1.layout_translate()
            self.tensor_out.byte_base = self.noc_settings.dst_addr
            self.tensor_out.layout_translate()
            self.tensor_in1.byte_base = None
            self.tensor_out.byte_base = None
    

    def golden_run(self, core_id, golden_vm_system: sim_backend.GOLDEN_VM_SYSTEM):
        ret = -1
        match self.type:
            case PrimName.TLD:
                desc_in  = TensorInfo2Descriptor(self.tensor_in1)
                desc_out = TensorInfo2Descriptor(self.tensor_out) 
                golden_vm_system.operation_load(core_id, desc_in, desc_out)    
            case PrimName.TST:
                desc_in  = TensorInfo2Descriptor(self.tensor_in1)
                desc_out = TensorInfo2Descriptor(self.tensor_out) 
                golden_vm_system.operation_store(core_id, desc_in, desc_out)
            case PrimName.MOV:
                desc_in  = TensorInfo2Descriptor(self.tensor_in1)
                desc_out = TensorInfo2Descriptor(self.tensor_out) 
                golden_vm_system.operation_move(core_id, desc_in, desc_out)               
            case PrimName.TRANS:
                desc_in  = TensorInfo2Descriptor(self.tensor_in1)
                desc_out = TensorInfo2Descriptor(self.tensor_out) 
                golden_vm_system.operation_transpose(core_id, desc_in, desc_out)
            case PrimName.GEMM | PrimName.GEMV:
                desc_in1 = TensorInfo2Descriptor(self.tensor_in1)
                desc_out = TensorInfo2Descriptor(self.tensor_out)
                desc_orig= TensorInfo2Descriptor(self.tensor_orig)
                desc_wt  = sim_backend.TensorDescriptor(
                    addr=self.conv_settings.byte_base_wt,
                    shape=(1, desc_in1.dim0, desc_out.dim0),
                    # byte_stride=(8192, 32, 32), # cimc_mode = 256 * 256bit
                    byte_stride=(8192, 64,32), # cimc_mode = 128 * 512bit
                    dtype=self.conv_settings.type + str(self.conv_settings.width)
                )
                accumulate = (self.conv_settings.accu == 1)
                golden_vm_system.operation_gemm(core_id, desc_in1, desc_wt, desc_orig, desc_out, accumulate)

            case PrimName.VV_V:
                desc_in1 = TensorInfo2Descriptor(self.tensor_in1)
                desc_in2 = TensorInfo2Descriptor(self.tensor_in2)
                desc_out = TensorInfo2Descriptor(self.tensor_out)
                golden_vm_system.operation_vvv(core_id, desc_in1, desc_in2, desc_out, VPOP2STR(self.vector_op))

            case PrimName.VS_V:
                desc_in1 = TensorInfo2Descriptor(self.tensor_in1)
                desc_out = TensorInfo2Descriptor(self.tensor_out)
                golden_vm_system.operation_vsv(core_id, desc_in1, desc_out, self.scalars[core_id % 4], self.tensor_in2.find_datatype(), VPOP2STR(self.vector_op))

            case PrimName.V_S:
                desc_in1 = TensorInfo2Descriptor(self.tensor_in1)
                ret = golden_vm_system.operation_vs(core_id, desc_in1, VPOP2STR(self.vector_op))
            case PrimName.V_V:
                desc_in1 = TensorInfo2Descriptor(self.tensor_in1)
                desc_out = TensorInfo2Descriptor(self.tensor_out)
                golden_vm_system.operation_vv(core_id, desc_in1, desc_out)

            case PrimName.NOC:
                raise NotImplementedError("Use NOC_SRC and NOC_DEST instead")
            
            case PrimName.NOC_SRC:
                # Simple NOC_SRC: Read source data and store in global buffer
                desc_in = TensorInfo2Descriptor(self.tensor_in1)
                data = golden_vm_system.read_tensor(core_id, desc_in)

                # Map core_id to coordinate
                core_to_cord = {
                    0: 0x0000, 1: 0x0001, 2: 0x0002, 3: 0x0003,
                    4: 0x0100, 5: 0x0101, 6: 0x0102, 7: 0x0103,
                    8: 0x0200, 9: 0x0201, 10: 0x0202, 11: 0x0203,
                    12: 0x0300, 13: 0x0301, 14: 0x0302, 15: 0x0303
                }
                
                src_coord = core_to_cord.get(core_id, core_id)
                
                # Storage key using both source and destination coordinates
                # This makes it clear where the data is from and where it's going
                storage_key = f"noc_from_{src_coord}_to_{self.noc_settings.dst_id}"

                # Store data in golden_vm_system for the destination core to retrieve
                if not hasattr(golden_vm_system, 'noc_transfer_buffer'):
                    golden_vm_system.noc_transfer_buffer = {}
                golden_vm_system.noc_transfer_buffer[storage_key] = data

                # Log NOC transfer details
                src_addr = f"0x{self.tensor_in1.byte_base:08X}" if self.tensor_in1.byte_base is not None else "None"
                logging.info(f"[NOC_SRC] Transfer from core_{core_id} (coord=0x{src_coord:04X}) to dst_id=0x{self.noc_settings.dst_id:04X}")
                logging.info(f"[NOC_SRC] Source address: {src_addr}")
                logging.info(f"[NOC_SRC] tensor_in shape: {self.tensor_in1.size}, stride: {self.tensor_in1.byte_stride}")
                logging.info(f"[NOC_SRC] Data shape: {data.shape}, dtype: {data.dtype}")
                logging.info(f"[NOC_SRC] Storage key: {storage_key}")
                logging.info(f"[NOC_SRC] Data sample (first few elements): {data.flatten()[:min(8, data.numel())]}")

                return data
            
            case PrimName.NOC_DEST:
                # Simple NOC_DEST: Retrieve data from global buffer and write to destination
                desc_out = TensorInfo2Descriptor(self.tensor_out)

                # Map core_id to coordinate for verification
                core_to_cord = {
                    0: 0x0000, 1: 0x0001, 2: 0x0002, 3: 0x0003,
                    4: 0x0100, 5: 0x0101, 6: 0x0102, 7: 0x0103,
                    8: 0x0200, 9: 0x0201, 10: 0x0202, 11: 0x0203,
                    12: 0x0300, 13: 0x0301, 14: 0x0302, 15: 0x0303
                }
                
                my_coord = core_to_cord.get(core_id, core_id)
                
                # Build expected key using src_id and my coordinate
                # The key format should match what NOC_SRC created: "noc_from_X_to_Y"
                # src_id is the source coordinate, my_coord is the destination
                expected_key = f"noc_from_{self.noc_settings.src_id}_to_{my_coord}"
                
                data = None
                storage_key = None
                
                if hasattr(golden_vm_system, 'noc_transfer_buffer'):
                    # First try to find exact match
                    if expected_key in golden_vm_system.noc_transfer_buffer:
                        storage_key = expected_key
                        data = golden_vm_system.noc_transfer_buffer[storage_key]
                        del golden_vm_system.noc_transfer_buffer[storage_key]
                    elif golden_vm_system.noc_transfer_buffer:
                        # If exact match not found but buffer not empty,
                        # take the first one (for paired execution compatibility)
                        storage_key = next(iter(golden_vm_system.noc_transfer_buffer.keys()))
                        data = golden_vm_system.noc_transfer_buffer[storage_key]
                        del golden_vm_system.noc_transfer_buffer[storage_key]
                        logging.warning(f"[NOC_DEST] Expected key {expected_key} not found, using {storage_key}")
                
                if data is not None:
                    # Log NOC transfer details
                    dest_addr = f"0x{self.tensor_out.byte_base:08X}" if self.tensor_out.byte_base is not None else "None"
                    logging.info(f"[NOC_DEST] Receiving data at core_{core_id} (coord=0x{my_coord:04X}) from src_id=0x{self.noc_settings.src_id:04X}")
                    logging.info(f"[NOC_DEST] Destination address: {dest_addr}")
                    logging.info(f"[NOC_DEST] tensor_dest shape: {self.tensor_out.size}, stride: {self.tensor_out.byte_stride}")
                    logging.info(f"[NOC_DEST] Received data shape: {data.shape}, dtype: {data.dtype}")
                    logging.info(f"[NOC_DEST] Storage key used: {storage_key}")
                    logging.info(f"[NOC_DEST] Data sample (first few elements): {data.flatten()[:min(8, data.numel())]}")

                    # Write tensor to destination
                    golden_vm_system.write_tensor(core_id, data, desc_out)

                    logging.info(f"[NOC_DEST] Transfer completed successfully")
                    return True
                else:
                    logging.warning(f"[NOC_DEST] No data found for core_{core_id} (coord=0x{my_coord:04X})")
                    logging.warning(f"[NOC_DEST] Expected key: {expected_key} (src_id=0x{self.noc_settings.src_id:04X})")
                    if hasattr(golden_vm_system, 'noc_transfer_buffer'):
                        available_keys = list(golden_vm_system.noc_transfer_buffer.keys())
                        if available_keys:
                            logging.warning(f"[NOC_DEST] Available storage keys: {available_keys}")
                        else:
                            logging.warning(f"[NOC_DEST] Buffer is empty")
                    else:
                        logging.warning(f"[NOC_DEST] No noc_transfer_buffer found in golden_vm_system")
                    return False
            
            case PrimName.NOC_FENCE:
                # Simple fence - no validation needed in golden model
                return True

            case _:
                raise ValueError(f"Undefined Prime type {self.type}")

        return ret

    # def golden_run_deprecated(self):
    #     if self.type in [PrimName.TLD, PrimName.TST, PrimName.BC, PrimName.MOV, PrimName.TRANS]:
    #         if self.type == PrimName.BC:
    #             # 向量寄存器？标量寄存器？
    #             raise NotImplementedError()
    #         else:
    #             # Read tensor_in1, Write to tensor_out
    #             tensor_data_dummy = self.tensor_in1.dummy_tensor_gen()
    #             tensor_data = GldMod.buffer2tensor(tensor=tensor_data_dummy,
    #                                                offset=self.tensor_in1.byte_base - self.tensor_in1.memory.address_space['Addr_Begin'],
    #                                                strides=self.tensor_in1.strides,
    #                                                datatype=self.tensor_in1.find_datatype(),
    #                                                buffer=self.tensor_in1.memory.buffer)
    #             # print('aaa', 'in golden_run', tensor_data)
    #             # input()
    #             # 进行可能的处理
    #             if self.type == PrimName.TRANS:
    #                 tensor_data = tensor_data.transpose(0, 2, 1)
    #             # 回存
    #             if isinstance(self.tensor_out.memory, Modules.CIMCPage):
    #                 matrix_data_exp, matrix_data_manti = (
    #                     GldMod.matrix_align(mat_np=tensor_data, manti_datatype=self.tensor_out.find_datatype())
    #                 )
    #                 # save exp
    #                 self.tensor_out.memory.exp_register = matrix_data_exp
                    
    #                 print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
    #                 print("matrix_data_exp ", matrix_data_exp.shape)
    #                 print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
    #                 print("matrix_data_manti ", matrix_data_manti.shape)

    #                 # save manti
    #                 matrix_data_manti = np.expand_dims(matrix_data_manti, axis=0)
    #                 GldMod.tensor2buffer(tensor=matrix_data_manti,
    #                                      offset=self.tensor_out.byte_base - self.tensor_out.memory.address_space['Addr_Begin'],
    #                                      strides=self.tensor_out.strides,
    #                                      datatype=self.tensor_out.find_datatype(),
    #                                      buffer=self.tensor_out.memory.buffer)
    #             else:
    #                 GldMod.tensor2buffer(tensor=tensor_data,
    #                                      offset=self.tensor_out.byte_base - self.tensor_out.memory.address_space['Addr_Begin'],
    #                                      strides=self.tensor_out.strides,
    #                                      datatype=self.tensor_out.find_datatype(),
    #                                      buffer=self.tensor_out.memory.buffer)
    #     elif self.type in [PrimName.GEMV, PrimName.GEMM]:
    #         # First, get input tensor from Spad
    #         input_tensor_data = self.tensor_in1.dummy_tensor_gen()
    #         GldMod.buffer2tensor(tensor=input_tensor_data,
    #                              offset=self.tensor_in1.byte_base,
    #                              strides=self.tensor_in1.strides,
    #                              datatype=self.tensor_in1.find_datatype(),
    #                              buffer=self.tensor_in1.memory.buffer)
    #         # Second, get weight tensor from CIM
    #         # !! Deduce the shape of the weight based on the shapes of the input and output tensors.
    #         weight_shape = [
    #             1,                              # dim2 
    #             self.tensor_in1.layout['dim0'], # dim1
    #             self.tensor_out.layout['dim0']  # dim0
    #         ]

    #         dim0a_wt = 256 // self.conv_settings.width
    #         dim0b_wt = (weight_shape[2] + dim0a_wt - 1) // dim0a_wt

    #         weight_strides = [
    #             dim0b_wt * weight_shape[1],
    #             dim0b_wt, 
    #             1
    #         ]

    #         weight_tensor_data_manti = np.zeros(weight_shape, dtype=self.conv_settings.find_dtype())
    #         weight_tensor_data_manti = GldMod.buffer2tensor(tensor=weight_tensor_data_manti,
    #                              offset=0,
    #                              strides=weight_strides,
    #                              datatype=self.conv_settings.find_datatype(),
    #                              buffer=self.conv_settings.memory.buffer
    #         )


    #         weight_tensor_data_exp = self.conv_settings.memory.exp_register

    #         weight_tensor_data = GldMod.restore_aligned_matrix(mat_exp_max=weight_tensor_data_exp,
    #                                                            mat_mantissa=weight_tensor_data_manti,
    #                                                            manti_datatype=self.conv_settings.find_datatype(),
    #                                                            restore_datatype='FP16')
    #         # Third, mat multiplication
    #         if input_tensor_data.ndim == 3:
    #             input_tensor_data = input_tensor_data.squeeze(axis=0)
    #         if weight_tensor_data.ndim == 3:
    #             weight_tensor_data = weight_tensor_data.squeeze(axis=0)

    #         output_tensor_data = input_tensor_data @ weight_tensor_data

    #         print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
    #         print("input_tensor_data ", input_tensor_data.shape)
    #         print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
    #         print("weight_tensor_data ", weight_tensor_data.shape)
    #         print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
    #         print("output_tensor_data ", output_tensor_data.shape)

    #         output_tensor_data = np.expand_dims(output_tensor_data, axis=0)

    #         # Forth, stored output tensor to Spad
    #         GldMod.buffer2tensor(tensor=output_tensor_data,
    #                              offset=self.tensor_out.byte_base - self.tensor_in1.memory.address_space['Addr_Begin'],
    #                              strides=self.tensor_out.strides,
    #                              datatype=self.tensor_out.find_datatype(),
    #                              buffer=self.tensor_out.memory.buffer)
    #     elif self.type == PrimName.VV_V:
    #         raise NotImplementedError()
    #     elif self.type == PrimName.VS_V:
    #         raise NotImplementedError()
    #     elif self.type == PrimName.V_S:
    #         raise NotImplementedError()
    #     elif self.type == PrimName.V_V:
    #         raise NotImplementedError()
    #     elif self.type == PrimName.NOC:
    #         raise NotImplementedError()
    #     else:
    #         raise Exception('Illegal Prim.Name for golden_run !')

    def __repr__(self):
        if self.begin_cycle is not None and self.end_cycle is not None:
            cycle_cost = self.end_cycle - self.begin_cycle
        else:
            cycle_cost = '?'
        # Control
        if self.type == PrimName.SWCIMC:
            return f'Pid: {self.prim_id}, {self.type}, cimc mode: {self.cimc_mode}, ' \
                   f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'
        elif self.type == PrimName.GROUP_MASK:
            return f'Pid: {self.prim_id}, {self.type}, group/mask: {self.npu_group}/{self.npu_mask}, ' \
                   f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'

        # GEMM and GEMV
        if self.type == PrimName.GEMM:
            return f'Pid: {self.prim_id}, {self.type}, shape:[{self.tensor_in1.size[1]},' \
                   f'{self.tensor_in1.size[2]}]×[{self.tensor_in1.size[2]},{self.tensor_out.size[2]}], ' \
                   f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'
        elif self.type == PrimName.GEMV:
            return f'Pid: {self.prim_id}, {self.type}, shape:[{self.tensor_in1.size[1]},' \
                   f'{self.tensor_in1.size[2]}]×[{self.tensor_in1.size[2]},{self.tensor_out.size[2]}], ' \
                   f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'

        # NoC
        if self.type == PrimName.NOC:
            addr_src = hex(self.noc_settings.src_addr)[2:].zfill(8)  # 去掉'0x'，并补零到8位
            addr_dst = hex(self.noc_settings.dst_addr)[2:].zfill(8)  # 去掉'0x'，并补零到8位

            # 在前4位和后4位之间插入下划线
            addr_src_formatted = f"{addr_src[:4]}_{addr_src[4:]}"
            addr_dst_formatted = f"{addr_dst[:4]}_{addr_dst[4:]}"
            return f'Pid: {self.prim_id}, {self.type}, ' \
                   f'from src core {self.noc_settings.src_group}.{self.noc_settings.src_id} to ' \
                   f'dst core {self.noc_settings.dst_group}.{self.noc_settings.dst_id} , ' \
                   f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'
            # return f'Pid: {self.prim_id}, {self.type}, ' \
            #        f'from src core{self.noc_settings.src_id}(0x{addr_src_formatted}) to ' \
            #        f'dst core{self.noc_settings.dst_id}(0x{addr_dst_formatted}), ' \
            #        f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'

        # Vector
        if self.loop_num:
            if self.vector_op == VectorProcess.UNKNOWN:
                return f'Pid: {self.prim_id}, {self.type}×{self.loop_num}, ' \
                       f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'
            else:
                return f'Pid: {self.prim_id}, {self.type}×{self.loop_num}, {self.vector_op}, ' \
                       f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'
        else:
            if self.vector_op == VectorProcess.UNKNOWN:
                return f'Pid: {self.prim_id}, {self.type}, ' \
                       f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'
            else:
                return f'Pid: {self.prim_id}, {self.type}, {self.vector_op}, ' \
                       f'begins and ends @cycle({self.begin_cycle}, {self.end_cycle}), #cycle: {cycle_cost}'

    def print_module_use(self):
        if self.type != PrimName.NOC:
            module_id = 0
            for module in self.module_use:
                print(module_id, '-', module)
                module_id += 1
        else:
            module_id = 0
            for module in self.module_use:
                print(module_id, '-', 'Core', module.parent_core.get_group_id(), module)
                module_id += 1

    def details(self):
        if self.type != PrimName.NOC:
            prefix = 'Details of Expection Primitive: \n'
            this_prim = '    ' + str(self) + '\n'
            in1_info = '    in1 info -> ' + str(self.tensor_in1) + '\n'
            in2_info = '    in2 info -> ' + str(self.tensor_in2) + '\n'
            out_info = '    out info -> ' + str(self.tensor_out) + '\n'
            orig_info = '    orig info -> ' + str(self.tensor_orig) + '\n'
            conv_info = '    conv info -> ' + str(self.conv_settings) + '\n'
            other_info = '    control -> cimc_mode = ' + str(self.cimc_mode) + '\n'
            return prefix + this_prim + in1_info + in2_info + out_info + orig_info + conv_info + other_info
        else:
            self.tensor_in1.byte_base = self.noc_settings.src_addr
            self.tensor_out.byte_base = self.noc_settings.dst_addr
            prefix = 'Details of Expection Primitive: \n'
            this_prim = '    ' + str(self) + '\n'
            src_info = '    src info -> ' + str(self.tensor_in1) + '\n'
            dst_info = '    dst info -> ' + str(self.tensor_out) + '\n'
            self.tensor_in1.byte_base = None
            self.tensor_out.byte_base = None
            return prefix + this_prim + src_info + dst_info


class OpName(Enum):
    load_operator = auto()
    store_operator = auto()
    add_operator = auto()
    add_tensor_operator = auto()
    add_scalar_operator = auto()
    sub_operator = auto()
    sub_tensor_operator = auto()
    sub_scalar_operator = auto()
    mul_scalar_operator = auto()
    mul_tensor_operator = auto()
    exp_operator = auto()
    sigmoid_operator = auto()
    tanh_operator = auto()
    log_operator = auto()
    gelu_operator = auto()
    reciprocal_operator = auto()
    squareroot_operator = auto()
    mul_operator = auto()
    clip_operator = auto()
    equal_operator = auto()
    greater_operator = auto()
    not_equal_operator = auto()
    less_operator = auto()
    less_or_equal_operator = auto()
    greater_or_equal_operator = auto()
    and_operator = auto()
    or_operator = auto()
    not_operator = auto()
    neg_operator = auto()
    square_operator = auto()
    abs_operator = auto()
    signfunction_operator = auto()
    relu_operator = auto()
    relu6_operator = auto()
    silu_operator = auto()
    leakyrelu_operator = auto()
    hardswish_operator = auto()
    hardsigmoid_operator = auto()
    cast_operator = auto()
    transpose_operator = auto()
    mov_operator = auto()
    conv_operator = auto()
    gemm_operator = auto()
    gemv_operator = auto()

    gqa_decode_operator = auto()
    gqa_decode_qkvgen_operator = auto()
    ffn_decode_operator = auto()

    UNKNOWN = auto()


class Operator:

    def __init__(self, op_id=-1):
        self.op_id = op_id
        self.type = OpName.UNKNOWN
        self.primitives = Queue(queue_name='OpPrims')

    def __repr__(self):
        total_prims = self.primitives.length()
        if total_prims == 0:
            return f"Oid: {self.op_id}, Operator: {self.type} (empty)"
        primitives_repr = "\n".join(f"{i + 1}/{total_prims} {repr(prim)}"
                                    for i, prim in enumerate(self.primitives.array))
        return f"Oid: {self.op_id}, Operator: {self.type} (#primitive: {total_prims}):\n{primitives_repr}"


class Queue:
    def __init__(self, queue_name):
        self.qname = queue_name
        self.array = []

    def length(self):
        if not self.array:
            return 0
        else:
            return len(self.array)

    def is_empty(self):
        return self.array == []

    def not_empty(self):
        return self.array != []

    def push(self, value):
        self.array.append(value)

    def pop(self, index=0):
        return self.array.pop(index)

    def top(self):
        return self.array[0]

    def bottom(self):
        return self.array[self.length()-1]

    def __repr__(self):
        total_elements = len(self.array)  # 获取队列中元素的总数
        if total_elements == 0:
            return f"Queue '{self.qname}' (empty)"  # 如果队列为空，显示空队列信息

        # 生成每个元素的字符串表示，包括当前位置和总数
        elements_repr = "\n".join(f"{i + 1}/{total_elements} {repr(elem)}" for i, elem in enumerate(self.array))
        return f"Queue '{self.qname}' (#operator: {total_elements}):\n{elements_repr}"

    # def is_full(self):
    #     return self.length() == self.volume
    #
    # def about_full(self):
    #     return self.length() == self.volume - 1
    #
    # def not_full(self):
    #     return self.length() != self.volume
    #
    # def travel(self):
    #     for i in self.array:
    #         i.print_inst()
    #         print('  ', end='')
    #         i.info.print_info()
    #
    # def travel_begin(self, begin):
    #     for i in range(begin, self.length()):
    #         self.array[i].print_inst()
    #         print('  ', end='')
    #         self.array[i].info.print_info()
    

