import GlobalSettings as GlbSet
import os
import json
from datetime import datetime


class DDRRefreshLogger:
    """
    DDR刷新日志记录器，负责记录所有DRAM访问的刷新相关信息
    """

    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        self.log_file = os.path.join(log_dir, "ddr_refresh.log")
        self.stats_file = os.path.join(log_dir, "ddr_refresh_stats.json")

        # 统计信息
        self.stats = {
            'total_accesses': 0,
            'refresh_hits': 0,
            'total_stall_cycles': 0,
            'access_types': {'TLD': 0, 'TST': 0, 'NOC_SRC': 0, 'NOC_DEST': 0},
            'core_stats': {},  # 每个core的统计
            'max_stall': 0,
            'stall_distribution': {}  # 等待时间分布
        }

        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)

        # 初始化日志文件
        self._init_log_file()

    def _init_log_file(self):
        """初始化日志文件，写入头部信息"""
        with open(self.log_file, 'w') as f:
            f.write("# DDR Refresh Access Log\n")
            f.write(f"# Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("# Format: timestamp,core_x,core_y,access_type,begin_cycle,access_cycles,data_bits,")
            f.write("in_refresh_window,refresh_position,remaining_refresh_time,actual_stall,total_latency\n")

    def log_access(self, core_x, core_y, access_type, begin_cycle, access_cycles, data_bits,
                   in_refresh_window, refresh_position, remaining_refresh_time, actual_stall, total_latency):
        """
        记录一次DRAM访问

        Args:
            core_x, core_y: core的坐标位置
            access_type: 访问类型 (TLD/TST/NOC_READ/NOC_WRITE/NOC_SRC/NOC_DEST)
            begin_cycle: 访问开始周期
            access_cycles: 访问持续周期
            data_bits: 访问数据大小(bits)
            in_refresh_window: 是否命中刷新窗口
            refresh_position: 当前周期在刷新周期M中的位置
            remaining_refresh_time: 剩余刷新时间
            actual_stall: 实际等待时间
            total_latency: 总延迟
        """
        # 更新统计信息
        self.stats['total_accesses'] += 1
        if in_refresh_window:
            self.stats['refresh_hits'] += 1
            self.stats['total_stall_cycles'] += actual_stall
            self.stats['max_stall'] = max(self.stats['max_stall'], actual_stall)

            # 等待时间分布
            stall_bucket = (actual_stall // 10) * 10  # 按10周期分组
            self.stats['stall_distribution'][stall_bucket] = self.stats['stall_distribution'].get(stall_bucket, 0) + 1

        self.stats['access_types'][access_type] = self.stats['access_types'].get(access_type, 0) + 1

        # 每个core的统计
        core_id = f"core_{core_x}_{core_y}"
        if core_id not in self.stats['core_stats']:
            self.stats['core_stats'][core_id] = {
                'total_accesses': 0, 'refresh_hits': 0, 'total_stall': 0,
                'access_types': {'TLD': 0, 'TST': 0, 'NOC_SRC': 0, 'NOC_DEST': 0}
            }

        core_stat = self.stats['core_stats'][core_id]
        core_stat['total_accesses'] += 1
        core_stat['access_types'][access_type] = core_stat['access_types'].get(access_type, 0) + 1
        if in_refresh_window:
            core_stat['refresh_hits'] += 1
            core_stat['total_stall'] += actual_stall

        # 写入日志文件
        with open(self.log_file, 'a') as f:
            f.write(f"{datetime.now().timestamp():.6f},{core_x},{core_y},{access_type},{begin_cycle},"
                   f"{access_cycles},{data_bits},{in_refresh_window},{refresh_position},"
                   f"{remaining_refresh_time},{actual_stall},{total_latency}\n")

    def save_stats(self):
        """保存统计信息到JSON文件"""
        # 计算衍生统计
        if self.stats['total_accesses'] > 0:
            self.stats['refresh_hit_rate'] = self.stats['refresh_hits'] / self.stats['total_accesses']
            self.stats['avg_stall_cycles'] = self.stats['total_stall_cycles'] / max(self.stats['refresh_hits'], 1)
        else:
            self.stats['refresh_hit_rate'] = 0.0
            self.stats['avg_stall_cycles'] = 0.0

        with open(self.stats_file, 'w') as f:
            json.dump(self.stats, f, indent=2)


class DDRRefreshController:
    """
    Periodic DDR refresh model (global across all cores).

    - period_cycles (M): refresh period in cycles
    - window_cycles (N): duration of refresh busy window within each period
    - mode:
        'entry'  : only stall accesses whose begin_cycle falls into the window
        'strict' : if an access overlaps with a refresh window at any time, extend by remaining busy time
    """

    def __init__(self,
                 period_cycles: int = None,
                 window_cycles: int = None,
                 mode: str = None,
                 enabled: bool = None,
                 enable_logging: bool = True):
        self.enabled = GlbSet.DDR_REFRESH_ENABLE if enabled is None else enabled
        self.period = GlbSet.DDR_REFRESH_PERIOD if period_cycles is None else int(period_cycles)
        self.window = GlbSet.DDR_REFRESH_WINDOW if window_cycles is None else int(window_cycles)
        self.mode = GlbSet.DDR_REFRESH_MODE if mode is None else mode

        # 日志记录器
        self.logger = DDRRefreshLogger() if enable_logging and self.enabled else None

        if self.window > self.period:
            raise ValueError("DDR refresh window must be <= period")

    def is_in_refresh(self, cycle: int) -> bool:
        if not self.enabled:
            return False
        t = cycle % self.period
        return t < self.window

    def remaining_busy(self, cycle: int) -> int:
        """
        Remaining time in the current refresh window at given cycle.
        Returns 0 if not in refresh or disabled.
        """
        if not self.enabled:
            return 0
        t = cycle % self.period
        if t < self.window:
            return self.window - t
        return 0

    def apply_to_access(self, begin_cycle: int, access_cycles: int,
                       core_x: int = -1, core_y: int = -1, access_type: str = "UNKNOWN",
                       data_bits: int = 0, total_latency: int = 0) -> int:
        """
        Given an access starting at begin_cycle lasting access_cycles (not including extra latencies),
        return additional stall cycles due to refresh.

        Args:
            begin_cycle: 访问开始周期
            access_cycles: 访问持续周期
            core_x, core_y: core坐标（用于日志记录）
            access_type: 访问类型（用于日志记录）
            data_bits: 数据大小（用于日志记录）
            total_latency: 总延迟（用于日志记录）
        """
        # 检查是否应该记录此类型的访问
        should_log = (self.logger is not None and
                     (access_type in ['TLD', 'TST'] or
                      (access_type in ['NOC_SRC', 'NOC_DEST'] and GlbSet.DDR_REFRESH_LOG_NOC)))

        if not self.enabled or access_cycles <= 0:
            # 记录未启用刷新的访问
            if should_log:
                self.logger.log_access(core_x, core_y, access_type, begin_cycle, access_cycles, data_bits,
                                     False, begin_cycle % self.period, 0, 0, total_latency)
            return 0

        # 计算刷新相关信息
        refresh_position = begin_cycle % self.period
        in_refresh_window = self.is_in_refresh(begin_cycle)
        remaining_refresh_time = self.remaining_busy(begin_cycle)
        actual_stall = 0

        if self.mode == 'entry':
            # Stall only if the access begins during a refresh window
            if in_refresh_window:
                actual_stall = remaining_refresh_time
        else:
            # strict mode
            t = begin_cycle % self.period
            until_next_window = (self.period - t) % self.period
            if until_next_window == 0:
                actual_stall = self.window
            elif access_cycles > until_next_window:
                actual_stall = self.window

        # 记录访问日志
        if should_log:
            self.logger.log_access(core_x, core_y, access_type, begin_cycle, access_cycles, data_bits,
                                 in_refresh_window, refresh_position, remaining_refresh_time,
                                 actual_stall, total_latency + actual_stall)

        return actual_stall

    def finalize_logging(self):
        """完成日志记录，保存统计信息"""
        if self.logger:
            self.logger.save_stats()


