import GlobalSettings as GlbSet


class DDRRefreshController:
    """
    Periodic DDR refresh model (global across all cores).

    - period_cycles (M): refresh period in cycles
    - window_cycles (N): duration of refresh busy window within each period
    - mode:
        'entry'  : only stall accesses whose begin_cycle falls into the window
        'strict' : if an access overlaps with a refresh window at any time, extend by remaining busy time
    """

    def __init__(self,
                 period_cycles: int = None,
                 window_cycles: int = None,
                 mode: str = None,
                 enabled: bool = None):
        self.enabled = GlbSet.DDR_REFRESH_ENABLE if enabled is None else enabled
        self.period = GlbSet.DDR_REFRESH_PERIOD if period_cycles is None else int(period_cycles)
        self.window = GlbSet.DDR_REFRESH_WINDOW if window_cycles is None else int(window_cycles)
        self.mode = GlbSet.DDR_REFRESH_MODE if mode is None else mode

        if self.window > self.period:
            raise ValueError("DDR refresh window must be <= period")

    def is_in_refresh(self, cycle: int) -> bool:
        if not self.enabled:
            return False
        t = cycle % self.period
        return t < self.window

    def remaining_busy(self, cycle: int) -> int:
        """
        Remaining time in the current refresh window at given cycle.
        Returns 0 if not in refresh or disabled.
        """
        if not self.enabled:
            return 0
        t = cycle % self.period
        if t < self.window:
            return self.window - t
        return 0

    def apply_to_access(self, begin_cycle: int, access_cycles: int) -> int:
        """
        Given an access starting at begin_cycle lasting access_cycles (not including extra latencies),
        return additional stall cycles due to refresh.
        """
        if not self.enabled or access_cycles <= 0:
            return 0

        if self.mode == 'entry':
            # Stall only if the access begins during a refresh window
            if self.is_in_refresh(begin_cycle):
                return self.remaining_busy(begin_cycle)
            return 0

        # strict: if any overlap occurs within duration, extend by overlap at entry or during run
        # conservative approximation: if begin not in window, check whether the window occurs before access completes
        # We compute next refresh boundary and see if access crosses into it
        t = begin_cycle % self.period
        # Time until next window start
        until_next_window = (self.period - t) % self.period
        if until_next_window == 0:
            # at boundary; window starts now
            return self.window
        # If the access duration reaches into the next window, stall by full window
        if access_cycles > until_next_window:
            return self.window
        return 0


