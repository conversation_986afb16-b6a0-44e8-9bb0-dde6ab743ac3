# * * * * * * * * * * * * * * * * #
# Basic Functional Modules in NPU Core
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #


import SIMTemplate as SIMTemp
import GlobalSettings as GlbSet
from abc import ABC, abstractmethod
from enum import Enum, auto
import math
import warnings
import numpy as np


class ModState(Enum):
    IDLE = auto()
    BUSY = auto()


class ModuleTemp(ABC):
    # Basic Attributes
    def __init__(self, name, id, parent_core):
        self.name = name
        self.id = id
        self.state = ModState.IDLE
        self.current_cycle = None
        self.stream_cycle = None
        self.op_energy = {}             # Format: Operation_Unit
        self.record = {}                # Format: Operation_Unit_Num
        self.cycle_cost = {}
        self.parent_core = parent_core
        self.latency = 0

    def set_cycles_zero(self):
        self.cycle_cost = {key: 0 for key in self.cycle_cost}

    def raise_unsupported_prim(self, prim):
        raise Exception(str(self.name) + '-' + str(self.id) + ' Running Unsupported Primitive !'
                        + '\n' + str(prim.details()))

    @abstractmethod
    def run_prim(self, primitive):
        # All modules can decode primitives, calculate runtime, and record operations.
        pass

    @abstractmethod
    def calculate_energy(self):
        # All modules must be able to calculate their energy consumption.
        pass


# Modules: Memory
class MemoryMod(ModuleTemp):        # parent
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {
            'Read_Byte': 0.0,
            'Write_Byte': 0.0
        }
        self.record = {
            'Read_Byte_Num': 0.0,
            'Write_Byte_Num': 0.0
        }
        self.address_space = {'Addr_Begin': 0x0000, 'Addr_End': 0xffff}
        self.bit_width = 0.0

    @abstractmethod
    def run_prim(self, primitive):
        # run_prim should be realized by child class
        pass

    def calculate_energy(self):
        read_energy  = self.op_energy['Read_Byte']  * self.record['Read_Byte_Num']
        write_energy = self.op_energy['Write_Byte'] * self.record['Write_Byte_Num']
        return read_energy + write_energy

    def __repr__(self):
        return f"{self.__class__.__name__}(id={self.id}, addr_space=[0x{self.address_space['Addr_Begin']:08x}, 0x{self.address_space['Addr_End']}])"


class DRAMBank(MemoryMod):         
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {
            'Read_Byte' : GlbSet.DRAMBank_Read_Byte,
            'Write_Byte': GlbSet.DRAMBank_Write_Byte
        }
        self.bit_width = GlbSet.DRAMBank_Data_Bitwidth
        self.cycle_cost = {
            'read': 0, 
            'write': 0, 
            'read_noc': 0, 
            'write_noc': 0
            }
        # self.buffer = np.zeros([GlbSet.DRAMBank_Capacity_Byte, ], dtype=np.int8)
        self.address_space = {
            'Addr_Begin': GlbSet.DRAMBank_Base_Address_Byte,
            'Addr_End': GlbSet.DRAMBank_Base_Address_Byte + GlbSet.DRAMBank_Capacity_Byte - 1
        }

    def run_prim(self, primitive):
        self.cycle_cost = {'read': 0, 'write': 0, 'read_noc': 0, 'write_noc': 0}
        # DRAMBank related primitives: Tensor Load/Store
        # Note: Primitives other than load/store/noc CANNOT access global memory, that is, DRAM banks.
        # Calculate cycle cost and Record operation HERE !
        if primitive.type == SIMTemp.PrimName.TLD:
            # Calculate cycle cost
            read_bits = primitive.tensor_in1.bits
            read_cycles = math.ceil(read_bits / self.bit_width)
            self.cycle_cost['read'] = read_cycles
            primitive.set_end_cycle(module_cycle_cost=read_cycles)
            # Calculate latency
            total_burst_latency = GlbSet.BurstRead_Latency * math.ceil(read_bits / GlbSet.MAX_Burst_Size)
            self.latency = total_burst_latency
            primitive.set_latency(latency=total_burst_latency)
            # DDR refresh stall
            if self.parent_core.top and hasattr(self.parent_core.top, 'ddr_refresh'):
                extra_stall = self.parent_core.top.ddr_refresh.apply_to_access(
                    primitive.begin_cycle, read_cycles,
                    core_x=self.parent_core.x, core_y=self.parent_core.y,
                    access_type="TLD", data_bits=read_bits,
                    total_latency=total_burst_latency
                )
                if extra_stall:
                    self.latency += extra_stall
                    primitive.set_latency(latency=extra_stall)
            # Record operation
            self.record['Read_Byte_Num'] = read_bits // 8
        elif primitive.type == SIMTemp.PrimName.TST:
            # Calculate cycle cost
            write_bits = primitive.tensor_out.bits
            write_cycles = math.ceil(write_bits / self.bit_width)
            self.cycle_cost['write'] = write_cycles
            primitive.set_end_cycle(module_cycle_cost=write_cycles)
            # Calculate latency
            total_burst_latency = GlbSet.BurstWrite_Latency * math.ceil(write_bits / GlbSet.MAX_Burst_Size)
            self.latency = total_burst_latency
            primitive.set_latency(latency=total_burst_latency)
            # DDR refresh stall
            if self.parent_core.top and hasattr(self.parent_core.top, 'ddr_refresh'):
                extra_stall = self.parent_core.top.ddr_refresh.apply_to_access(
                    primitive.begin_cycle, write_cycles,
                    core_x=self.parent_core.x, core_y=self.parent_core.y,
                    access_type="TST", data_bits=write_bits,
                    total_latency=total_burst_latency
                )
                if extra_stall:
                    self.latency += extra_stall
                    primitive.set_latency(latency=extra_stall)
            # Record operation
            self.record['Write_Byte_Num'] = write_bits // 8
        elif primitive.type == SIMTemp.PrimName.NOC:
            if primitive.noc_settings.src_memory == self:                   # Src Read
                # Calculate cycle cost
                read_bits = primitive.tensor_in1.bits
                read_cycles = math.ceil(read_bits / self.bit_width)
                self.cycle_cost['read_noc'] = read_cycles
                primitive.set_end_cycle(module_cycle_cost=read_cycles)
                # Calculate latency
                total_burst_latency = GlbSet.BurstRead_Latency * math.ceil(read_bits / GlbSet.MAX_Burst_Size)
                self.latency = total_burst_latency
                primitive.set_latency(latency=total_burst_latency)
                # DDR refresh stall
                if self.parent_core.top and hasattr(self.parent_core.top, 'ddr_refresh'):
                    extra_stall = self.parent_core.top.ddr_refresh.apply_to_access(primitive.begin_cycle, read_cycles)
                    if extra_stall:
                        self.latency += extra_stall
                        primitive.set_latency(latency=extra_stall)
                # Record operation
                self.record['Read_Byte_Num'] = read_bits // 8
            elif primitive.noc_settings.dst_memory == self:                 # Dst Write
                # Calculate cycle cost
                write_bits = primitive.tensor_out.bits
                write_cycles = math.ceil(write_bits / self.bit_width)
                self.cycle_cost['write_noc'] = write_cycles
                primitive.set_end_cycle(module_cycle_cost=write_cycles)
                # Calculate latency
                total_burst_latency = GlbSet.BurstWrite_Latency * math.ceil(write_bits / GlbSet.MAX_Burst_Size)
                self.latency = total_burst_latency
                primitive.set_latency(latency=total_burst_latency)
                # DDR refresh stall
                if self.parent_core.top and hasattr(self.parent_core.top, 'ddr_refresh'):
                    extra_stall = self.parent_core.top.ddr_refresh.apply_to_access(primitive.begin_cycle, write_cycles)
                    if extra_stall:
                        self.latency += extra_stall
                        primitive.set_latency(latency=extra_stall)
                # Record operation
                self.record['Write_Byte_Num'] = write_bits // 8
            else:
                raise Exception('Both NOC src and dst is not this DRAMBank !')
        # Support for split NoC primitives
        elif primitive.type == SIMTemp.PrimName.NOC_SRC:
            if primitive.noc_settings.src_memory == self:
                read_bits = primitive.tensor_in1.bits if getattr(primitive.tensor_in1, 'bits', 0) else (
                    getattr(primitive.noc_settings, 'transfer_size', 0) * 8
                )
                read_cycles = math.ceil(read_bits / self.bit_width)
                self.cycle_cost['read_noc'] = read_cycles
                primitive.set_end_cycle(module_cycle_cost=read_cycles)
                total_burst_latency = GlbSet.BurstRead_Latency * math.ceil(read_bits / GlbSet.MAX_Burst_Size)
                self.latency = total_burst_latency
                primitive.set_latency(latency=total_burst_latency)
                if self.parent_core.top and hasattr(self.parent_core.top, 'ddr_refresh'):
                    extra_stall = self.parent_core.top.ddr_refresh.apply_to_access(
                        primitive.begin_cycle, read_cycles,
                        core_x=self.parent_core.x, core_y=self.parent_core.y,
                        access_type="NOC_SRC", data_bits=read_bits,
                        total_latency=total_burst_latency
                    )
                    if extra_stall:
                        self.latency += extra_stall
                        primitive.set_latency(latency=extra_stall)
                if read_bits:
                    self.record['Read_Byte_Num'] = read_bits // 8
        elif primitive.type == SIMTemp.PrimName.NOC_DEST:
            if primitive.noc_settings.dst_memory == self:
                write_bits = primitive.tensor_out.bits if getattr(primitive.tensor_out, 'bits', 0) else (
                    getattr(primitive.noc_settings, 'transfer_size', 0) * 8
                )
                write_cycles = math.ceil(write_bits / self.bit_width)
                self.cycle_cost['write_noc'] = write_cycles
                primitive.set_end_cycle(module_cycle_cost=write_cycles)
                total_burst_latency = GlbSet.BurstWrite_Latency * math.ceil(write_bits / GlbSet.MAX_Burst_Size)
                self.latency = total_burst_latency
                primitive.set_latency(latency=total_burst_latency)
                if self.parent_core.top and hasattr(self.parent_core.top, 'ddr_refresh'):
                    extra_stall = self.parent_core.top.ddr_refresh.apply_to_access(
                        primitive.begin_cycle, write_cycles,
                        core_x=self.parent_core.x, core_y=self.parent_core.y,
                        access_type="NOC_DEST", data_bits=write_bits,
                        total_latency=total_burst_latency
                    )
                    if extra_stall:
                        self.latency += extra_stall
                        primitive.set_latency(latency=extra_stall)
                if write_bits:
                    self.record['Write_Byte_Num'] = write_bits // 8
        else:
            self.raise_unsupported_prim(primitive)

        assert self.state == ModState.IDLE
        self.state = ModState.BUSY

    def __repr__(self):
        return f"{self.__class__.__name__}(id={self.id}, addr_space=[0x{self.address_space['Addr_Begin']:08x}, 0x{self.address_space['Addr_End']}])"


class SRAMBank(MemoryMod):          # child for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {'Read_Byte': GlbSet.SRAMBank_Read_Byte,
                          'Write_Byte': GlbSet.SRAMBank_Write_Byte}
        self.bit_width = GlbSet.SRAMBank_Data_Bitwidth
        self.cycle_cost = {'read_in1': 0, 'read_in2': 0, 'read_orig': 0, 'write_out': 0,
                           'read_noc': 0, 'write_noc': 0}
        self.buffer = np.zeros([GlbSet.SRAMBank_Capacity_Byte, ], dtype=np.int8)

    def run_prim(self, primitive):
        # possible operations when an SRAM Bank running primitive
        # Read Tin1, Read Tin2, Write Tout, Read Torigin

        cycle_costs = 0
        self.cycle_cost = {'read_in1': 0, 'read_in2': 0, 'read_orig': 0, 'write_out': 0, 'read_noc': 0, 'write_noc': 0}

        # write operations
        if self.addr_match(primitive.tensor_out.byte_base):
            cycle_costs += math.ceil(primitive.tensor_out.bits / self.bit_width)
            self.cycle_cost['write_out'] = math.ceil(primitive.tensor_out.bits / self.bit_width)
            self.record['Write_Byte_Num'] += primitive.tensor_out.bits // 8

        # read operations
        if self.addr_match(primitive.tensor_in1.byte_base):
            cycle_costs += math.ceil(primitive.tensor_in1.bits / self.bit_width)
            self.cycle_cost['read_in1'] = math.ceil(primitive.tensor_in1.bits / self.bit_width)
            self.record['Read_Byte_Num'] += primitive.tensor_in1.bits // 8
        if self.addr_match(primitive.tensor_in2.byte_base):
            cycle_costs += math.ceil(primitive.tensor_in2.bits / self.bit_width)
            self.cycle_cost['read_in2'] = math.ceil(primitive.tensor_in2.bits / self.bit_width)
            self.record['Read_Byte_Num'] += primitive.tensor_in2.bits // 8
        if self.addr_match(primitive.tensor_orig.byte_base):
            cycle_costs += math.ceil(primitive.tensor_orig.bits / self.bit_width)
            self.cycle_cost['read_orig'] = math.ceil(primitive.tensor_orig.bits / self.bit_width)
            self.record['Read_Byte_Num'] += primitive.tensor_in2.bits // 8

        # read and write during NOC primitive
        # will not overlap with above calculation
        if primitive.noc_settings.src_memory == self:
            cycle_costs += math.ceil(primitive.tensor_in1.bits / self.bit_width)
            self.cycle_cost['read_noc'] = math.ceil(primitive.tensor_in1.bits / self.bit_width)
            self.record['Read_Byte_Num'] += primitive.tensor_in1.bits // 8
        if primitive.noc_settings.dst_memory == self:
            cycle_costs += math.ceil(primitive.tensor_out.bits / self.bit_width)
            self.cycle_cost['write_noc'] = math.ceil(primitive.tensor_out.bits / self.bit_width)
            self.record['Write_Byte_Num'] += primitive.tensor_out.bits // 8

        primitive.set_end_cycle(module_cycle_cost=cycle_costs)

        # state transfer
        assert self.state == ModState.IDLE, primitive
        self.state = ModState.BUSY

    def addr_match(self, byte_base):
        if byte_base is not None:
            return self.address_space['Addr_Begin'] <= byte_base <= self.address_space['Addr_End']
        else:
            return False

    def __repr__(self):
        return f"{self.__class__.__name__}(id={self.id}, addr_space=[0x{self.address_space['Addr_Begin']:08x}, 0x{self.address_space['Addr_End']}])"


# Modules: Compute
class ComputeMod(ModuleTemp):       # parent
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {'Add_Byte': 0.0,
                          'Mul_Byte': 0.0,
                          'Logic_Byte': 0.0}
        self.record = {'Add_Byte_Num': 0.0,
                       'Mul_Byte_Num': 0.0,
                       'Logic_Byte_Num': 0.0}

    @abstractmethod
    def run_prim(self, primitive):
        # run_prim should be realized by child class
        pass

    @abstractmethod
    def calculate_energy(self):
        # calculate_energy of ComputeMods should be realized by child class
        pass


class VPU(ComputeMod):              # child for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {'Add_Byte': GlbSet.VPU_Add_Byte,
                          'Mul_Byte': GlbSet.VPU_Mul_Byte,
                          'Logic_Byte': GlbSet.VPU_Logic_Byte}
        self.vpe_per_vpu = GlbSet.VPE_per_VPU
        self.vpe_each_input_bits = GlbSet.VPE_INPUT_Byte * 8
        self.cycle_cost = {'VectorProcess': 0}
        assert (self.vpe_per_vpu & (self.vpe_per_vpu - 1)) == 0         # check: is power of 2 or not

    def run_prim(self, primitive):
        # VPU related primitives: VV_V, VS_V, V_S, V_V
        # When considering effective performance, the following factors need to be taken into account
        # 1) the equivalent parallelism of VPEs under different precision and operation configurations;
        # 2) the topological relationships between VPEs.

        simd_prims = [SIMTemp.PrimName.VV_V, SIMTemp.PrimName.VS_V, SIMTemp.PrimName.V_V]
        reduction_prims = [SIMTemp.PrimName.V_S]

        add_prims = [SIMTemp.VectorProcess.ADD, SIMTemp.VectorProcess.SUB, SIMTemp.VectorProcess.RSUB]
        compare_prims = [SIMTemp.VectorProcess.MAX, SIMTemp.VectorProcess.MIN, SIMTemp.VectorProcess.COMPARE,
                         SIMTemp.VectorProcess.EQUAL, SIMTemp.VectorProcess.NOT_EQUAL,
                         SIMTemp.VectorProcess.LESS, SIMTemp.VectorProcess.LESS_OR_EQUAL,
                         SIMTemp.VectorProcess.GREATER, SIMTemp.VectorProcess.GREATER_OR_EQUAL]
        mul_prims = [SIMTemp.VectorProcess.MUL]

        if primitive.type in simd_prims:

            # Calculate cycle cost
            # Input vector length refers to tensor1
            vector_length = primitive.tensor_in1.layout['dim0b'] * primitive.tensor_in1.layout['dim0a']
            # default width is set to -1
            width_max = max(primitive.tensor_in1.width, primitive.tensor_in2.width, primitive.tensor_out.width)
            assert self.vpe_each_input_bits % width_max == 0
            vpe_parallelism = self.vpe_each_input_bits // width_max
            topology_coeff = self.vpe_per_vpu
            effective_perf = vpe_parallelism * topology_coeff
            vp_cycles = math.ceil(vector_length / effective_perf) * primitive.loop_num
            primitive.set_end_cycle(module_cycle_cost=vp_cycles)
            self.cycle_cost['VectorProcess'] = vp_cycles

            # Calculate latency
            self.latency = self.vp_latency(primitive)
            primitive.set_latency(latency=self.latency)

            # record operations
            if primitive.vector_op in add_prims or primitive.vector_op in compare_prims:
                if primitive.tensor_in1.type == 'INT':
                    adder_width = width_max
                elif primitive.tensor_in1.type == 'BF':
                    if primitive.vector_op in add_prims:
                        adder_width = 8
                    elif primitive.vector_op in compare_prims:
                        adder_width = 16
                elif primitive.tensor_in1.type == 'FP' and primitive.tensor_in1.width == 16:
                    if primitive.vector_op in add_prims:
                        adder_width = 11
                    elif primitive.vector_op in compare_prims:
                        adder_width = 16
                elif primitive.tensor_in1.type == 'FP' and primitive.tensor_in1.width == 32:
                    adder_width = 24
                self.record['Add_Byte_Num'] += math.ceil(vector_length * adder_width / 8)
            elif primitive.vector_op in mul_prims:
                if primitive.tensor_in1.type == 'INT' or primitive.tensor_in1.type == 'BF':
                    multiplier_width = 16               # 4×4bit or 2×8bit muliplier
                elif primitive.tensor_in1.type == 'FP' and primitive.tensor_in1.width == 16:
                    multiplier_width = 22               # 2×11bit muliplier
                elif primitive.tensor_in1.type == 'FP' and primitive.tensor_in1.width == 32:
                    if primitive.tensor_in2.type == 'BF':
                        multiplier_width = 8            # 1×8bit muliplier
                    elif primitive.tensor_in2.type == 'FP':
                        multiplier_width = 11           # 1×11bit muliplier
                self.record['Mul_Byte_Num'] += math.ceil(vector_length * multiplier_width / 8) * primitive.loop_num
            else:
                self.record['Logic_Byte_Num'] += math.ceil(vector_length * width_max / 8) * primitive.loop_num

            # state transfer
            assert self.state == ModState.IDLE
            self.state = ModState.BUSY

        elif primitive.type in reduction_prims:

            # Calculate cycle cost
            # Input vector length refers to tensor1
            vector_length = primitive.tensor_in1.layout['dim0b'] * primitive.tensor_in1.layout['dim0a']
            assert self.vpe_each_input_bits % primitive.tensor_in1.width == 0
            # Two input ports serve one input vector that requries reduction: 2 *
            vpe_parallelism = 2 * self.vpe_each_input_bits // primitive.tensor_in1.width
            topology_coeff = self.vpe_per_vpu // 2
            effective_perf = vpe_parallelism * topology_coeff
            vp_cycles = math.ceil(vector_length / effective_perf)
            tree_depth = math.ceil(math.log(self.vpe_per_vpu, 2))
            primitive.set_end_cycle(module_cycle_cost=(vp_cycles + tree_depth - 1) * primitive.loop_num)
            self.cycle_cost['VectorProcess'] = (vp_cycles + tree_depth - 1) * primitive.loop_num

            # Calculate latency
            self.latency = self.vp_latency(primitive)
            primitive.set_latency(latency=self.latency)

            # record operations

            # state transfer

    @staticmethod
    def vp_latency(primitive):
        if primitive.vector_op == SIMTemp.VectorProcess.MUL and \
                (primitive.tensor_in1.width >= 16 or primitive.tensor_in2.width >= 16):
            latency = 2
        else:
            latency = 1
        return latency

    def __repr__(self):
        return f"VPU with {self.vpe_per_vpu} Engines each receives 2×{self.vpe_each_input_bits}bit inputs, " \
                f"Cycle cost: VectorProcess={self.cycle_cost['VectorProcess']}, Latency={self.latency}"

    def calculate_energy(self):
        add_energy = self.op_energy['Add_Byte'] * self.record['Add_Byte_Num']
        mul_energy = self.op_energy['Mul_Byte'] * self.record['Mul_Byte_Num']
        logic_energy = self.op_energy['Logic_Byte'] * self.record['Logic_Byte_Num']
        return add_energy + mul_energy + logic_energy


class DWEngine(ComputeMod):         # child for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {'Add_Byte': GlbSet.DWEngine_Add_Byte,
                          'Mul_Byte': GlbSet.DWEngine_Mul_Byte}

    def run_prim(self, primitive):
        # Need DWEngine ??
        raise NotImplementedError("DWEngine run_prim not implemented")


class CIMCPage(MemoryMod):          # a part of CIM Cluster
    def __init__(self, name, id, parent_core, parent_CIM):
        super().__init__(name, id, parent_core)
        self.op_energy = {'Read_Byte': GlbSet.CIMC_Read_Byte,
                          'Write_Byte': GlbSet.CIMC_Write_Byte}
        self.bit_width = GlbSet.CIMCluster_Data_Bitwidth
        self.parent_CIM = parent_CIM
        self.cycle_cost = {'write': 0}
        self.buffer = np.zeros([GlbSet.CIMPage_Capacity_Byte, ], dtype=np.int8)
        # Different data types have different numbers of mappable columns, and therefore the exp_max quantity varies.
        self.exp_register = np.zeros([1], dtype=np.int8)

    def run_prim(self, primitive):
        # possible operations when an SRAM Bank running primitive
        # Read Tin1, Read Tin2, Write Tout, Read Torigin

        cycle_costs = 0
        self.cycle_cost = {'write': 0}

        # write operations
        if self.addr_match(primitive.tensor_out.byte_base, primitive.tensor_out.byte_base_list):
            total_pages_to_write = primitive.tensor_out.layout['dim2']
            cycle_costs += math.ceil(primitive.tensor_out.bits / self.bit_width) // total_pages_to_write
            self.record['Write_Byte_Num'] += primitive.tensor_out.bits // 8 // total_pages_to_write
            self.cycle_cost['write'] += math.ceil(primitive.tensor_out.bits / self.bit_width) // total_pages_to_write
            if self.addr_match(primitive.tensor_out.byte_base):     # the first page to write
                primitive.set_end_cycle(module_cycle_cost=cycle_costs*total_pages_to_write)

        # read operations
        tensor_processing_list = [SIMTemp.PrimName.GEMV, SIMTemp.PrimName.GEMM]
        if primitive.type in tensor_processing_list:
            if self.addr_match(primitive.conv_settings.byte_base_wt):
                cycle_costs += 0                # page read is calculated in CIMArray
                self.record['Read_Byte_Num'] += primitive.tensor_in1.bits // 8
                primitive.set_end_cycle(module_cycle_cost=cycle_costs)

        # state transfer
        assert self.state == ModState.IDLE
        self.state = ModState.BUSY

    def addr_match(self, byte_base, byte_base_list=None):
        if byte_base_list is None:
            return self.address_space['Addr_Begin'] <= byte_base <= self.address_space['Addr_End']
        else:
            for cur_byte_addr in byte_base_list:
                if self.address_space['Addr_Begin'] <= cur_byte_addr <= self.address_space['Addr_End']:
                    return True

            return False

    def __repr__(self):
        addr_begin = hex(self.address_space['Addr_Begin'])[2:].zfill(8)  # 去掉'0x'，并补零到8位
        addr_end = hex(self.address_space['Addr_End'])[2:].zfill(8)  # 去掉'0x'，并补零到8位

        # 在前4位和后4位之间插入下划线
        addr_begin_formatted = f"{addr_begin[:4]}_{addr_begin[4:]}"
        addr_end_formatted = f"{addr_end[:4]}_{addr_end[4:]}"

        return f"{self.__class__.__name__} {self.id} " \
               f"from 0x{addr_begin_formatted} to 0x{addr_end_formatted}, " \
               f"Cycle cost: Write={self.cycle_cost['write']}"


class CIMMACArray(ModuleTemp):       # child for NPU Core in CIM
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.op_energy = {'MAC_Byte': GlbSet.CIMC_MAC_Byte}
        self.record = {'MAC_Byte_Num': 0.0}
        self.cycle_cost = {'mac': 0}
        # self.cycle_cost = {'page_read': 0, 'mac': 0}  previous version

    def run_prim(self, primitive):

        self.cycle_cost = {'mac': 0}

        if primitive.type == SIMTemp.PrimName.GEMM:
            # assuming A[m, p] × B[p, n]
            m, p, n = primitive.tensor_in1.layout['dim1'], \
                      primitive.tensor_in1.layout['dim0'], \
                      primitive.tensor_out.layout['dim0']

            # Calculate cycle cost
            mac_cycles = m * primitive.tensor_in1.mac_width()
            primitive.set_end_cycle(module_cycle_cost=mac_cycles)
            self.cycle_cost['mac'] = mac_cycles

            # Calculate latency
            self.latency = self.mac_latency(primitive)
            primitive.set_latency(latency=self.latency)

            # record operations
            mac_num = 2 * m * p * n
            self.record['MAC_Byte_Num'] += math.ceil(mac_num * (primitive.tensor_in1.mac_width() / 8)
                                                     * (primitive.conv_settings.mac_width() / 8))

        elif primitive.type == SIMTemp.PrimName.GEMV:
            # assuming A[1, p] × B[p, n]
            p, n = primitive.tensor_in1.layout['dim0'], primitive.tensor_out.layout['dim0']

            # Calculate cycle cost
            mac_cycles = 1 * primitive.tensor_in1.mac_width()
            primitive.set_end_cycle(module_cycle_cost=mac_cycles)
            self.cycle_cost['mac'] = mac_cycles

            # Calculate latency
            self.latency = self.mac_latency(primitive)
            primitive.set_latency(latency=self.latency)

            # record operations
            mac_num = 2 * 1 * p * n
            self.record['MAC_Byte_Num'] += math.ceil(mac_num * (primitive.tensor_in1.mac_width() / 8)
                                                     * (primitive.conv_settings.mac_width() / 8))

    @staticmethod
    def mac_latency(primitive):
        # calculate latency according to input/output data type
        if primitive.tensor_in1.type == 'INT' and primitive.tensor_out.type == 'INT':
            latency = 1 + GlbSet.MPU_PsumProcess_Latency + GlbSet.MPU_PostProcess_Latency
        elif primitive.tensor_in1.type == 'INT':
            latency = 1 + GlbSet.MPU_PsumProcess_Latency + GlbSet.MPU_PostProcess_Latency + \
                      + primitive.tensor_out.mac_width()
        else:
            latency = GlbSet.MPU_PsumProcess_Latency + GlbSet.MPU_PostProcess_Latency + GlbSet.MPU_InputAlign_Latency \
                      + 1 + primitive.tensor_out.mac_width()
        return latency

    # previous version
    def run_prim_v0p0(self, primitive):

        self.cycle_cost = {'page_read': 0, 'mac': 0}

        if primitive.type == SIMTemp.PrimName.GEMM:
            # assuming A[m, p] × B[p, n]
            m, p, n = primitive.tensor_in1.layout['dim1'], \
                      primitive.tensor_in1.layout['dim0'], \
                      primitive.tensor_out.layout['dim0']

            # Calculate cycle cost
            # page read and bit serial input
            page_read_cycles = 1
            bit_serial_input_per_data = primitive.tensor_in1.mac_width()
            mac_cycles = m * bit_serial_input_per_data
            compute_cycles = page_read_cycles + mac_cycles
            primitive.set_end_cycle(module_cycle_cost=compute_cycles)
            self.cycle_cost['page_read'] = page_read_cycles
            self.cycle_cost['mac'] = mac_cycles

            # record operations
            mac_num = 2 * m * p * n
            self.record['MAC_Byte_Num'] += math.ceil(mac_num * (primitive.tensor_in1.mac_width() / 8)
                                                     * (primitive.conv_settings.mac_width() / 8))
        elif primitive.type == SIMTemp.PrimName.GEMV:
            # assuming A[1, p] × B[p, n]
            p, n = primitive.tensor_in1.layout['dim0'], primitive.tensor_out.layout['dim0']

            # Calculate cycle cost
            # page read and bit serial input
            page_read_cycles = 1
            bit_serial_input_per_data = primitive.tensor_in1.mac_width()
            mac_cycles = 1 * bit_serial_input_per_data
            compute_cycles = page_read_cycles + mac_cycles
            primitive.set_end_cycle(module_cycle_cost=compute_cycles)
            self.cycle_cost['page_read'] = page_read_cycles
            self.cycle_cost['mac'] = mac_cycles

            # record operations
            mac_num = 2 * 1 * p * n
            self.record['MAC_Byte_Num'] += math.ceil(mac_num * (primitive.tensor_in1.mac_width() / 8)
                                                     * (primitive.conv_settings.mac_width() / 8))

    def calculate_energy(self):
        # calculate_energy of ComputeMods should be realized by child class
        raise NotImplementedError()

    def __repr__(self):
        return f"MAC Array of CIMCluster {self.id}, Cycle cost: MAC={self.cycle_cost['mac']}, Latency={self.latency}"


# Module: Compute-in-Memory Cluster
class CIMCluster(ModuleTemp):       # for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)
        self.macarray = CIMMACArray('cimmacarray', self.id, self.parent_core)
        self.bit_width = GlbSet.CIMCluster_Data_Bitwidth
        # Each page is considered as a separate SRAM bank.
        self.pages = [CIMCPage('cimpage', page_num, self.parent_core, self) for page_num in range(GlbSet.CIMM_PG_NUM)]
        cim_page_addr_lmem = GlbSet.CIMCluster_Base_Address_Lmem
        for cur_page in self.pages:
            cur_page.address_space['Addr_Begin'] = (cim_page_addr_lmem << 5)
            cim_page_addr_lmem += GlbSet.CIMPage_Capacity_Lmem
            cur_page.address_space['Addr_End'] = ((cim_page_addr_lmem - 1) << 5)

    # USELESS since using CIMMACArray and CIMCPage to Calculate cycles
    def run_prim(self, primitive):
        # CIM Cluster related primitives: CONV, GEMV and GEMM
        if primitive.type == SIMTemp.PrimName.CONV:
            # Currently, DO NOT need CONV in LLM inference
            raise NotImplementedError('CIM Cluster Running CONV ????')
        elif primitive.type == SIMTemp.PrimName.GEMV:
            # possessed information: dim0 of input vector and output vector
            # which means the shape of matrix: (dim0_in, dim0_out)
            # Calculate cycle cost
            page_read_cycle = 1
            gemv_cycles = primitive.tensor_in1.mac_width()
            primitive.set_end_cycle(module_cycle_cost=page_read_cycle+gemv_cycles)
            # record operations
            mac_operations = primitive.tensor_in1.layout['dim0'] * primitive.tensor_out.layout['dim0']
            mac_precision_normed = primitive.tensor_in1.mac_width() * primitive.tensor_in2.mac_width() / (8*8)
            self.record['MAC_Byte_num'] += mac_operations * mac_precision_normed
            # state transfer
            assert self.state in [ModState.IDLE, ModState.WRITE]
            self.state = ModState.MAC if self.state == ModState.IDLE else ModState.WRITEandMAC
        elif primitive.type == SIMTemp.PrimName.GEMM:
            # possessed information: (dim0, dim1) of input matrix and output matrix
            # which means the shape of matrix: (dim0_in, dim1_out)
            assert primitive.tensor_in1.layout['dim1'] == primitive.tensor_out.layout['dim1']
            # Calculate cycle cost
            page_read_cycle = 1
            gemv_cycles = primitive.tensor_in1.mac_width()
            gemm_cycles = gemv_cycles * primitive.tensor_in1.layout['dim1']
            primitive.set_end_cycle(module_cycle_cost=page_read_cycle + gemm_cycles)
            # record operations
            mac_operations = primitive.tensor_in1.layout['dim1'] * primitive.tensor_in1.layout['dim0'] * \
                             primitive.tensor_out.layout['dim0']
            mac_precision_normed = primitive.tensor_in1.mac_width() * primitive.tensor_in2.mac_width() / (8 * 8)
            self.record['MAC_Byte_num'] += mac_operations * mac_precision_normed
            # state transfer
            assert self.state in [ModState.IDLE, ModState.WRITE]
            self.state = ModState.MAC if self.state == ModState.IDLE else ModState.WRITEandMAC
        elif primitive.type == SIMTemp.PrimName.TST:    # write weights to CIM
            write_cycles = math.ceil(primitive.tensor_out.bits / self.bit_width)
            self.record['Write_Byte_Num'] += primitive.tensor_out.bits // 8
            primitive.set_end_cycle(module_cycle_cost=write_cycles)
            # state transfer
            assert self.state in [ModState.IDLE, ModState.MAC]
            self.state = ModState.WRITE if self.state == ModState.IDLE else ModState.WRITEandMAC

    def calculate_energy(self):
        raise NotImplementedError()

    def __repr__(self):
        # # 逐个打印 self.pages 中的每个实例
        # return "\n".join(repr(page) for page in self.pages)
        return f"{self.__class__.__name__}"


# Module: Manipulation
class ManageMod(ModuleTemp):        # for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)

    def run_prim(self, primitive):
        # TMU related primitives: Broadcast, Tensor Move and Tensor Transpose
        if primitive.type == SIMTemp.PrimName.BC:
            pass
        elif primitive.type == SIMTemp.PrimName.MOV:
            pass
        elif primitive.type == SIMTemp.PrimName.TRANS:
            self.latency = GlbSet.TMU_Trans_Latency
            primitive.set_latency(latency=GlbSet.TMU_Trans_Latency)
        elif primitive.type == SIMTemp.PrimName.NOC or primitive.type == SIMTemp.PrimName.TLD:
            # alignment
            pass
        else:
            self.raise_unsupported_prim(primitive)

        # state transfer
        assert self.state == ModState.IDLE
        self.state = ModState.BUSY

    def calculate_energy(self):
        raise NotImplementedError("ManageMod energy calculation not implemented")

    def __repr__(self):
        return f"{self.__class__.__name__}"


# Module: Manipulation
class LoadStoreUnit(ModuleTemp):        # for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)

    def run_prim(self, primitive):
        # TMU related primitives: Broadcast, Tensor Move and Tensor Transpose
        if primitive.type == SIMTemp.PrimName.TLD:
            pass
        elif primitive.type == SIMTemp.PrimName.TST:
            pass
        elif primitive.type == SIMTemp.PrimName.NOC:
            pass
        else:
            self.raise_unsupported_prim(primitive)

        # state transfer
        assert self.state == ModState.IDLE
        self.state = ModState.BUSY

    def calculate_energy(self):
        raise NotImplementedError("LoadStoreUnit energy calculation not implemented")

    def __repr__(self):
        return f"{self.__class__.__name__}"


# Modules: NoC Router
class NoCRouter(ModuleTemp):        # for NPU Core
    def __init__(self, name, id, parent_core, noc, port_type):
        super().__init__(name, id, parent_core)
        self.op_energy = {'Comm_Byte': GlbSet.NoC_Comm_Byte}
        self.record = {'Comm_Byte_Num': 0.0}
        self.bit_width = GlbSet.NoC_Data_Bitwidth
        self.noc = noc
        self.port_type = port_type
        self.cycle_cost = {'Comm': 0}

    def run_prim(self, primitive):
        # NoC Router related primitives: NoC, NOC_SRC, NOC_DEST
        routing_cycle = 1
        if primitive.type == SIMTemp.PrimName.NOC:
            # Calculate cycle cost
            if self.port_type == 'TX':
                comm_bits = primitive.tensor_in1.bits
            else:
                comm_bits = primitive.tensor_out.bits
            comm_cycles = math.ceil(comm_bits / self.bit_width)
            self.cycle_cost['Comm'] = comm_cycles
            primitive.set_end_cycle(module_cycle_cost=comm_cycles)
            # Calculate latency
            # Only calculate latency for TX, avoid repetitive calculation
            if self.port_type == 'TX':
                self.latency = GlbSet.NoC_Latency
                primitive.set_latency(latency=GlbSet.NoC_Latency)
            # Record operation
            self.record['Comm_Byte_Num'] += comm_bits // 8
        elif primitive.type == SIMTemp.PrimName.NOC_SRC:
            # NOC_SRC: Only TX router handles this
            if self.port_type == 'TX':
                # Use transfer_size from noc_settings
                comm_bits = primitive.noc_settings.transfer_size * 8
                comm_cycles = math.ceil(comm_bits / self.bit_width)
                self.cycle_cost['Comm'] = comm_cycles
                primitive.set_end_cycle(module_cycle_cost=comm_cycles)
                # Set latency
                self.latency = GlbSet.NoC_Latency
                primitive.set_latency(latency=GlbSet.NoC_Latency)
                # Record operation
                self.record['Comm_Byte_Num'] += comm_bits // 8
        elif primitive.type == SIMTemp.PrimName.NOC_DEST:
            # NOC_DEST: Only RX router handles this
            if self.port_type == 'RX':
                # Use transfer_size from noc_settings
                comm_bits = primitive.noc_settings.transfer_size * 8
                comm_cycles = math.ceil(comm_bits / self.bit_width)
                self.cycle_cost['Comm'] = comm_cycles
                primitive.set_end_cycle(module_cycle_cost=comm_cycles)
                # Record operation
                self.record['Comm_Byte_Num'] += comm_bits // 8
        else:
            self.raise_unsupported_prim(primitive)

        # state transfer
        assert self.state == ModState.IDLE
        self.state = ModState.BUSY

    def calculate_energy(self):
        comm_energy = self.op_energy['Comm_Byte'] * self.record['Comm_Byte_Num']
        return comm_energy

    def __repr__(self):
        return f"NPU NoC Router {self.port_type} {self.id}, Cycle cost: Comm={self.cycle_cost['Comm']}, " \
               f"Latency={self.latency}"


# Modules: NPU Controller
class Ctrl(ModuleTemp):        # for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)

    def run_prim(self, primitive):
        if primitive.type == SIMTemp.PrimName.GROUP_MASK:
            group, mask = primitive.npu_group, primitive.npu_mask
            if group == self.parent_core.group and int(format(mask+pow(2, 8), 'b')[::-1][self.parent_core.id]):
                self.parent_core.flag_receive_prim = True
            else:
                self.parent_core.flag_receive_prim = False
            return
        else:
            control_cycle = 1
            primitive.set_end_cycle(module_cycle_cost=control_cycle)

    def calculate_energy(self):
        raise NotImplementedError()

    def __repr__(self):
        return f"{self.__class__.__name__}, id={self.id}"


# Modules: MUX
class MUX(ModuleTemp):        # for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)

    def run_prim(self, primitive):
        raise NotImplementedError("MUX run_prim not implemented")

    def calculate_energy(self):
        raise NotImplementedError("MUX energy calculation not implemented")


# Modules: Xbar
class Xbar(ModuleTemp):        # for NPU Core
    def __init__(self, name, id, parent_core):
        super().__init__(name, id, parent_core)

    def run_prim(self, primitive):
        raise NotImplementedError("Xbar run_prim not implemented")

    def calculate_energy(self):
        raise NotImplementedError("Xbar energy calculation not implemented")
