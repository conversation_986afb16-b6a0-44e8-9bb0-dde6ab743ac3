# * * * * * * * * * * * * * * * * #
# Global Settings
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #


import math
from enum import Enum


class SI(Enum):
    Kilo = 10e3
    Mega = 10e6
    Giga = 10e9
    Tera = 10e12


class SIBinary(Enum):
    <PERSON><PERSON> = pow(2, 10)
    Mega = pow(2, 20)
    Giga = pow(2, 30)


FP16_Exponent_Width = 5
FP16_Mantissa_Width = 10
FP32_Exponent_Width = 8
FP32_Mantissa_Width = 23
BF16_Exponent_Width = 8
BF16_Mantissa_Width = 7
BBF16_Mantissa_Width = 14

# ISA Design Parameters Begin
GROUP_NUM, CORE_NUM = 4, 4          # SoC
GMEM_WD, GMEM_ADDR_WD = 256, 32     # GMEM
LMEM_WD, LMEM_ADDR_WD, LMEM_INDEX_ADDR_WD, LMEM_OFFSET_ADDR_WD = 256, 32, 8, 15         # LMEM
SPAD_NUM, <PERSON><PERSON>_WD, SPAD_DP, SPAD_ADDR_WD = 4, 256, 2048, 11                             # Scratchpad
CIMC_NUM, CIMM_PG_NUM, CIMC_CIME_NUM, CIME_ROW_NUM, CIME_COL_NUM = 1, 16, 4, 64, 256    # CIM Cluster
# ISA Design Parameters End


GROUP_Num_per_Die = GROUP_NUM
NPU_Num_per_Group = CORE_NUM

# Performance Settings
# Format1: Module_per_upperModule
# Format2: Module_Perf(_Unit)
# Note1: Here, "Bandwidth" refers to the data width transported in each cycle
System_Frequency = 800 * SI.Mega.value
DRAM_Frequency = 400 * SI.Mega.value

DRAMBank_per_NPUCore = 1
DRAMBank_Data_Bitwidth = math.floor(GMEM_WD * DRAM_Frequency / System_Frequency)
DRAMBank_Base_Address_Byte = 0x1000_0000
DRAMBank_Capacity_Byte = 128 * SIBinary.Mega.value

SRAMBank_per_NPUCore = SPAD_NUM
SRAMBank_Data_Bitwidth = SPAD_WD
SRAMBank_Depth = SPAD_DP
SRAMBank_Address_Width = SPAD_ADDR_WD
SRAMBank_Base_Address_Byte = 0x0000_0000
SRAMBank_Base_Address_Lmem = 0x0000_0000
SRAMBank_Capacity_Byte = SPAD_WD * SPAD_DP // 8
SRAMBank_Capacity_Lmem = SPAD_WD * SPAD_DP // LMEM_WD

CIMCluster_per_NPUCore = CIMC_NUM
CIMCluster_Data_Bitwidth = CIME_COL_NUM
CIMCluster_Base_Address_Byte = (SPAD_NUM << 20)
CIMCluster_Base_Address_Lmem = (SPAD_NUM << 15)
CIMPage_Capacity_Byte = CIMC_CIME_NUM * CIME_ROW_NUM * CIME_COL_NUM // 8
CIMPage_Capacity_Lmem = CIMC_CIME_NUM * CIME_ROW_NUM * CIME_COL_NUM // LMEM_WD

TensorLoadStore_per_NPUCore = 1
TensorManipulate_per_NPUCore = 1
VPU_per_NPUCore = 1
VPE_per_VPU = 16
NoCRouter_per_NPUCore = 1

VPE_INPUT_Byte = 4                           # 32 bit input1 and input2
DWEngine_Num_Byte = 0.00                     # ??
CIMCluster_MAC_Byte = 0.00                   # ??

NoC_Data_Bitwidth = 128
NoC_Num_Channel = 1

# v0.4 Add Latency
LSU_Latency = 4 + 2 + 1
BurstRead_Latency = math.ceil(20 * System_Frequency / DRAM_Frequency)
BurstWrite_Latency = math.ceil(20 * System_Frequency / DRAM_Frequency)
MAX_Burst_Length = 64
MAX_Burst_Size = MAX_Burst_Length * GMEM_WD
MPU_InputAlign_Latency = 8
MPU_PsumProcess_Latency = 4
MPU_PostProcess_Latency = 4
TMU_Trans_Latency = 64
NoC_Latency = 60

# Energy Settings
# Format: Module_Operation_Unit
# Energy Unit: pico joule
DRAMBank_Read_Byte = 0.8 * 8                 # 0.8pJ per bit
DRAMBank_Write_Byte = 0.8 * 8                # 0.8pJ per bit
# DRAMBank_Leak = 0.0                        # Leakage

SRAMBank_Read_Byte = 0.05 * 8                # 50fJ per bit
SRAMBank_Write_Byte = 0.05 * 8               # 50fJ per bit
# SRAMBank_Leak = 0.0                        # Leakage

VPU_Add_Byte = 0.00                          # ??
VPU_Mul_Byte = 0.00                          # ??
VPU_Logic_Byte = 0.00                        # ??

DWEngine_Add_Byte = 0.00                     # Depthwise Engine
DWEngine_Mul_Byte = 0.00                     # Depthwise Engine

CIMC_Read_Byte = 0.00                        # ??
CIMC_Write_Byte = 0.00                       # ??
CIMC_MAC_Byte = 1/10                         # 10TOPS/W

NoC_Comm_Byte = 4 * 8                        # 4pJ per bit for Mesh Hop

# DDR Refresh Settings (cycles-based)
# Enable to model periodic DDR refresh where DRAM is unavailable during a refresh window
DDR_REFRESH_ENABLE = True                    # Disabled by default
DDR_REFRESH_PERIOD = 100000                   # M: period in simulator cycles
DDR_REFRESH_WINDOW = 780                      # N: refresh busy window in cycles (N <= M)
DDR_REFRESH_MODE = 'entry'                    # 'entry' (stall only if access starts in window) or 'strict'
DDR_REFRESH_LOG_NOC = False                   # Enable NOC_SRC/NOC_DEST logging (disabled by default)

