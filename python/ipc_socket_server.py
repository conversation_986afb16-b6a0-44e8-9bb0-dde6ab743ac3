import socket
import os
import threading

from ipc_message import Ipc<PERSON><PERSON><PERSON>, IPC_REQUEST_HEADER_LENGTH

class IpcSocketServer:
    def __init__(self, name: str, handler):
        self.name = name
        self.handler = handler

        self.running = False
        self.t = None

        try:
            os.unlink(self.name)
        except OSError:
            if os.path.exists(self.name):
                raise

        # Create the Unix socket server
        self.server = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)

        # Bind the socket to the path
        self.server.bind(self.name)

        # Listen for incoming connections
        self.server.listen(1)

        self.connection = None

    def run(self):
        while self.running:
            print(f"Server '{self.name}' is listening for incoming connections...")
            connection, client_address = self.server.accept()
            self.connection = connection

            try:
                print('Connection from', str(connection).split(", ")[0][-4:])

                # receive data from the client
                while self.running:
                    req_headers = connection.recv(IPC_REQUEST_HEADER_LENGTH)
                    if not req_headers:
                        break

                    req = IpcRequest()
                    req.header_from_bytes(req_headers)

                    # print("----------------------------------------------------")
                    # print(f"ipcSocketServer receive request header: {req_headers.hex()}")

                    if req.length > 0:
                        req.data = connection.recv(req.length)
                        if not req.data:
                            break

                        # print(f"ipcSocketServer receive request data: {req.data.hex()}")

                    resp = self.handler.handle(req)
                    # Send a response back to the client
                    resp_headers = resp.header_to_bytes()
                    connection.sendall(resp_headers)
                    if resp.length > 0:
                        connection.sendall(resp.data)
            finally:
                # close the connection
                connection.close()
                self.connection = None

    def start(self):
        self.running = True
        self.t = threading.Thread(target=self.run, daemon=True)
        self.t.start()

    def stop(self):
        self.running = False

        try:
            if (self.connection):
                self.connection.close()
        except Exception as e:
            print(f"IpcSocketServer close connection fail! {e}")

        self.connection = None

        try:
            os.unlink(self.name)
        except OSError:
            pass