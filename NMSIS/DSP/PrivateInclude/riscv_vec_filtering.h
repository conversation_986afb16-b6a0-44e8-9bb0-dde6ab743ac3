/******************************************************************************
 * @file     riscv_vec_filtering.h
 * @brief    Private header file for NMSIS DSP Library
 * @version  V1.7.0
 * @date     30. October 2019
 ******************************************************************************/
/*
 * Copyright (c) 2010-2019 Arm Limited or its affiliates. All rights reserved.
 * Copyright (c) 2019 Nuclei Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _RISCV_VEC_FILTERING_H_
#define _RISCV_VEC_FILTERING_H_

#include "riscv_math.h"
#include "riscv_helium_utils.h"

#ifdef   __cplusplus
extern "C"
{
#endif



#ifdef   __cplusplus
}
#endif


#endif /* _RISCV_VEC_FILTERING_H_ */
