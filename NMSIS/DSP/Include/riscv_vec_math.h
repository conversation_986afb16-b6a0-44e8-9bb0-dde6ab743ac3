/******************************************************************************
 * @file     riscv_vec_math.h
 * @brief    Public header file for NMSIS DSP Library
 * @version  V1.10.0
 * @date     08 July 2021
 * Target Processor: RISC-V Cores
 ******************************************************************************/
/*
 * Copyright (c) 2010-2021 Arm Limited or its affiliates. All rights reserved.
 * Copyright (c) 2019 Nuclei Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef RISCV_VEC_MATH_H
#define RISCV_VEC_MATH_H

#include "riscv_math_types.h"
#include "riscv_common_tables.h"
#include "riscv_helium_utils.h"

#if defined (RISCV_FLOAT16_SUPPORTED)
#include "riscv_vec_math_f16.h"
#endif /* defined (RISCV_FLOAT16_SUPPORTED) */

#ifdef   __cplusplus
extern "C"
{
#endif




#ifdef   __cplusplus
}
#endif


#endif /* _RISCV_VEC_MATH_H */

/**
 *
 * End of file.
 */
