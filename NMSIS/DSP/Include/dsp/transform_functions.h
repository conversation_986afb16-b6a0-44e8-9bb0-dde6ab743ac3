/******************************************************************************
 * @file     transform_functions.h
 * @brief    Public header file for NMSIS DSP Library
 * @version  V1.10.0
 * @date     08 July 2021
 * Target Processor: RISC-V Cores
 ******************************************************************************/
/*
 * Copyright (c) 2010-2020 Arm Limited or its affiliates. All rights reserved.
 * Copyright (c) 2019 Nuclei Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

 
#ifndef TRANSFORM_FUNCTIONS_H_
#define TRANSFORM_FUNCTIONS_H_

#include "riscv_math_types.h"
#include "riscv_math_memory.h"

#include "dsp/none.h"
#include "dsp/utils.h"

#include "dsp/basic_math_functions.h"
#include "dsp/complex_math_functions.h"

#ifdef   __cplusplus
extern "C"
{
#endif


/**
 * @defgroup groupTransforms Transform Functions
 */


  /**
   * @brief Instance structure for the Q15 CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                 /**< length of the FFT. */
          uint8_t ifftFlag;                /**< flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. */
          uint8_t bitReverseFlag;          /**< flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. */
    const q15_t *pTwiddle;                 /**< points to the Sin twiddle factor table. */
    const uint16_t *pBitRevTable;          /**< points to the bit reversal table. */
          uint16_t twidCoefModifier;       /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
          uint16_t bitRevFactor;           /**< bit reversal modifier that supports different size FFTs with the same bit reversal table. */
  } riscv_cfft_radix2_instance_q15;

/* Deprecated */
  riscv_status riscv_cfft_radix2_init_q15(
        riscv_cfft_radix2_instance_q15 * S,
        uint16_t fftLen,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);

/* Deprecated */
  void riscv_cfft_radix2_q15(
  const riscv_cfft_radix2_instance_q15 * S,
        q15_t * pSrc);


  /**
   * @brief Instance structure for the Q15 CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                 /**< length of the FFT. */
          uint8_t ifftFlag;                /**< flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. */
          uint8_t bitReverseFlag;          /**< flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. */
    const q15_t *pTwiddle;                 /**< points to the twiddle factor table. */
    const uint16_t *pBitRevTable;          /**< points to the bit reversal table. */
          uint16_t twidCoefModifier;       /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
          uint16_t bitRevFactor;           /**< bit reversal modifier that supports different size FFTs with the same bit reversal table. */
  } riscv_cfft_radix4_instance_q15;

/* Deprecated */
  riscv_status riscv_cfft_radix4_init_q15(
        riscv_cfft_radix4_instance_q15 * S,
        uint16_t fftLen,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);

/* Deprecated */
  void riscv_cfft_radix4_q15(
  const riscv_cfft_radix4_instance_q15 * S,
        q15_t * pSrc);

  /**
   * @brief Instance structure for the Radix-2 Q31 CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                 /**< length of the FFT. */
          uint8_t ifftFlag;                /**< flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. */
          uint8_t bitReverseFlag;          /**< flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. */
    const q31_t *pTwiddle;                 /**< points to the Twiddle factor table. */
    const uint16_t *pBitRevTable;          /**< points to the bit reversal table. */
          uint16_t twidCoefModifier;       /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
          uint16_t bitRevFactor;           /**< bit reversal modifier that supports different size FFTs with the same bit reversal table. */
  } riscv_cfft_radix2_instance_q31;

/* Deprecated */
  riscv_status riscv_cfft_radix2_init_q31(
        riscv_cfft_radix2_instance_q31 * S,
        uint16_t fftLen,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);

/* Deprecated */
  void riscv_cfft_radix2_q31(
  const riscv_cfft_radix2_instance_q31 * S,
        q31_t * pSrc);

  /**
   * @brief Instance structure for the Q31 CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                 /**< length of the FFT. */
          uint8_t ifftFlag;                /**< flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. */
          uint8_t bitReverseFlag;          /**< flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. */
    const q31_t *pTwiddle;                 /**< points to the twiddle factor table. */
    const uint16_t *pBitRevTable;          /**< points to the bit reversal table. */
          uint16_t twidCoefModifier;       /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
          uint16_t bitRevFactor;           /**< bit reversal modifier that supports different size FFTs with the same bit reversal table. */
  } riscv_cfft_radix4_instance_q31;

/* Deprecated */
  void riscv_cfft_radix4_q31(
  const riscv_cfft_radix4_instance_q31 * S,
        q31_t * pSrc);

/* Deprecated */
  riscv_status riscv_cfft_radix4_init_q31(
        riscv_cfft_radix4_instance_q31 * S,
        uint16_t fftLen,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);

  /**
   * @brief Instance structure for the floating-point CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                   /**< length of the FFT. */
          uint8_t ifftFlag;                  /**< flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. */
          uint8_t bitReverseFlag;            /**< flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. */
    const float32_t *pTwiddle;               /**< points to the Twiddle factor table. */
    const uint16_t *pBitRevTable;            /**< points to the bit reversal table. */
          uint16_t twidCoefModifier;         /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
          uint16_t bitRevFactor;             /**< bit reversal modifier that supports different size FFTs with the same bit reversal table. */
          float32_t onebyfftLen;             /**< value of 1/fftLen. */
  } riscv_cfft_radix2_instance_f32;


/* Deprecated */
  riscv_status riscv_cfft_radix2_init_f32(
        riscv_cfft_radix2_instance_f32 * S,
        uint16_t fftLen,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);

/* Deprecated */
  void riscv_cfft_radix2_f32(
  const riscv_cfft_radix2_instance_f32 * S,
        float32_t * pSrc);

  /**
   * @brief Instance structure for the floating-point CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                   /**< length of the FFT. */
          uint8_t ifftFlag;                  /**< flag that selects forward (ifftFlag=0) or inverse (ifftFlag=1) transform. */
          uint8_t bitReverseFlag;            /**< flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. */
    const float32_t *pTwiddle;               /**< points to the Twiddle factor table. */
    const uint16_t *pBitRevTable;            /**< points to the bit reversal table. */
          uint16_t twidCoefModifier;         /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
          uint16_t bitRevFactor;             /**< bit reversal modifier that supports different size FFTs with the same bit reversal table. */
          float32_t onebyfftLen;             /**< value of 1/fftLen. */
  } riscv_cfft_radix4_instance_f32;



/* Deprecated */
  riscv_status riscv_cfft_radix4_init_f32(
        riscv_cfft_radix4_instance_f32 * S,
        uint16_t fftLen,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);

/* Deprecated */
  void riscv_cfft_radix4_f32(
  const riscv_cfft_radix4_instance_f32 * S,
        float32_t * pSrc);

  /**
   * @brief Instance structure for the fixed-point CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                   /**< length of the FFT. */
    const q15_t *pTwiddle;             /**< points to the Twiddle factor table. */
    const uint16_t *pBitRevTable;      /**< points to the bit reversal table. */
          uint16_t bitRevLength;             /**< bit reversal table length. */
  } riscv_cfft_instance_q15;

riscv_status riscv_cfft_init_4096_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_2048_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_1024_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_512_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_256_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_128_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_64_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_32_q15(riscv_cfft_instance_q15 * S);
riscv_status riscv_cfft_init_16_q15(riscv_cfft_instance_q15 * S);

riscv_status riscv_cfft_init_q15(
  riscv_cfft_instance_q15 * S,
  uint16_t fftLen);

void riscv_cfft_q15(
    const riscv_cfft_instance_q15 * S,
          q15_t * p1,
          uint8_t ifftFlag,
          uint8_t bitReverseFlag);

  /**
   * @brief Instance structure for the fixed-point CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                   /**< length of the FFT. */
    const q31_t *pTwiddle;             /**< points to the Twiddle factor table. */
    const uint16_t *pBitRevTable;      /**< points to the bit reversal table. */
          uint16_t bitRevLength;             /**< bit reversal table length. */
  } riscv_cfft_instance_q31;

riscv_status riscv_cfft_init_4096_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_2048_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_1024_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_512_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_256_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_128_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_64_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_32_q31(riscv_cfft_instance_q31 * S);
riscv_status riscv_cfft_init_16_q31(riscv_cfft_instance_q31 * S);

riscv_status riscv_cfft_init_q31(
  riscv_cfft_instance_q31 * S,
  uint16_t fftLen);

void riscv_cfft_q31(
    const riscv_cfft_instance_q31 * S,
          q31_t * p1,
          uint8_t ifftFlag,
          uint8_t bitReverseFlag);

  /**
   * @brief Instance structure for the floating-point CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                   /**< length of the FFT. */
    const float32_t *pTwiddle;         /**< points to the Twiddle factor table. */
    const uint16_t *pBitRevTable;      /**< points to the bit reversal table. */
          uint16_t bitRevLength;             /**< bit reversal table length. */
  } riscv_cfft_instance_f32;


riscv_status riscv_cfft_init_4096_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_2048_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_1024_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_512_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_256_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_128_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_64_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_32_f32(riscv_cfft_instance_f32 * S);
riscv_status riscv_cfft_init_16_f32(riscv_cfft_instance_f32 * S);

  riscv_status riscv_cfft_init_f32(
  riscv_cfft_instance_f32 * S,
  uint16_t fftLen);

  void riscv_cfft_f32(
  const riscv_cfft_instance_f32 * S,
        float32_t * p1,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);


  /**
   * @brief Instance structure for the Double Precision Floating-point CFFT/CIFFT function.
   */
  typedef struct
  {
          uint16_t fftLen;                   /**< length of the FFT. */
    const float64_t *pTwiddle;         /**< points to the Twiddle factor table. */
    const uint16_t *pBitRevTable;      /**< points to the bit reversal table. */
          uint16_t bitRevLength;             /**< bit reversal table length. */
  } riscv_cfft_instance_f64;

riscv_status riscv_cfft_init_4096_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_2048_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_1024_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_512_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_256_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_128_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_64_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_32_f64(riscv_cfft_instance_f64 * S);
riscv_status riscv_cfft_init_16_f64(riscv_cfft_instance_f64 * S);

  riscv_status riscv_cfft_init_f64(
  riscv_cfft_instance_f64 * S,
  uint16_t fftLen);
  
  void riscv_cfft_f64(
  const riscv_cfft_instance_f64 * S,
        float64_t * p1,
        uint8_t ifftFlag,
        uint8_t bitReverseFlag);

  /**
   * @brief Instance structure for the Q15 RFFT/RIFFT function.
   */
  typedef struct
  {
          uint32_t fftLenReal;                      /**< length of the real FFT. */
          uint8_t ifftFlagR;                        /**< flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform. */
          uint8_t bitReverseFlagR;                  /**< flag that enables (bitReverseFlagR=1) or disables (bitReverseFlagR=0) bit reversal of output. */
          uint32_t twidCoefRModifier;               /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
    const q15_t *pTwiddleAReal;                     /**< points to the real twiddle factor table. */
    const q15_t *pTwiddleBReal;                     /**< points to the imag twiddle factor table. */
    const riscv_cfft_instance_q15 *pCfft;       /**< points to the complex FFT instance. */
  } riscv_rfft_instance_q15;

riscv_status riscv_rfft_init_32_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_64_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_128_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_256_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_512_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_1024_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_2048_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_4096_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

riscv_status riscv_rfft_init_8192_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_q15(
        riscv_rfft_instance_q15 * S,
        uint32_t fftLenReal,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  void riscv_rfft_q15(
  const riscv_rfft_instance_q15 * S,
        q15_t * pSrc,
        q15_t * pDst);

  /**
   * @brief Instance structure for the Q31 RFFT/RIFFT function.
   */
  typedef struct
  {
          uint32_t fftLenReal;                        /**< length of the real FFT. */
          uint8_t ifftFlagR;                          /**< flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform. */
          uint8_t bitReverseFlagR;                    /**< flag that enables (bitReverseFlagR=1) or disables (bitReverseFlagR=0) bit reversal of output. */
          uint32_t twidCoefRModifier;                 /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
    const q31_t *pTwiddleAReal;                       /**< points to the real twiddle factor table. */
    const q31_t *pTwiddleBReal;                       /**< points to the imag twiddle factor table. */
    const riscv_cfft_instance_q31 *pCfft;         /**< points to the complex FFT instance. */
  } riscv_rfft_instance_q31;

  riscv_status riscv_rfft_init_32_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_64_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_128_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_256_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_512_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_1024_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_2048_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_4096_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_8192_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  riscv_status riscv_rfft_init_q31(
        riscv_rfft_instance_q31 * S,
        uint32_t fftLenReal,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  void riscv_rfft_q31(
  const riscv_rfft_instance_q31 * S,
        q31_t * pSrc,
        q31_t * pDst);

  /**
   * @brief Instance structure for the floating-point RFFT/RIFFT function.
   */
  typedef struct
  {
          uint32_t fftLenReal;                        /**< length of the real FFT. */
          uint16_t fftLenBy2;                         /**< length of the complex FFT. */
          uint8_t ifftFlagR;                          /**< flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform. */
          uint8_t bitReverseFlagR;                    /**< flag that enables (bitReverseFlagR=1) or disables (bitReverseFlagR=0) bit reversal of output. */
          uint32_t twidCoefRModifier;                     /**< twiddle coefficient modifier that supports different size FFTs with the same twiddle factor table. */
    const float32_t *pTwiddleAReal;                   /**< points to the real twiddle factor table. */
    const float32_t *pTwiddleBReal;                   /**< points to the imag twiddle factor table. */
          riscv_cfft_radix4_instance_f32 *pCfft;        /**< points to the complex FFT instance. */
  } riscv_rfft_instance_f32;

  riscv_status riscv_rfft_init_f32(
        riscv_rfft_instance_f32 * S,
        riscv_cfft_radix4_instance_f32 * S_CFFT,
        uint32_t fftLenReal,
        uint32_t ifftFlagR,
        uint32_t bitReverseFlag);

  void riscv_rfft_f32(
  const riscv_rfft_instance_f32 * S,
        float32_t * pSrc,
        float32_t * pDst);

  /**
   * @brief Instance structure for the Double Precision Floating-point RFFT/RIFFT function.
   */
typedef struct
  {
          riscv_cfft_instance_f64 Sint;      /**< Internal CFFT structure. */
          uint16_t fftLenRFFT;             /**< length of the real sequence */
    const float64_t * pTwiddleRFFT;        /**< Twiddle factors real stage  */
  } riscv_rfft_fast_instance_f64 ;

riscv_status riscv_rfft_fast_init_32_f64( riscv_rfft_fast_instance_f64 * S );
riscv_status riscv_rfft_fast_init_64_f64( riscv_rfft_fast_instance_f64 * S );
riscv_status riscv_rfft_fast_init_128_f64( riscv_rfft_fast_instance_f64 * S );
riscv_status riscv_rfft_fast_init_256_f64( riscv_rfft_fast_instance_f64 * S );
riscv_status riscv_rfft_fast_init_512_f64( riscv_rfft_fast_instance_f64 * S );
riscv_status riscv_rfft_fast_init_1024_f64( riscv_rfft_fast_instance_f64 * S );
riscv_status riscv_rfft_fast_init_2048_f64( riscv_rfft_fast_instance_f64 * S );
riscv_status riscv_rfft_fast_init_4096_f64( riscv_rfft_fast_instance_f64 * S );

riscv_status riscv_rfft_fast_init_f64 (
         riscv_rfft_fast_instance_f64 * S,
         uint16_t fftLen);


void riscv_rfft_fast_f64(
    riscv_rfft_fast_instance_f64 * S,
    float64_t * p, float64_t * pOut,
    uint8_t ifftFlag);


  /**
   * @brief Instance structure for the floating-point RFFT/RIFFT function.
   */
typedef struct
  {
          riscv_cfft_instance_f32 Sint;      /**< Internal CFFT structure. */
          uint16_t fftLenRFFT;             /**< length of the real sequence */
    const float32_t * pTwiddleRFFT;        /**< Twiddle factors real stage  */
  } riscv_rfft_fast_instance_f32 ;

riscv_status riscv_rfft_fast_init_32_f32( riscv_rfft_fast_instance_f32 * S );
riscv_status riscv_rfft_fast_init_64_f32( riscv_rfft_fast_instance_f32 * S );
riscv_status riscv_rfft_fast_init_128_f32( riscv_rfft_fast_instance_f32 * S );
riscv_status riscv_rfft_fast_init_256_f32( riscv_rfft_fast_instance_f32 * S );
riscv_status riscv_rfft_fast_init_512_f32( riscv_rfft_fast_instance_f32 * S );
riscv_status riscv_rfft_fast_init_1024_f32( riscv_rfft_fast_instance_f32 * S );
riscv_status riscv_rfft_fast_init_2048_f32( riscv_rfft_fast_instance_f32 * S );
riscv_status riscv_rfft_fast_init_4096_f32( riscv_rfft_fast_instance_f32 * S );

riscv_status riscv_rfft_fast_init_f32 (
         riscv_rfft_fast_instance_f32 * S,
         uint16_t fftLen);


  void riscv_rfft_fast_f32(
        const riscv_rfft_fast_instance_f32 * S,
        float32_t * p, float32_t * pOut,
        uint8_t ifftFlag);

  /**
   * @brief Instance structure for the floating-point DCT4/IDCT4 function.
   */
  typedef struct
  {
          uint16_t N;                          /**< length of the DCT4. */
          uint16_t Nby2;                       /**< half of the length of the DCT4. */
          float32_t normalize;                 /**< normalizing factor. */
    const float32_t *pTwiddle;                 /**< points to the twiddle factor table. */
    const float32_t *pCosFactor;               /**< points to the cosFactor table. */
          riscv_rfft_instance_f32 *pRfft;        /**< points to the real FFT instance. */
          riscv_cfft_radix4_instance_f32 *pCfft; /**< points to the complex FFT instance. */
  } riscv_dct4_instance_f32;


  /**
   * @brief  Initialization function for the floating-point DCT4/IDCT4.
   * @param[in,out] S          points to an instance of floating-point DCT4/IDCT4 structure.
   * @param[in]     S_RFFT     points to an instance of floating-point RFFT/RIFFT structure.
   * @param[in]     S_CFFT     points to an instance of floating-point CFFT/CIFFT structure.
   * @param[in]     N          length of the DCT4.
   * @param[in]     Nby2       half of the length of the DCT4.
   * @param[in]     normalize  normalizing factor.
   * @return      riscv_status function returns RISCV_MATH_SUCCESS if initialization is successful or RISCV_MATH_ARGUMENT_ERROR if <code>fftLenReal</code> is not a supported transform length.
   */
  riscv_status riscv_dct4_init_f32(
        riscv_dct4_instance_f32 * S,
        riscv_rfft_instance_f32 * S_RFFT,
        riscv_cfft_radix4_instance_f32 * S_CFFT,
        uint16_t N,
        uint16_t Nby2,
        float32_t normalize);


  /**
   * @brief Processing function for the floating-point DCT4/IDCT4.
   * @param[in]     S              points to an instance of the floating-point DCT4/IDCT4 structure.
   * @param[in]     pState         points to state buffer.
   * @param[in,out] pInlineBuffer  points to the in-place input and output buffer.
   */
  void riscv_dct4_f32(
  const riscv_dct4_instance_f32 * S,
        float32_t * pState,
        float32_t * pInlineBuffer);


  /**
   * @brief Instance structure for the Q31 DCT4/IDCT4 function.
   */
  typedef struct
  {
          uint16_t N;                          /**< length of the DCT4. */
          uint16_t Nby2;                       /**< half of the length of the DCT4. */
          q31_t normalize;                     /**< normalizing factor. */
    const q31_t *pTwiddle;                     /**< points to the twiddle factor table. */
    const q31_t *pCosFactor;                   /**< points to the cosFactor table. */
          riscv_rfft_instance_q31 *pRfft;        /**< points to the real FFT instance. */
          riscv_cfft_radix4_instance_q31 *pCfft; /**< points to the complex FFT instance. */
  } riscv_dct4_instance_q31;


  /**
   * @brief  Initialization function for the Q31 DCT4/IDCT4.
   * @param[in,out] S          points to an instance of Q31 DCT4/IDCT4 structure.
   * @param[in]     S_RFFT     points to an instance of Q31 RFFT/RIFFT structure
   * @param[in]     S_CFFT     points to an instance of Q31 CFFT/CIFFT structure
   * @param[in]     N          length of the DCT4.
   * @param[in]     Nby2       half of the length of the DCT4.
   * @param[in]     normalize  normalizing factor.
   * @return      riscv_status function returns RISCV_MATH_SUCCESS if initialization is successful or RISCV_MATH_ARGUMENT_ERROR if <code>N</code> is not a supported transform length.
   */
  riscv_status riscv_dct4_init_q31(
        riscv_dct4_instance_q31 * S,
        riscv_rfft_instance_q31 * S_RFFT,
        riscv_cfft_radix4_instance_q31 * S_CFFT,
        uint16_t N,
        uint16_t Nby2,
        q31_t normalize);


  /**
   * @brief Processing function for the Q31 DCT4/IDCT4.
   * @param[in]     S              points to an instance of the Q31 DCT4 structure.
   * @param[in]     pState         points to state buffer.
   * @param[in,out] pInlineBuffer  points to the in-place input and output buffer.
   */
  void riscv_dct4_q31(
  const riscv_dct4_instance_q31 * S,
        q31_t * pState,
        q31_t * pInlineBuffer);


  /**
   * @brief Instance structure for the Q15 DCT4/IDCT4 function.
   */
  typedef struct
  {
          uint16_t N;                          /**< length of the DCT4. */
          uint16_t Nby2;                       /**< half of the length of the DCT4. */
          q15_t normalize;                     /**< normalizing factor. */
    const q15_t *pTwiddle;                     /**< points to the twiddle factor table. */
    const q15_t *pCosFactor;                   /**< points to the cosFactor table. */
          riscv_rfft_instance_q15 *pRfft;        /**< points to the real FFT instance. */
          riscv_cfft_radix4_instance_q15 *pCfft; /**< points to the complex FFT instance. */
  } riscv_dct4_instance_q15;


  /**
   * @brief  Initialization function for the Q15 DCT4/IDCT4.
   * @param[in,out] S          points to an instance of Q15 DCT4/IDCT4 structure.
   * @param[in]     S_RFFT     points to an instance of Q15 RFFT/RIFFT structure.
   * @param[in]     S_CFFT     points to an instance of Q15 CFFT/CIFFT structure.
   * @param[in]     N          length of the DCT4.
   * @param[in]     Nby2       half of the length of the DCT4.
   * @param[in]     normalize  normalizing factor.
   * @return      riscv_status function returns RISCV_MATH_SUCCESS if initialization is successful or RISCV_MATH_ARGUMENT_ERROR if <code>N</code> is not a supported transform length.
   */
  riscv_status riscv_dct4_init_q15(
        riscv_dct4_instance_q15 * S,
        riscv_rfft_instance_q15 * S_RFFT,
        riscv_cfft_radix4_instance_q15 * S_CFFT,
        uint16_t N,
        uint16_t Nby2,
        q15_t normalize);


  /**
   * @brief Processing function for the Q15 DCT4/IDCT4.
   * @param[in]     S              points to an instance of the Q15 DCT4 structure.
   * @param[in]     pState         points to state buffer.
   * @param[in,out] pInlineBuffer  points to the in-place input and output buffer.
   */
  void riscv_dct4_q15(
  const riscv_dct4_instance_q15 * S,
        q15_t * pState,
        q15_t * pInlineBuffer);

  /**
   * @brief Instance structure for the Floating-point MFCC function.
   */
typedef struct
  {
     const float32_t *dctCoefs; /**< Internal DCT coefficients */
     const float32_t *filterCoefs; /**< Internal Mel filter coefficients */ 
     const float32_t *windowCoefs; /**< Windowing coefficients */ 
     const uint32_t *filterPos; /**< Internal Mel filter positions in spectrum */ 
     const uint32_t *filterLengths; /**< Internal Mel filter  lengths */ 
     uint32_t fftLen; /**< FFT length */
     uint32_t nbMelFilters; /**< Number of Mel filters */
     uint32_t nbDctOutputs; /**< Number of DCT outputs */
#if defined(RISCV_MFCC_CFFT_BASED)
     /* Implementation of the MFCC is using a CFFT */
     riscv_cfft_instance_f32 cfft; /**< Internal CFFT instance */
#else
     /* Implementation of the MFCC is using a RFFT (default) */
     riscv_rfft_fast_instance_f32 rfft;
#endif
  } riscv_mfcc_instance_f32 ;

riscv_status riscv_mfcc_init_32_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_64_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_128_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_256_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_512_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_1024_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_2048_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_4096_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );

riscv_status riscv_mfcc_init_f32(
  riscv_mfcc_instance_f32 * S,
  uint32_t fftLen,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const float32_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const float32_t *filterCoefs,
  const float32_t *windowCoefs
  );


/**
  @brief         MFCC F32
  @param[in]    S       points to the mfcc instance structure
  @param[in]     pSrc points to the input samples
  @param[out]     pDst  points to the output MFCC values
  @param[inout]     pTmp  points to a temporary buffer of complex
 */
  void riscv_mfcc_f32(
  const riscv_mfcc_instance_f32 * S,
  float32_t *pSrc,
  float32_t *pDst,
  float32_t *pTmp
  );

 /**
   * @brief Instance structure for the Q31 MFCC function.
   */
typedef struct
  {
     const q31_t *dctCoefs; /**< Internal DCT coefficients */
     const q31_t *filterCoefs; /**< Internal Mel filter coefficients */ 
     const q31_t *windowCoefs; /**< Windowing coefficients */ 
     const uint32_t *filterPos; /**< Internal Mel filter positions in spectrum */ 
     const uint32_t *filterLengths; /**< Internal Mel filter  lengths */ 
     uint32_t fftLen; /**< FFT length */
     uint32_t nbMelFilters; /**< Number of Mel filters */
     uint32_t nbDctOutputs; /**< Number of DCT outputs */
#if defined(RISCV_MFCC_CFFT_BASED)
     /* Implementation of the MFCC is using a CFFT */
     riscv_cfft_instance_q31 cfft; /**< Internal CFFT instance */
#else
     /* Implementation of the MFCC is using a RFFT (default) */
     riscv_rfft_instance_q31 rfft;
#endif
  } riscv_mfcc_instance_q31 ;

riscv_status riscv_mfcc_init_32_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_64_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_128_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_256_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_512_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_1024_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_2048_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_4096_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );

riscv_status riscv_mfcc_init_q31(
  riscv_mfcc_instance_q31 * S,
  uint32_t fftLen,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q31_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q31_t *filterCoefs,
  const q31_t *windowCoefs
  );


/**
  @brief         MFCC Q31
  @param[in]    S       points to the mfcc instance structure
  @param[in]     pSrc points to the input samples
  @param[out]     pDst  points to the output MFCC values
  @param[inout]     pTmp  points to a temporary buffer of complex
  @return        error status
 */
  riscv_status riscv_mfcc_q31(
  const riscv_mfcc_instance_q31 * S,
  q31_t *pSrc,
  q31_t *pDst,
  q31_t *pTmp
  );

 /**
   * @brief Instance structure for the Q15 MFCC function.
   */
typedef struct
  {
     const q15_t *dctCoefs; /**< Internal DCT coefficients */
     const q15_t *filterCoefs; /**< Internal Mel filter coefficients */ 
     const q15_t *windowCoefs; /**< Windowing coefficients */ 
     const uint32_t *filterPos; /**< Internal Mel filter positions in spectrum */ 
     const uint32_t *filterLengths; /**< Internal Mel filter  lengths */ 
     uint32_t fftLen; /**< FFT length */
     uint32_t nbMelFilters; /**< Number of Mel filters */
     uint32_t nbDctOutputs; /**< Number of DCT outputs */
#if defined(RISCV_MFCC_CFFT_BASED)
     /* Implementation of the MFCC is using a CFFT */
     riscv_cfft_instance_q15 cfft; /**< Internal CFFT instance */
#else
     /* Implementation of the MFCC is using a RFFT (default) */
     riscv_rfft_instance_q15 rfft;
#endif
  } riscv_mfcc_instance_q15 ;

riscv_status riscv_mfcc_init_32_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_64_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_128_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_256_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_512_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_1024_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_2048_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_4096_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );

riscv_status riscv_mfcc_init_q15(
  riscv_mfcc_instance_q15 * S,
  uint32_t fftLen,
  uint32_t nbMelFilters,
  uint32_t nbDctOutputs,
  const q15_t *dctCoefs,
  const uint32_t *filterPos,
  const uint32_t *filterLengths,
  const q15_t *filterCoefs,
  const q15_t *windowCoefs
  );


/**
  @brief         MFCC Q15
  @param[in]    S       points to the mfcc instance structure
  @param[in]     pSrc points to the input samples
  @param[out]     pDst  points to the output MFCC values in q8.7 format
  @param[inout]     pTmp  points to a temporary buffer of complex
  @return        error status
 */
  riscv_status riscv_mfcc_q15(
  const riscv_mfcc_instance_q15 * S,
  q15_t *pSrc,
  q15_t *pDst,
  q31_t *pTmp
  );


#ifdef   __cplusplus
}
#endif

#endif /* ifndef _TRANSFORM_FUNCTIONS_H_ */
