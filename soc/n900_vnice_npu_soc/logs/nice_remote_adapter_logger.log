[08-12 15:10:47.854] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-12 15:10:52.249] [90958 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.249] [90958 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.250] [90958 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 45479
[08-12 15:10:52.250] [90958 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.250] [90958 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.250] [90958 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.250] [90958 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.252] [90992 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.252] [90992 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.253] [90992 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 45496
[08-12 15:10:52.253] [90992 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.253] [90992 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.253] [90992 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.253] [90992 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.253] [91 us] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.253] [91 us] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.253] [91 us] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45500
[08-12 15:10:52.253] [91 us] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.253] [91 us] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.253] [91 us] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.253] [91 us] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.254] [91008 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.254] [91008 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.254] [91008 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45504
[08-12 15:10:52.254] [91008 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.254] [91008 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.254] [91008 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.254] [91008 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.255] [91016 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.255] [91016 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.255] [91016 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45508
[08-12 15:10:52.255] [91016 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.255] [91016 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.255] [91016 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.255] [91016 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.255] [91024 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.255] [91024 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.255] [91024 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45512
[08-12 15:10:52.256] [91024 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.256] [91024 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.256] [91024 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.256] [91024 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.256] [91032 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.256] [91032 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.256] [91032 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45516
[08-12 15:10:52.256] [91032 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.256] [91032 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.256] [91032 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.256] [91032 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.257] [91040 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.257] [91040 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.257] [91040 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45520
[08-12 15:10:52.257] [91040 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.257] [91040 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.257] [91040 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.257] [91040 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.258] [91050 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.258] [91050 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.258] [91050 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x40e7b00b to npu, cycles 45525
[08-12 15:10:52.258] [91050 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.258] [91050 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.258] [91050 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.258] [91050 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.263] [91142 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.263] [91142 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.264] [91142 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 45571
[08-12 15:10:52.265] [91142 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.265] [91142 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.265] [91142 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.265] [91142 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.277] [91176 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.277] [91176 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.277] [91176 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 45588
[08-12 15:10:52.277] [91176 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.277] [91176 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.277] [91176 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.277] [91176 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.278] [91184 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.278] [91184 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.278] [91184 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45592
[08-12 15:10:52.278] [91184 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.278] [91184 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.278] [91184 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.278] [91184 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.279] [91192 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.279] [91192 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.279] [91192 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45596
[08-12 15:10:52.279] [91192 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.279] [91192 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.279] [91192 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.279] [91192 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.280] [91200 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.280] [91200 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.280] [91200 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45600
[08-12 15:10:52.280] [91200 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.280] [91200 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.280] [91200 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.280] [91200 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.281] [91208 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.281] [91208 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.281] [91208 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45604
[08-12 15:10:52.281] [91208 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.281] [91208 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.281] [91208 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.281] [91208 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.282] [91216 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.282] [91216 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.282] [91216 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45608
[08-12 15:10:52.282] [91216 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.282] [91216 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.282] [91216 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.282] [91216 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.282] [91224 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.282] [91224 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.283] [91224 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45612
[08-12 15:10:52.283] [91224 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.283] [91224 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.283] [91224 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.283] [91224 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.283] [91234 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.283] [91234 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.284] [91234 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x40e7b00b to npu, cycles 45617
[08-12 15:10:52.284] [91234 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.284] [91234 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.284] [91234 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.284] [91234 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.288] [91308 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.288] [91308 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.289] [91308 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 45654
[08-12 15:10:52.290] [91308 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.290] [91308 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.290] [91308 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.290] [91308 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.292] [91342 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.292] [91342 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.292] [91342 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 45671
[08-12 15:10:52.292] [91342 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.292] [91342 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.292] [91342 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.292] [91342 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.292] [91350 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.293] [91350 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.293] [91350 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45675
[08-12 15:10:52.293] [91350 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.293] [91350 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.293] [91350 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.293] [91350 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.293] [91358 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.293] [91358 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.293] [91358 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45679
[08-12 15:10:52.293] [91358 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.293] [91358 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.293] [91358 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.293] [91358 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.294] [91366 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.294] [91366 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.294] [91366 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45683
[08-12 15:10:52.294] [91366 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.294] [91366 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.294] [91366 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.294] [91366 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.295] [91374 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.295] [91374 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.295] [91374 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45687
[08-12 15:10:52.295] [91374 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.295] [91374 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.295] [91374 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.295] [91374 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.295] [91382 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.295] [91382 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.295] [91382 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45691
[08-12 15:10:52.296] [91382 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.296] [91382 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.296] [91382 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.296] [91382 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.296] [91390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.296] [91390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.296] [91390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45695
[08-12 15:10:52.296] [91390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.296] [91390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.296] [91390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.296] [91390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.297] [91400 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.297] [91400 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.297] [91400 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x40e7b00b to npu, cycles 45700
[08-12 15:10:52.297] [91400 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.297] [91400 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.297] [91400 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.297] [91400 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.301] [91474 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.301] [91474 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.302] [91474 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 45737
[08-12 15:10:52.303] [91474 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.303] [91474 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.303] [91474 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.303] [91474 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.305] [91508 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.305] [91508 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.305] [91508 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 45754
[08-12 15:10:52.305] [91508 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.305] [91508 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.305] [91508 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.305] [91508 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.306] [91516 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.306] [91516 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.306] [91516 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45758
[08-12 15:10:52.306] [91516 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.306] [91516 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.306] [91516 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.306] [91516 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.307] [91524 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.307] [91524 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.307] [91524 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45762
[08-12 15:10:52.307] [91524 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.307] [91524 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.307] [91524 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.307] [91524 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.307] [91532 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.307] [91532 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.307] [91532 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45766
[08-12 15:10:52.307] [91532 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.307] [91532 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.307] [91532 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.307] [91532 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.308] [91540 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.308] [91540 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.308] [91540 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45770
[08-12 15:10:52.308] [91540 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.308] [91540 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.308] [91540 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.308] [91540 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.309] [91548 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.309] [91548 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.309] [91548 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45774
[08-12 15:10:52.309] [91548 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.309] [91548 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.309] [91548 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.309] [91548 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.309] [91556 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.309] [91556 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.309] [91556 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 45778
[08-12 15:10:52.310] [91556 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.310] [91556 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.310] [91556 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.310] [91556 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.310] [91566 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.310] [91566 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.310] [91566 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x40e7b00b to npu, cycles 45783
[08-12 15:10:52.310] [91566 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.310] [91566 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.310] [91566 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.310] [91566 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.351] [92268 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.351] [92268 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.352] [92268 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 46134
[08-12 15:10:52.352] [92268 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.352] [92268 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.352] [92268 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.352] [92268 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.354] [92302 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.354] [92302 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.355] [92302 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 46151
[08-12 15:10:52.355] [92302 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.355] [92302 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.355] [92302 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.355] [92302 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.355] [92310 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.355] [92310 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.355] [92310 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46155
[08-12 15:10:52.355] [92310 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.355] [92310 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.355] [92310 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.355] [92310 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.356] [92318 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.356] [92318 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.356] [92318 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46159
[08-12 15:10:52.356] [92318 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.356] [92318 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.356] [92318 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.356] [92318 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.357] [92326 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.357] [92326 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.357] [92326 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46163
[08-12 15:10:52.357] [92326 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.357] [92326 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.357] [92326 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.357] [92326 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.357] [92334 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.357] [92334 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.357] [92334 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46167
[08-12 15:10:52.358] [92334 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.358] [92334 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.358] [92334 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.358] [92334 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.358] [92342 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.358] [92342 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.358] [92342 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46171
[08-12 15:10:52.358] [92342 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.358] [92342 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.358] [92342 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.358] [92342 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.359] [92350 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.359] [92350 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.359] [92350 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46175
[08-12 15:10:52.359] [92350 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.359] [92350 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.359] [92350 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.359] [92350 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.360] [92360 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.360] [92360 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.360] [92360 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x40e7b00b to npu, cycles 46180
[08-12 15:10:52.360] [92360 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.360] [92360 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.360] [92360 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.360] [92360 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.364] [92434 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.364] [92434 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.365] [92434 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 46217
[08-12 15:10:52.365] [92434 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.365] [92434 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.365] [92434 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.365] [92434 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.367] [92468 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.367] [92468 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.367] [92468 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 46234
[08-12 15:10:52.368] [92468 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.368] [92468 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.368] [92468 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.368] [92468 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.368] [92476 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.368] [92476 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.368] [92476 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46238
[08-12 15:10:52.368] [92476 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.368] [92476 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.368] [92476 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.368] [92476 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.369] [92484 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.369] [92484 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-12 15:10:52.369] [92484 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 46242
[08-12 15:10:52.369] [92484 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-12 15:10:52.369] [92484 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-12 15:10:52.369] [92484 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-12 15:10:52.369] [92484 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-12 15:10:52.369] [92492 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-12 15:10:52.369] [92492 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
