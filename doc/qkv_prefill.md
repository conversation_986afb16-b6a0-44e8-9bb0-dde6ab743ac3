```mermaid
flowchart TD
    subgraph "初始化阶段"
        A1["设置常量和内存布局<br/>NUM_GROUPS=4, HEAD_DIM=64<br/>SPAD0/1/2/3 基地址"] 
        A2["计算RoPE缓存位置<br/>COS_CACHE_BASE, SIN_CACHE_BASE<br/>ROPE_HALF_ROWS=360"]
        A3["预分配psum Tensor<br/>Q→SPAD1, K→SPAD2, V→SPAD3"]
        A4["预加载前360行RoPE cos/sin<br/>循环4个group，每组用group-mask<br/>加载到SPAD1/2尾部"]
    end
    
    A1 --> A2 --> A3 --> A4 --> B1

    subgraph "主循环"
        B1{"Batch循环<br/>batch_idx: 0→71<br/>每batch处理10行"}
        B2["slice_to_spad<br/>X切片→SPAD0<br/>使用FULL_MASK 16核"]
        B3{"RoPE重载检查<br/>batch_idx == 36?"}
        B4["重新加载后360行RoPE<br/>循环4个group<br/>覆盖相同缓存地址"]
        
        B5{"Head迭代循环<br/>head_iter: 0→2"}
        B6{"Group循环<br/>group_id: 0→3"}
        B7["检查head映射<br/>heads_per_core = minicpmv2_head_maping<br/>head_iter >= heads_per_core?"]
        B8["生成group mask<br/>激活当前group的4个核<br/>其他group mask=0"]
        
        subgraph "Tile处理"
            C1{"Tile_k循环<br/>tile_k: 0→17<br/>处理embedding维度"}
            C2["创建X_tile视图<br/>X_tile维度10×128 from SPAD0"]
            C3["计算CIM页索引<br/>page_q/k/v = tile_k*3+0/1/2 % 16"]
            C4["加载Q权重 → CIM page_q<br/>wt_q: DRAM → CIM"]
            C5["加载K权重 → CIM page_k<br/>wt_k: DRAM → CIM"]  
            C6["加载V权重 → CIM page_v<br/>wt_v: DRAM → CIM"]
            C7["GEMM Q: X_tile × W_q → Q_psum<br/>accumulate=0 for tile_k==0 else 1"]
            C8["GEMM K: X_tile × W_k → K_psum<br/>accumulate=1 for tile_k>0"]
            C9["GEMM V: X_tile × W_v → V_psum<br/>accumulate=1 for tile_k>0"]
        end
        
        subgraph "RoPE和存储"
            D1["构造sin/cos视图<br/>从缓存中取当前batch对应的10行"]
            D2["初始化InterMemory<br/>Q用SPAD1+0x800, K用SPAD2+0x800<br/>4×2KB chunks"]
            D3["RoPE处理Q<br/>Q_psum = rope Q_psum, sin_view, cos_view"]
            D4["RoPE处理K<br/>K_psum = rope K_psum, sin_view, cos_view"]
            D5["存储Q到Q_out<br/>base + head_iter×720 + row_start×64×2B"]
            D6["存储K到K_cache<br/>layer_base + head_iter×slice + offset"]
            D7["存储V到V_cache<br/>layer_base + head_iter×slice + offset"]
        end
    end

    B1 --> B2 --> B3
    B3 -->|yes| B4 --> B5
    B3 -->|no| B5
    B5 --> B6 --> B7
    B7 -->|skip| B6
    B7 -->|process| B8 --> C1
    
    C1 --> C2 --> C3 --> C4 --> C5 --> C6 --> C7 --> C8 --> C9
    C9 --> C1
    C1 -->|完成18个tile| D1 --> D2 --> D3 --> D4 --> D5 --> D6 --> D7
    
    D7 --> B6
    B6 -->|完成4个group| B5  
    B5 -->|完成3个head| B1
    B1 -->|完成72个batch| E1["函数结束"]

    style A1 fill:#e1f5fe
    style A4 fill:#f3e5f5
    style B3 fill:#fff3e0
    style B4 fill:#fff3e0
    style C1 fill:#e8f5e8
    style D3 fill:#fce4ec
    style D4 fill:#fce4ec
```

```mermaid
sequenceDiagram
    participant Main as 主函数
    participant DRAM as DDR内存
    participant SPAD as Scratchpad
    participant CIM as CIM存储
    participant NPU as NPU核心

    Note over Main,NPU: 初始化阶段
    Main->>SPAD: 预分配Q/K/V psum张量到SPAD1/2/3
    loop 4个Group
        Main->>DRAM: 读取RoPE cos/sin前360行
        Main->>SPAD: 加载到SPAD1/2尾部缓存
    end

    Note over Main,NPU: 主循环开始
    loop 72个Batch
        Main->>DRAM: 读取X数据切片10行
        Main->>SPAD: slice_to_spad加载到SPAD0
        
        alt batch_idx == 36
            loop 4个Group  
                Main->>DRAM: 读取RoPE cos/sin后360行
                Main->>SPAD: 覆盖到相同缓存位置
            end
        end
        
        loop 最多3个Head
            loop 4个Group
                alt Group有当前Head
                    NPU->>NPU: 设置group mask激活4核
                    
                    loop 18个Tile_k
                        Main->>SPAD: 创建X_tile视图
                        Main->>DRAM: 读取Q/K/V权重tile
                        Main->>CIM: 加载权重到不同页
                        NPU->>NPU: GEMM Q操作
                        NPU->>NPU: GEMM K操作  
                        NPU->>NPU: GEMM V操作
                    end
                    
                    Main->>SPAD: 构造RoPE sin/cos视图
                    Main->>SPAD: 初始化InterMemory
                    NPU->>NPU: RoPE处理Q张量
                    NPU->>NPU: RoPE处理K张量
                    
                    Main->>DRAM: 存储Q到Q_out
                    Main->>DRAM: 存储K到K_cache
                    Main->>DRAM: 存储V到V_cache
                end
            end
        end
    end

    Note over Main,NPU: 函数完成
```