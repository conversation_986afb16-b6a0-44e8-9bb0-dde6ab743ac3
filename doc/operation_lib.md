## 2.2 与CIMC相关

*   所提供内存计算算子库对一个NPU Core内部的CIMC操作仅仅为1个Page
*   例如GEMV和GEMM算子，仅仅选择CIMC中的1个Page中的权重作为计算
*   Tensor Load 或 NoC 算子，仅仅选择CIMC中的1个Page进行数据搬运和转移
*   注意：CIMC的内存只能能够用于

## 2.3 内存规格和地址说明

*   Scratchpad: 64KB*4;
*   CIMC: 8KB*16;
*   DRAM: 128

**注意：以下地址布局为每个NPU Core独享，系统总共有16个NPU Core，每个NPU Core都具有相同的地址布局。**

| Local Memory | Range                       |           | Range                         |
| :----------- | :-------------------------- | :-------- | :---------------------------- |
| Scratchpad0  | 0x0000_0000—0x0001_0000-1   | CIMC Page0 | 0x0040_0000—0x0040_2000-1     |
| Scratchpad1  | 0x0010_0000—0x0011_0000-1   | CIMC Page1 | 0x0040_2000—0x0040_4000-1     |
| Scratchpad2  | 0x0020_0000—0x0021_0000-1   | CIMC Pagen | 0x0040_0000+0x2000*n          |
| Scratchpad3  | 0x0030_0000—0x0031_0000-1   | CIMC Page15| 0x0041_e000—0x0042_0000-1     |
| **Global Memory** | **Range**                   |           |                               |
| DRAM         | 0x1000_0000—0x1800_0000-1   |           |                               |
## 3.1 Tensor 结构体

| 参数名      | 数据类型 | 含义                                    |
| :---------- | :------- | :-------------------------------------- |
| dim0        | uint32_t | Tensor的dim0维度元素个数                |
| dim1        | uint32_t | Tensor的dim1维度元素个数                |
| dim2        | uint32_t | Tensor的dim2维度元素个数                |
| byte_stride1 | uint32_t | Tensor在dim1维度上的数据步长，以字节为单位 |
| byte_stride2 | uint32_t | Tensor在dim2维度上的数据步长，以字节为单位 |
| width       | uint32_t | Tensor的数据宽度                        |
| type        | uint32_t | Tensor的数据类型                        |
| base_addr   | uint32_t | Tensor的起始数据地址                    |

*   `byte_stride1`: 表示编译器传入的一个张量在 `dim1` 维度上的数据排布步长，以字节为单位。由于内存块的最小单位为256bits (32bytes)，其值为 `dim1_stride * 32 bytes`，并在对应的联合体中，`stride/stride2` 的低五位固定为零。
    *   设计此参数的目的在于更灵活地控制中间变量的数据排布。在实际算子设计中，由于数据精度和步长的动态变化，`stride1` 从 `byte_stride1` 中独立出来，以满足更细粒度的数据排布需求。

## 3.2 InterMemoryArray 结构体

| 参数名           |             | 数据类型   | 含义                           |
| :--------------- | :---------- | :--------- | :----------------------------- |
| `*InterMemory`   | base_addr   | uint32_t   | 单个Tensor所需要中间内存的数据起始地址 |
|                  | byte_size   | uint32_t   | 单个Tensor所需要中间内存的数据大小，单位字节数 |
| `*InterMemoryArray` | memory      | *InterMemory | 单个Tensor所需要中间内存的结构体 |
|                  | length      | uint32_t   | 需要中间内存的Tensor个个数     |

## 3.3 CIM_Option结构体

| 参数名    | 数据类型 | 含义                   |
| :-------- | :------- | :--------------------- |
| type      | uint32_t | Weight的数据类型       |
| width     | uint32_t | Weight的数据宽度       |
| page_index | uint32_t | Weight在CIM Page起始地址 |
| accumulate | uint32_t | CIM Page计算结果是否累加 |
| activate  | uint32_t | CIM Page计算结果是否激活 |
## 3.4 VP_Option

|             | 参数名      | 数据类型   | 含义                                        |
| :---------- | :---------- | :--------- | :------------------------------------------ |
|             | saturate    | uint32_t   | 浮点数是否有饱和和要求: '0'无, '1'有        |
| special_case | disable0    | uint32_t   | 去规划范围浮点数: '0'无, '1'视为有          |
|             | round_mode  | uint32_t   | 取舍模式                                    |
|             | operation   | uint32_t   | SIMD操作类型                                |
| NICE        | scalar_in2  | uint32_t   | VS_V的第二个输入标量值                      |
| VNICE       | scalar_in2  | vint32m1_t | VS_V的第二个输入向量值                      |

## 3.5 NOC_Option

**NOC (Network on Chip) 命令用于不同NPU Core之间的数据传递和通信。**

|       | 参数名            | 数据类型 | 含义           |
| :---- | :---------------- | :------- | :------------- |
|       | base_addr_srcmem  | uint32_t | 源内存基地址   |
|       | base_addr_destmem | uint32_t | 目的内存基地址 |
| NICE  | src_idx           | uint32_t | 传输目的NPU索引 |
|       | dest_idx          | uint32_t | 传输源NPU索引  |

## 3.6 npu_mask和\*传参

**npu_mask用于选定相应的NPU Core执行操作，通过掩码机制控制哪些NPU Core参与计算。**

| 参数名     | 数据类型 | 含义                                 |
| :--------- | :------- | :----------------------------------- |
| MAX_MASK   | 宏定义 '4' | 低四位二进制指定某个Group的四个NPU Core |
| MAX_GROUP  | 宏定义 '4' | 代表四个NPU Group                    |
| *npu_mask  | int      | 数组指针，用于指定哪些NPU Core执行操作 |
```cpp
#define MAX_MASK 4
#define MAX_GROUP 4
// 编译器在软件上指定 (1 表示启用, 0 表示不启用)
int mask[MAX_MASK] = {0, 0b11, 0, 0b1};
// 表示选择了 group 1 和 group 3
int primitive(tensor_in, tensor_out, *npu_mask){
    for (int group_id = 0; group_id < MAX_GROUP; group_id++) {
        if (npu_mask[MAX_MASK]) {
            u_primitive(group_id, npu_mask[group_id]);
        }
    }
}
```