# MiniCPMV2 QKV Generation Prefill 量化支持 PRD

## 1. 功能概述

### 1.1 项目背景
MiniCPMV2 QKV Generation Prefill 当前实现了高效的注意力机制Q、K、V投影计算，支持多级切分和并行处理。为了进一步优化模型部署效率和降低内存需求，需要在现有实现基础上增加**INT4量化支持**。

### 1.2 核心目标
- **量化支持**：为QKV投影权重增加INT4量化和BF16反量化
- **精度保持**：确保量化后的计算精度满足模型要求
- **性能优化**：在支持量化的同时保持良好的计算性能
- **架构兼容**：与现有内存布局和硬件特性协同工作

### 1.3 技术挑战
- **架构重构**：从group级并行改为core级处理以支持per-core量化参数
- **内存管理**：扩展InterMemory需求，优化量化参数存储和访问
- **并行度权衡**：在量化支持和计算并行度之间找到最佳平衡

## 2. 技术背景分析

### 2.1 当前实现架构

```mermaid
flowchart TD
    subgraph "当前Group级架构"
        A1["输入矩阵 [720, 2304]"]
        A2["Batch切分 TILE_N=10<br/>[10, 2304]"]
        A3["Head切分 36heads<br/>[10, 2304] → [10, 64] per head"]
        A4["Group并行处理<br/>4 groups × 4 cores"]
        
        B1["Group循环 (g=0→3)"]
        B2["Head循环 (h=0→2/3)"]
        B3["Tile循环 (k=0→17)"]
        B4["权重共享<br/>Group内4核相同权重"]
        B5["CIM共享<br/>[10,128] @ [128,64]"]
    end
    
    A1 --> A2 --> A3 --> A4
    A4 --> B1 --> B2 --> B3 --> B4 --> B5

    style A4 fill:#e1f5fe
    style B4 fill:#ffebee
    style B5 fill:#ffebee
```

### 2.2 量化需求架构

```mermaid
flowchart TD
    subgraph "量化支持Core级架构"
        C1["量化权重分布<br/>每core独立权重+量化参数"]
        C2["量化参数布局<br/>[head_per_core×18, 64]"]
        C3["Core级处理<br/>16 cores独立并行"]
        
        D1["Core循环 (c=0→15)"]
        D2["Head循环 (h=0→2/3)"] 
        D3["Tile循环 (k=0→17)"]
        D4["权重独立<br/>每core专属权重"]
        D5["量化处理<br/>GEMM + 反量化"]
        D6["CIM独立<br/>每core独立页面"]
    end
    
    C1 --> C2 --> C3
    C3 --> D1 --> D2 --> D3 --> D4 --> D5 --> D6

    style C1 fill:#e8f5e8
    style D4 fill:#e8f5e8  
    style D5 fill:#fff3e0
    style D6 fill:#e8f5e8
```

### 2.3 关键差异对比

| 方面 | 当前Group级实现 | 量化Core级实现 | 影响分析 |
|------|----------------|---------------|----------|
| **并行粒度** | Group级(4核并行) | Core级(16核独立) | 降低group内并行度 |
| **权重管理** | Group内共享权重 | 每core独立权重 | 增加权重存储需求 |
| **量化参数** | 无 | Per-core分布式存储 | 新增内存和计算开销 |
| **CIM使用** | Group共享 | Core独立 | 提高CIM利用率 |
| **计算流程** | GEMM直接输出 | GEMM+反量化 | 增加反量化计算步骤 |

## 3. 架构设计

### 3.1 整体架构

```c
// 新的量化QKV Gen架构
void minicpmv2_qkvgen_prefill_quantized(
    int layer_id, 
    const Tensor* X_dram, 
    const Tensor* Q_out,
    InterMemoryArray* intermemory  // 扩展内存需求
) {
    // 外层：Batch级切分
    for (batch_idx = 0; batch_idx < 72; batch_idx++) {
        slice_to_spad(...);  // [10, 2304] → SPAD0
        
        // RoPE一次性加载(group级优化保留)
        load_rope_cos_sin_all_groups();
        
        // 中层：Core级处理(新架构)
        for (core_idx = 0; core_idx < 16; core_idx++) {
            int group_id = core_idx / 4;
            int heads_per_core = minicpmv2_head_maping(group_id, layer_id);
            
            // 内层：Head级处理
            for (head_iter = 0; head_iter < heads_per_core; head_iter++) {
                // 量化GEMM计算
                quantized_gemm_process(core_idx, head_iter, ...);
                
                // 反量化处理
                dequantization_process(core_idx, head_iter, ...);
            }
            
            // RoPE处理(恢复group级并行)
            rope_process_optimized();
        }
    }
}
```

### 3.2 量化参数布局

```c
// 每个Core的量化参数分布
Core存储布局：
┌─────────────────────────────────────┐
│ Core X (heads_per_core = 2/3)       │
│                                    │
│ 权重数据：                           │
│ q_proj: [2304, 64×heads_per_core]   │
│ k_proj: [2304, 64×heads_per_core]   │
│ v_proj: [2304, 64×heads_per_core]   │
│                                    │
│ 量化参数：                           │
│ scale: [18×heads_per_core, 64]      │ 
│ zero:  [18×heads_per_core, 64]      │
│                                    │
│ 地址计算：                           │
│ scale_base = core_scale_start_addr  │
│ zero_base = core_zero_start_addr    │
└─────────────────────────────────────┘
```

### 3.3 内存分配策略

```c
// InterMemoryArray扩展布局
InterMemory Layout (总计 ~6KB):
├── [0-1]:   RoPE cos/sin缓存     (256B)   - 复用原逻辑
├── [2-5]:   Q RoPE中间缓存       (512B)   - 复用原逻辑  
├── [6-9]:   K RoPE中间缓存       (512B)   - 复用原逻辑
├── [10]:    量化权重tile缓存     (1280B)  - 新增：[10,128]×2B
├── [11]:    scale参数缓存        (1280B)  - 新增：[18,64]×2B  
├── [12]:    zero参数缓存         (1280B)  - 新增：[18,64]×2B
├── [13]:    反量化中间结果       (1280B)  - 新增：[10,64]×2B
└── [14]:    量化GEMM累加器       (1280B)  - 新增：[10,64]×2B
```

## 4. 核心算法实现

### 4.1 量化GEMM处理流程

```c
void quantized_gemm_process(int core_idx, int head_iter, 
                           const Tensor* X_tile, 
                           InterMemoryArray* intermemory) {
    
    // 1. 计算当前core的量化参数地址
    int group_id = core_idx / 4;
    int heads_per_core = minicpmv2_head_maping(group_id, layer_id);
    
    uint32_t scale_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
    uint32_t zero_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
    
    // 2. 构建量化参数张量
    Tensor scale_tensor, zero_tensor;
    build_tensor(scale_base_addr + scale_offset, 18, HEAD_DIM, TYPE_BF, WIDTH_16, &scale_tensor);
    build_tensor(zero_base_addr + zero_offset, 18, HEAD_DIM, TYPE_BF, WIDTH_16, &zero_tensor);
    
    // 3. 初始化累加器
    Tensor quant_accumulator;
    build_tensor(intermemory->memory[14].base_addr, TILE_N, HEAD_DIM, TYPE_INT, WIDTH_32, &quant_accumulator);
    zero_tensor_content(&quant_accumulator, npu_mask);
    
    // 4. Tile级GEMM循环
    for (uint32_t tile_k = 0; tile_k < 18; tile_k++) {
        // 4.1 CIM页面分配
        uint32_t page_idx = tile_k % 16;  // 页面负载均衡
        uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx * CIMC_PAGE_OFFSET;
        
        // 4.2 权重tile地址计算
        uint32_t weight_tile_offset = (tile_k * TILE_C * heads_per_core + head_iter * TILE_C) * HEAD_DIM;
        uint32_t weight_addr = core_weight_base + weight_tile_offset;
        
        // 4.3 构建权重tile张量并加载到CIM
        Tensor weight_tile;
        build_tensor(weight_addr, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &weight_tile);
        
        CIM_Option cim_opt = {
            .type = TYPE_INT, .width = WIDTH_4, 
            .page_index = page_idx,
            .accumulate = (tile_k > 0) ? 1 : 0  // 首次不累加，后续累加
        };
        
        // 4.4 执行量化GEMM
        int npu_mask_single[4];
        make_single_core_mask(core_idx, npu_mask_single);
        
        load(&weight_tile, &cim_dst, npu_mask_single);
        gemm(&X_tile, &quant_accumulator, &cim_opt, npu_mask_single);
    }
    
    // 5. 反量化处理
    apply_per_tile_dequantization(&quant_accumulator, &scale_tensor, &zero_tensor, 
                                 &final_result, npu_mask_single);
}
```

### 4.2 反量化算法

```c
void apply_per_tile_dequantization(const Tensor* quantized_result,    // [10, 64] INT32
                                  const Tensor* scale_params,        // [18, 64] BF16
                                  const Tensor* zero_params,         // [18, 64] BF16  
                                  const Tensor* output_result,       // [10, 64] BF16
                                  int* npu_mask) {
    
    // 确定当前tile的量化参数组索引
    uint32_t scale_group_idx = current_tile_k / (MINICPMV2_EMBEDDING_DIM / TILE_C / 18);
    
    // 获取对应的scale/zero参数行
    Tensor scale_row, zero_row;
    make_tensor_view(scale_params, scale_group_idx, 0, 1, HEAD_DIM, &scale_row);   // [1, 64]
    make_tensor_view(zero_params, scale_group_idx, 0, 1, HEAD_DIM, &zero_row);     // [1, 64]
    
    // 逐行反量化：output = scale * (quantized - zero_point)
    for (uint32_t row = 0; row < TILE_N; row++) {
        Tensor quant_row, output_row;
        make_tensor_view(quantized_result, row, 0, 1, HEAD_DIM, &quant_row);
        make_tensor_view((Tensor*)output_result, row, 0, 1, HEAD_DIM, &output_row);
        
        // 计算：temp = quantized_row - zero_row
        VP_Option vp_sub = { .operation = OPERATION_SUB, .scalar_in2 = 0 };
        sub(&quant_row, &zero_row, &output_row, &vp_sub, npu_mask);
        
        // 计算：output_row = temp * scale_row  
        VP_Option vp_mul = { .operation = OPERATION_MUL, .scalar_in2 = 0 };
        mul(&output_row, &scale_row, &output_row, &vp_mul, npu_mask);
    }
}
```

## 5. 实现细节

### 5.1 循环结构重构

```c
// ❌ 原始Group级循环
for (batch_idx = 0; batch_idx < 72; batch_idx++) {
    for (head_iter = 0; head_iter < MAX_HEADS; head_iter++) {
        for (group_id = 0; group_id < 4; group_id++) {
            make_group_mask(group_id, mask);  // 4核并行
            // GEMM计算...
        }
    }
}

// ✅ 新的Core级循环  
for (batch_idx = 0; batch_idx < 72; batch_idx++) {
    for (core_idx = 0; core_idx < 16; core_idx++) {
        int group_id = core_idx / 4;
        int heads_per_core = minicpmv2_head_maping(group_id, layer_id);
        
        for (head_iter = 0; head_iter < heads_per_core; head_iter++) {
            make_single_core_mask(core_idx, mask);  // 单核处理
            quantized_gemm_process();  // 量化GEMM + 反量化
        }
    }
    
    // RoPE优化：恢复group级并行
    rope_process_group_parallel();
}
```

### 5.2 地址计算公式

```c
// Core级权重和量化参数地址计算
uint32_t core_id = core_idx;
uint32_t group_id = core_idx / 4;
uint32_t core_in_group = core_idx % 4;

// 权重地址 (每core独立)
uint32_t core_weight_base = minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj.addr_dram +
                           core_in_group * CORE_WEIGHT_OFFSET;

// 量化参数地址 (per-core分布) 
uint32_t scale_base_addr = minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj_scale.addr_dram +
                          core_in_group * CORE_SCALE_OFFSET;
uint32_t zero_base_addr = minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj_zero.addr_dram +
                         core_in_group * CORE_ZERO_OFFSET;

// Head级偏移计算
uint32_t head_weight_offset = head_iter * HEAD_DIM * MINICPMV2_EMBEDDING_DIM * sizeof(int4);
uint32_t head_scale_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
uint32_t head_zero_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
```

### 5.3 CIM页面管理

```c
// CIM页面负载均衡策略
uint32_t get_cim_page_index(uint32_t core_idx, uint32_t tile_k) {
    // 确保每个core的tile_k映射到不同页面，避免冲突
    uint32_t base_page = core_idx % 4;  // 每组4个基础页面
    uint32_t page_offset = (tile_k * 3) % 4;  // Q/K/V错开分配
    return (base_page + page_offset) % 16;
}

// CIM选项配置
CIM_Option setup_cim_option(uint32_t core_idx, uint32_t tile_k, bool is_first_tile) {
    return (CIM_Option){
        .type = TYPE_INT,
        .width = WIDTH_4,
        .page_index = get_cim_page_index(core_idx, tile_k),
        .accumulate = is_first_tile ? 0 : 1,  // 首次不累加
        .activate = 0
    };
}
```

## 6. 性能分析

### 6.1 并行度对比

| 阶段 | 原实现 | 量化实现 | 变化 | 影响 |
|------|--------|----------|------|------|
| **GEMM计算** | Group级(4核) | Core级(1核) | **↓75%** | 降低并行度 |
| **RoPE处理** | Group级(4核) | Group级(4核) | **无变化** | 保持优化 |
| **整体流程** | 简单 | 复杂+反量化 | **↑20%计算** | 增加开销 |

### 6.2 内存使用对比

```c
// 内存需求对比
                   原实现    量化实现    增量
InterMemory:       1.25KB   6KB        +4.75KB (+380%)
权重存储:          共享      分布式     +3x存储需求  
量化参数:          0        2×1.28KB   +2.56KB新增
总体增长:          -        -          +约8KB
```

### 6.3 计算复杂度分析

```c
// 计算步骤对比
原实现：GEMM(18次) + RoPE + Store = 约100 ops/batch
量化实现：GEMM(18次) + 反量化(18次) + RoPE + Store = 约140 ops/batch

// 性能预期
理论计算增长：+40%
并行度损失：-75% (GEMM阶段)  
整体性能预期：约持平或轻微下降(5-10%)
```

### 6.4 优化策略

#### 6.4.1 混合并行策略
```c
// 保持RoPE阶段的group级并行优化
for (core_idx) {
    quantized_gemm_single_core();  // 串行但必要
}
// 所有core完成GEMM后
for (group_id) {  
    rope_group_parallel();  // 恢复并行
}
```

#### 6.4.2 内存访问优化
```c
// 量化参数缓存策略
- 预加载当前batch所需的所有量化参数
- 使用InterMemory作为量化参数缓存
- 减少DDR访问频次
```

## 7. 接口设计

### 7.1 函数签名

```c
/// @brief MiniCPMV2 QKV Generation Prefill with INT4 Quantization Support
/// @param layer_id 层号, 0~39
/// @param X_dram 输入X(shape=[720,2304]), 储存在dram上  
/// @param Q_out Q_proj结果保存到q_out, shape=[heads_total*720, 64]
/// @param intermemory 中间计算缓存数组，至少15个块，约6KB
void minicpmv2_qkvgen_prefill_quantized(
    int layer_id, 
    const Tensor* X_dram, 
    const Tensor* Q_out,
    InterMemoryArray* intermemory
);
```

### 7.2 InterMemoryArray配置

```c
// 推荐配置
InterMemory qkv_quant_mem[15];
uint32_t base_addr = INTERMEMORY_BASE;

// RoPE缓存 (复用)
qkv_quant_mem[0].base_addr = base_addr;         qkv_quant_mem[0].byte_size = 128;   // cos cache
qkv_quant_mem[1].base_addr = base_addr + 128;   qkv_quant_mem[1].byte_size = 128;   // sin cache

// RoPE中间缓存 (复用)
for(int i = 2; i < 10; i++) {
    qkv_quant_mem[i].base_addr = base_addr + i * 128;
    qkv_quant_mem[i].byte_size = 128;
}

// 量化处理新增缓存
qkv_quant_mem[10].base_addr = base_addr + 1280;  qkv_quant_mem[10].byte_size = 1280; // 权重tile缓存
qkv_quant_mem[11].base_addr = base_addr + 2560;  qkv_quant_mem[11].byte_size = 1280; // scale缓存  
qkv_quant_mem[12].base_addr = base_addr + 3840;  qkv_quant_mem[12].byte_size = 1280; // zero缓存
qkv_quant_mem[13].base_addr = base_addr + 5120;  qkv_quant_mem[13].byte_size = 1280; // 反量化中间结果
qkv_quant_mem[14].base_addr = base_addr + 6400;  qkv_quant_mem[14].byte_size = 1280; // 量化累加器

InterMemoryArray qkv_quant_intermemory = {
    .memory = qkv_quant_mem,
    .length = 15
};
```

### 7.3 权重数据结构扩展

```c
// minicpmv_def.h 中需要扩展
typedef struct __MINICPMV2_WEIGHT_Layer {
    // ... 现有字段 ...
    struct {
        __WeightElem q_proj;        // 量化权重
        __WeightElem k_proj;
        __WeightElem v_proj;
        __WeightElem o_proj;
        
        // 新增：量化参数
        __WeightElem q_proj_scale;  // [18*heads_per_core, 64] per core
        __WeightElem k_proj_scale;  
        __WeightElem v_proj_scale;
        __WeightElem o_proj_scale;
        
        __WeightElem q_proj_zero;   // [18*heads_per_core, 64] per core  
        __WeightElem k_proj_zero;
        __WeightElem v_proj_zero;
        __WeightElem o_proj_zero;
    } self_attn;
    // ... 其他字段 ...
} __MINICPMV2_WEIGHT_Layer;
```

## 8. 验证与测试

### 8.1 功能正确性验证

```c
// 关键验证点
1. 量化精度验证：INT4→BF16精度损失在可接受范围内
2. 地址计算验证：每个core访问正确的权重和量化参数
3. 累加逻辑验证：18个tile的累加结果正确性
4. 反量化验证：量化参数组索引和应用正确性
5. 输出一致性：与原实现的数值对比验证
```

### 8.2 性能基准测试

```c
// 性能测试指标
1. 单层耗时：量化实现 vs 原实现
2. 内存峰值：InterMemory使用量和DDR访问模式
3. CIM利用率：页面分配和使用效率  
4. 端到端性能：完整推理流程的性能影响
```

### 8.3 边界情况测试

```c
// 边界条件验证
1. 不同layer的head分布：2头和3头group的正确处理
2. 边界batch：最后一个batch的数据处理
3. 量化参数边界：scale/zero极值情况的处理
4. 内存不足：InterMemory空间不足时的错误处理
```

## 9. 风险分析与缓解策略

### 9.1 技术风险

| 风险类型 | 风险描述 | 影响程度 | 缓解策略 |
|----------|----------|----------|----------|
| **精度损失** | INT4量化导致计算精度下降 | 高 | 仔细调优量化参数，增加精度验证 |
| **性能下降** | Core级处理降低并行度 | 中 | 优化RoPE并行，减少不必要开销 |
| **内存溢出** | InterMemory需求大幅增加 | 中 | 优化内存布局，提供配置选项 |
| **地址错误** | 复杂的地址计算容易出错 | 高 | 详细的地址计算验证和测试 |

### 9.2 实现风险

| 风险类型 | 风险描述 | 缓解策略 |
|----------|----------|----------|
| **兼容性** | 与现有代码架构不兼容 | 保持接口兼容，逐步迁移 |
| **维护性** | 代码复杂度显著增加 | 详细文档，模块化设计 |
| **测试复杂度** | 量化逻辑难以全面测试 | 分层测试，自动化验证 |

### 9.3 缓解措施

```c
// 1. 渐进式实现策略
阶段1：基础量化支持，保持功能正确性
阶段2：性能优化，减少并行度损失  
阶段3：内存优化，降低内存开销

// 2. 回退机制
提供编译时开关，支持非量化版本回退：
#ifdef QUANTIZATION_ENABLED
    minicpmv2_qkvgen_prefill_quantized(...);
#else  
    minicpmv2_qkvgen_prefill(...);  // 原实现
#endif

// 3. 详细的调试支持
增加详细的日志和断言，便于问题定位：
debug_assert(scale_index < max_scale_groups);
debug_log("Core %d processing head %d with scale_addr=0x%x", 
          core_idx, head_iter, scale_addr);
```

## 10. 总结

### 10.1 核心价值
- **模型优化**：支持INT4量化，显著降低模型存储和计算需求
- **精度平衡**：在量化效率和计算精度之间达到最优平衡
- **架构兼容**：与现有硬件特性和内存布局良好协同

### 10.2 技术创新
- **分布式量化参数管理**：每个core独立管理量化参数，避免全局同步开销
- **混合并行策略**：GEMM串行+RoPE并行，平衡计算效率和实现复杂度
- **高效反量化算法**：per-tile组织的反量化，最小化计算和内存开销

### 10.3 实施建议
1. **分阶段实现**：先确保功能正确性，再优化性能
2. **详细测试**：重点验证量化精度和地址计算正确性
3. **性能监控**：实时监控性能影响，必要时调优并行策略
4. **文档完善**：详细记录量化参数布局和地址计算公式

这个PRD为MiniCPMV2 QKV Generation Prefill的量化支持提供了完整的技术方案，平衡了功能需求、性能要求和实现复杂度，为后续开发实施提供了清晰的指导。