# MiniCPMV2 QKV Decode 函数实现说明

## 函数概述

`minicpmv2_qkvgen_decode` 是 MiniCPMV2 模型在 decode 阶段的 QKV 生成函数，处理单个 token 的 Q、K、V 投影计算。

## 函数签名

```c
void minicpmv2_qkvgen_decode(int seq_idx, int layer_id, const Tensor* X_spad, 
                            const Tensor* q_out, const Tensor* k_out, const Tensor* v_out, 
                            InterMemoryArray *intermemory);
```

## 参数说明

- **seq_idx**: decode token 的序列索引，从 prompt_len(720) 开始计数
- **layer_id**: 层号，范围 0~39
- **X_spad**: 输入张量 `[1, 2304]`，存储在 SPAD 上
- **q_out**: Q 投影结果输出张量 `[heads_mapping*1, 64]`
- **k_out**: K 投影结果输出张量 `[heads_mapping*1, 64]`  
- **v_out**: V 投影结果输出张量 `[heads_mapping*1, 64]`
- **intermemory**: 中间计算缓存数组

## 与 Prefill 的主要差异

| 方面 | Prefill | Decode |
|------|---------|--------|
| 输入尺寸 | `[N, d]` (N=720) | `[1, d]` (单token) |
| 批量处理 | 72个batch，每batch 10行 | 无批量，直接处理1行 |
| RoPE加载 | 分批加载前360行/后360行 | 简化计算，直接偏移加载 |
| RoPE加载优化 | 可用FULL_MASK优化4倍性能 | ✅已优化：函数开始一次性加载 |
| RoPE执行优化 | 受限于不同写回地址 | ✅已优化：group循环外full_mask执行 |
| 输出方式 | Q→Q_out, K/V→Cache, 需要store | 直接写入输出地址，无需store |
| 内存使用 | SPAD1/2/3 + 32KB RoPE缓存 | InterMemory 1.25KB |
| 并行度利用 | RoPE阶段25%利用率 | RoPE阶段100%利用率 |
| 循环复杂度 | batch × head × group × tile | head × (group × tile + RoPE) |

## RoPE 索引计算与加载机制

```c
// ========= 函数开始：一次性加载RoPE数据 =========
// 简化的索引计算：seq_idx 直接计算偏移
uint32_t rope_offset = seq_idx * HEAD_DIM * BYTES_PER_ELEM;

// 构建目标缓存张量
Tensor dst_cos, dst_sin;
build_tensor(cos_cache_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_cos);
build_tensor(sin_cache_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_sin);

// 遍历所有group，使用group_mask将各自的cos/sin加载到同一缓存地址
for (uint32_t g = 0; g < NUM_GROUPS; ++g) {
    make_group_mask(g, group_mask);  // 激活第g组的4个核
    
    // 从第g组的addr_dram加载cos/sin
    src_cos.base_addr = minicpmv2_weight.group[g].rope.cos.addr_dram + rope_offset;
    src_sin.base_addr = minicpmv2_weight.group[g].rope.sin.addr_dram + rope_offset;
    
    // 使用group_mask加载到统一的InterMemory缓存
    load(&src_cos, &dst_cos, group_mask);
    load(&src_sin, &dst_sin, group_mask);
}

// ========= 后续所有head和group都使用dst_cos/dst_sin =========
```

## InterMemoryArray 需求分析

### 最小需求
- **数量**: 至少 10 个 InterMemory 块
- **用途**: 
  - memory[0]: cos 缓存 (从DRAM加载的cos数据)
  - memory[1]: sin 缓存 (从DRAM加载的sin数据)
  - memory[2-5]: Q 的 RoPE 计算中间缓存 (4个)
  - memory[6-9]: K 的 RoPE 计算中间缓存 (4个)
  - V 不需要 RoPE，无需额外缓存

### 大小计算

对于单token处理：
- cos/sin缓存: `1 × 64 × 2B = 128B` 每个
- RoPE中间缓存: `4 × 128B = 512B` 每个RoPE操作
- **cos/sin缓存大小**: 128B 每个
- **RoPE中间缓存大小**: 128B 每个块
- **总内存需求**: 2×128B + 8×128B = **1.25KB**

### 推荐配置

```c
// 示例 InterMemoryArray 配置
InterMemory decode_mem[10];
uint32_t base_addr = some_spad_addr;

// cos/sin 缓存 (2个×128B)
decode_mem[0].base_addr = base_addr;
decode_mem[0].byte_size = 128;  // cos 缓存
decode_mem[1].base_addr = base_addr + 128;
decode_mem[1].byte_size = 128;  // sin 缓存

// Q RoPE 中间缓存 (4个×128B)
for(int i = 2; i < 6; i++) {
    decode_mem[i].base_addr = base_addr + i * 128;
    decode_mem[i].byte_size = 128;
}

// K RoPE 中间缓存 (4个×128B)
for(int i = 6; i < 10; i++) {
    decode_mem[i].base_addr = base_addr + i * 128;
    decode_mem[i].byte_size = 128;
}

InterMemoryArray decode_intermemory = {
    .memory = decode_mem,
    .length = 10
};
```

## 内存布局

```
InterMemory 布局 (1.25KB 总计):
├── [0] cos_cache [1, 64]           (128B)
├── [1] sin_cache [1, 64]           (128B)
├── [2-5] Q_RoPE_intermediate       (4×128B = 512B)
└── [6-9] K_RoPE_intermediate       (4×128B = 512B)

输出地址直接使用:
├── q_out->base_addr + head_offset  (Q psum 直接写入)
├── k_out->base_addr + head_offset  (K psum 直接写入)  
└── v_out->base_addr + head_offset  (V psum 直接写入)
```

## 性能特点

1. **更简单的控制流**: 无batch循环，减少分支
2. **简化的RoPE索引**: 直接计算偏移，避免复杂的分块逻辑
3. **RoPE一次性加载**: 移到函数开始，4个group的cos/sin统一加载到InterMemory
4. **RoPE优化执行**: 移到group循环外，对完整psum使用full_mask(16核)执行
5. **直接输出写入**: 使用q_out/k_out/v_out地址作为psum，无需额外store
6. **最小内存足迹**: InterMemory总需求仅1.25KB
7. **最大并行度**: RoPE阶段使用全部16核并行处理

## 调用示例

```c
// 准备输入
Tensor X_spad;     // [1, 2304] 在SPAD上
Tensor q_out;      // [heads_total, 64] 输出缓冲区
Tensor k_out;      // [heads_total, 64] 输出缓冲区  
Tensor v_out;      // [heads_total, 64] 输出缓冲区

// 准备中间缓存
InterMemory mem[10];
uint32_t base_addr = SPAD1_BASE + 0x1000;

// cos/sin 缓存
mem[0].base_addr = base_addr;      mem[0].byte_size = 128;  // cos
mem[1].base_addr = base_addr + 128; mem[1].byte_size = 128;  // sin

// Q RoPE 中间缓存 (4个)
for(int i = 2; i < 6; i++) {
    mem[i].base_addr = base_addr + i * 128;
    mem[i].byte_size = 128;
}

// K RoPE 中间缓存 (4个)
for(int i = 6; i < 10; i++) {
    mem[i].base_addr = base_addr + i * 128;
    mem[i].byte_size = 128;
}

InterMemoryArray intermem = {.memory = mem, .length = 10};

// 调用decode函数
minicpmv2_qkvgen_decode(seq_idx, layer_id, &X_spad, 
                       &q_out, &k_out, &v_out, &intermem);
```

## 关键Bug修正与性能优化

### 🐛 **重要Bug修正：RoPE执行时机**

**问题描述**：
- ❌ **错误实现**：在每个group的GEMM后立即执行RoPE
- ❌ **导致问题**：RoPE在不完整的psum上执行，结果错误

**正确实现**：
- ✅ **修正实现**：等所有group的GEMM完成后再执行RoPE
- ✅ **确保正确性**：RoPE在完整的psum上执行

```c
// ❌ 错误的流程
for (head_iter) {
    for (group_id) {
        gemm(..., accumulate=1);  // 部分累积
        rope(&psum, ...);         // 在不完整的psum上执行！
    }
}

// ✅ 正确的流程  
for (head_iter) {
    for (group_id) {
        gemm(..., accumulate=1);  // 累积到完整的psum
    }
    rope(&psum, ...);             // 在完整的psum上执行
}
```

### 🚀 **性能优化效果**

#### **1. RoPE并行度提升**
| 项目 | 修正前 | 修正后 | 提升效果 |
|------|--------|--------|----------|
| 并行核心数 | 4核×4次 | 16核×1次 | **4倍并行度** |
| RoPE执行次数 | 4次/head | 1次/head | **75%减少** |
| 硬件利用率 | 25% | 100% | **4倍提升** |

#### **2. 整体性能提升预期**
1. **正确性保证**: 修正了关键的逻辑错误
2. **RoPE性能**: 4倍并行度提升
3. **整体decode性能**: 预期**15-25%性能提升**
4. **硬件效率**: 最大化NPU核心利用率

#### **3. 对比prefill的优化**
| 优化类型 | Prefill可优化 | Decode已优化 |
|----------|---------------|--------------|
| RoPE加载 | 可用FULL_MASK | ✅已优化 |
| RoPE执行 | 受限于写回地址 | ✅已优化 |
| 并行度 | 部分优化空间 | ✅最大化 |

### 🔄 **修正前后对比图**

```mermaid
graph TB
    subgraph "错误实现 (修正前)"
        A1["Head 0 开始"]
        A2["Group 0: GEMM"]
        A3["RoPE on 部分psum ❌"]
        A4["Group 1: GEMM"]
        A5["RoPE on 部分psum ❌"]
        A6["Group 2: GEMM"]
        A7["RoPE on 部分psum ❌"]
        A8["Group 3: GEMM"]
        A9["RoPE on 部分psum ❌"]
        A10["Head 0 完成 (错误结果)"]
    end
    
    subgraph "正确实现 (修正后)"
        B1["Head 0 开始"]
        B2["Group 0: GEMM (累积)"]
        B3["Group 1: GEMM (累积)"]
        B4["Group 2: GEMM (累积)"]
        B5["Group 3: GEMM (累积)"]
        B6["RoPE on 完整psum ✅<br/>使用full_mask(16核)"]
        B7["Head 0 完成 (正确结果)"]
    end
    
    subgraph "性能对比"
        C1["RoPE执行次数<br/>修正前: 4次/head<br/>修正后: 1次/head"]
        C2["并行核心数<br/>修正前: 4核×4次<br/>修正后: 16核×1次"]
        C3["硬件利用率<br/>修正前: 25%<br/>修正后: 100%"]
        C4["性能提升<br/>RoPE: 4倍提升<br/>整体: 15-25%提升"]
    end

    A1 --> A2 --> A3 --> A4 --> A5 --> A6 --> A7 --> A8 --> A9 --> A10
    B1 --> B2 --> B3 --> B4 --> B5 --> B6 --> B7
    
    C1 --> C2 --> C3 --> C4

    style A3 fill:#ffebee
    style A5 fill:#ffebee
    style A7 fill:#ffebee
    style A9 fill:#ffebee
    style A10 fill:#ffcdd2
    
    style B6 fill:#e8f5e8
    style B7 fill:#c8e6c8
    
    style C4 fill:#fff3e0
```

### 📝 **关键收获与总结**

1. **🐛 Bug的严重性**: 
   - 在不完整的psum上执行RoPE会导致**完全错误的结果**
   - 这不仅仅是性能问题，而是**功能正确性问题**

2. **✅ 修正的重要性**:
   - **正确性**: 确保RoPE在完整的累积结果上执行
   - **性能**: 4倍RoPE并行度提升 + 15-25%整体性能提升
   - **效率**: 100%硬件利用率，最大化NPU核心使用

3. **💡 优化启示**:
   - decode阶段相比prefill有更好的RoPE优化潜力
   - 正确的执行顺序是性能优化的前提
   - full_mask的使用能够最大化并行计算效率

## 算法流程图

```mermaid
flowchart TD
    subgraph "初始化阶段"
        A1["设置常量<br/>NUM_GROUPS=4, HEAD_DIM=64"]
        A2["验证InterMemory配置<br/>需要10个块，总计1.25KB"]
        A3["分配内存地址<br/>cos_cache_addr = memory[0]<br/>sin_cache_addr = memory[1]<br/>rope_mem_q = memory[2-5]<br/>rope_mem_k = memory[6-9]"]
    end
    
    subgraph "RoPE一次性加载"
        B1["计算RoPE偏移<br/>rope_offset = seq_idx × 64 × 2B"]
        B2["构建dst_cos/dst_sin张量<br/>指向InterMemory缓存地址"]
        B3{"遍历4个Group<br/>g: 0→3"}
        B4["生成group_mask[g]<br/>激活第g组的4个核"]
        B5["构建src张量<br/>从group[g].addr_dram+offset加载"]
        B6["执行加载操作<br/>load(src_cos→dst_cos, group_mask)<br/>load(src_sin→dst_sin, group_mask)"]
    end
    
    subgraph "主计算循环"
        C1{"Head迭代循环<br/>head_iter: 0→2"}
        C2{"Group循环<br/>group_id: 0→3"}
        C3["检查head映射<br/>heads_per_core = minicpmv2_head_maping<br/>head_iter >= heads_per_core?"]
        C4["生成group mask<br/>激活当前group的4个核"]
        C5["设置psum地址<br/>Q/K/V_psum = q/k/v_out + head_offset<br/>直接使用输出地址"]
        
        subgraph "GEMM计算"
            D1{"Tile_k循环<br/>tile_k: 0→17"}
            D2["创建X_tile视图<br/>[1,128] from X_spad"]
            D3["计算CIM页索引<br/>分配Q/K/V不同页面"]
            D4["加载权重到CIM<br/>Q/K/V权重tile → CIM页"]
            D5["执行GEMM<br/>X_tile × W_q → Q_psum<br/>X_tile × W_k → K_psum<br/>X_tile × W_v → V_psum"]
        end
        
        subgraph "RoPE处理 (移到Group循环外)"
            E1["RoPE处理Q<br/>使用dst_cos/dst_sin<br/>rope_mem_q中间缓存<br/>对完整psum执行<br/>使用full_mask(16核)"]
            E2["RoPE处理K<br/>使用dst_cos/dst_sin<br/>rope_mem_k中间缓存<br/>对完整psum执行<br/>使用full_mask(16核)"]
            E3["V无需RoPE<br/>GEMM结果已在v_out地址"]
        end
    end

    A1 --> A2 --> A3 --> B1
    B1 --> B2 --> B3 --> B4 --> B5 --> B6
    B6 --> B3
    B3 -->|完成4个group| C1
    
    C1 --> C2 --> C3
    C3 -->|skip| C2
    C3 -->|process| C4 --> C5 --> D1
    
    D1 --> D2 --> D3 --> D4 --> D5
    D5 --> D1
    D1 -->|完成18个tile| C2
    C2 -->|完成4个group| E1
    
    E1 --> E2 --> E3 --> C1
    C1 -->|完成head| F1["函数结束<br/>所有结果已写入输出地址"]

    style A2 fill:#e1f5fe
    style B1 fill:#fff3e0
    style B6 fill:#f3e5f5
    style C5 fill:#e8f5e8
    style D5 fill:#fce4ec
    style E1 fill:#fff8e1
    style E2 fill:#fff8e1
    style F1 fill:#e8f5e8
```

## 时序图

```mermaid
sequenceDiagram
    participant Main as 主函数
    participant IM as InterMemory
    participant DRAM as DDR内存
    participant CIM as CIM存储
    participant NPU as NPU核心

    Note over Main,NPU: 初始化阶段
    Main->>IM: 验证InterMemory配置(10个块,1.25KB)
    Main->>IM: 分配cos/sin缓存地址
    Main->>IM: 分配Q/K RoPE中间缓存地址

    Note over Main,NPU: RoPE一次性加载
    Main->>Main: 计算rope_offset = seq_idx × 64 × 2B
    loop 4个Group
        Main->>NPU: 生成group_mask激活4核
        Main->>DRAM: 读取group[g].cos/sin.addr_dram+offset
        Main->>IM: 加载到cos/sin缓存
    end
    Note right of IM: cos/sin数据在InterMemory中<br/>后续所有计算共享使用

    Note over Main,NPU: 主计算循环
    loop 最多3个Head
        Note over Main,NPU: GEMM阶段：所有Group累积计算
        loop 4个Group
            alt Group有当前Head
                Main->>NPU: 设置group mask
                Main->>Main: 设置Q/K/V psum地址为输出地址
                
                loop 18个Tile_k
                    Main->>Main: 创建X_tile视图
                    Main->>DRAM: 读取Q/K/V权重tile
                    Main->>CIM: 加载权重到不同页
                    NPU->>NPU: GEMM Q/K/V操作(累积)
                end
            end
        end
        
        Note over Main,NPU: RoPE阶段：对完整psum执行
        Main->>NPU: 设置full_mask激活16核
        Main->>IM: 使用cos/sin缓存数据
        NPU->>NPU: RoPE处理Q张量(完整psum)
        NPU->>NPU: RoPE处理K张量(完整psum)
        Note right of NPU: V不需要RoPE<br/>结果已在输出地址
    end

    Note over Main,NPU: 函数完成
    Note right of Main: 所有Q/K/V结果已直接<br/>写入输出地址，无需store
```
