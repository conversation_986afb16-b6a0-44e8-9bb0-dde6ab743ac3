# MiniCPMV2 FlashAttention Token-wise Prefill 函数实现说明

## 函数概述

`minicpmv2_flashattn_prefill_tokenwise` 是 MiniCPMV2 模型在 prefill 阶段的 FlashAttention 实现，基于 Token-by-Token 策略和 Online-Softmax 算法实现内存高效的自注意力计算，天然满足因果性约束。

## 函数签名

```c
void minicpmv2_flashattn_prefill_tokenwise(int layer_id,
    const Tensor *Q_dram,   // [prompt_len, HEAD_DIM] 在 DRAM
    const Tensor *O_out,    // [prompt_len*head_mapping, HEAD_DIM] 目标地址 (DRAM)
    InterMemoryArray *intermemory);
```

## 参数说明

- **layer_id**: 层号，范围 0~39
- **Q_dram**: 查询张量 `[prompt_len, 64]`，储存在DRAM上，通常prompt_len=720
- **O_out**: 注意力输出张量 `[prompt_len*head_mapping, 64]`，目标地址 (DRAM)，每个核有独立地址空间
- **intermemory**: 中间计算缓存数组，至少7个块，约4.4KB

## 核心算法：Token-wise Processing + Online-Softmax

### 原理概述

Token-wise Prefill 逐个处理每个 query token，对每个 token 只处理其可见的历史 tokens，天然满足因果性：

```
标准 Prefill Attention: O = softmax(Q @ K^T) @ V  → 需要存储 [prompt_len, prompt_len] 矩阵
Token-wise FlashAttention: 逐token处理 + 自然因果性 + 在线更新 → 只需要常数级内存
```

### Token-wise Processing 策略

```c
// 逐个处理每个query token：
for q_idx in range(prompt_len):                    // 720个query tokens
    visible_kv_len = q_idx + 1                     // 因果性：只能看到 ≤ 当前位置的tokens
    for blk_start in range(0, visible_kv_len, BLOCK_M):  // 分块处理历史tokens
        process_attention_block(Q[q_idx], K[blk_start:blk_end], V[blk_start:blk_end])
```

### 天然因果性保证

相比显式 causal mask，token-wise 处理天然满足因果性：

```c
// 因果性检查：q_idx >= blk_start..blk_end 天然成立
// 因为我们只处理 kv_indices in [0, q_idx]，无需显式mask
```

## 架构设计

### 三层嵌套结构 (简化设计)

```
minicpmv2_flashattn_prefill_tokenwise
├── q_idx 循环 (720个query tokens) ✅ 外层：逐token处理
│   ├── head_iter 循环 (MAX_HEADS_PER_GROUP=3)
│   │   ├── core_idx 循环 (16个NPU核心) ✅ 16核并行处理
│   │   │   ├── 检查group是否有当前head
│   │   │   ├── 生成单核mask ✅ 使用单核精确控制
│   │   │   ├── 获取group对应的KV cache地址 ✅ 区分不同group
│   │   │   └── block_iter 循环 (分块处理可见历史)
│   │   │       ├── 从KV cache加载K/V_block ✅ 从cache读取
│   │   │       ├── 计算S_block = Q_token @ K_block^T
│   │   │       ├── 无需Causal Mask ✅ 天然因果性
│   │   │       ├── Online-Softmax更新
│   │   │       └── 累积O_accum输出
│   │   └── 最终归一化 & 写回DRAM
│   └── 进入下一个token
```

### 关键优势说明

#### 1. 天然因果性

**Token-wise 处理的优势:**
```c
✅ // 天然因果性：无需显式mask
   for (uint32_t q_idx = 0; q_idx < prompt_len; ++q_idx) {
     uint32_t visible_kv_len = q_idx + 1;  // 只处理 ≤ 当前位置的tokens
     
     // 所有 blk_start..blk_end ≤ q_idx，天然满足因果性
     for (uint32_t blk_start = 0; blk_start < visible_kv_len; blk_start += BLOCK_M) {
       // 无需检查 global_q_pos < global_k_pos，因为结构保证了因果性
     }
   }
```

**对比传统 Chunked 方法:**
```c
❌ // 需要显式检查和mask
   if (chunk_q_idx == chunk_k_idx) {
     for (uint32_t i = 0; i < q_chunk_rows; i++) {
       for (uint32_t j = i + 1; j < k_chunk_rows; j++) {
         if (global_q_pos < global_k_pos) {
           S_chunk[i][j] = NEG_INF_BF16;  // 复杂的mask逻辑
         }
       }
     }
   }
```

#### 2. 简化的算法流程

**Token-wise 版本 (简单):**
```c
// 1. 外层：逐token循环，每次处理1个query
// 2. 内层：标准decode-like处理，复用mature算法
// 3. 因果性：结构保证，无需额外逻辑
```

**Chunked 版本 (复杂):**
```c
// 1. 外层：chunk_q循环，每次处理10个query
// 2. 内层：chunk_k循环，需要causal限制
// 3. 因果性：需要复杂的mask应用逻辑
// 4. 矩阵化：每行独立状态管理
```

#### 3. 内存布局优化

Token-wise 处理可以直接复用 decode 的内存布局：

```c
✅ // Token-wise：复用decode内存布局
   InterMemory 需求：7个块，约4.4KB
   - 无需矩阵级别的S/P缓存 (10×10)
   - 只需向量级别的S/P缓存 (1×10)
   - exp缓存需求最小 (1个块)
```

## InterMemoryArray 需求分析

### 内存布局

```c
总计需要 7 个 InterMemory 块，约 4.4KB：

[0]: K_block 缓存         (1280 bytes) - [BLOCK_M, 64] × 2B
[1]: K_block_T 缓存       (1280 bytes) - [64, BLOCK_M] × 2B  
[2]: V_block 缓存         (1280 bytes) - [BLOCK_M, 64] × 2B
[3]: S_block/P_block      (32 bytes)   - [1, BLOCK_M] × 2B (复用地址)
[4]: O_accum 缓存         (128 bytes)  - [1, 64] × 2B
[5]: O_update 缓存        (128 bytes)  - [1, 64] × 2B
[6]: exp_v1 中间缓存      (128 bytes)  - exp函数计算缓存 (1个)
```

### 大小计算精确性

```c
// 向量级别处理：
K_block:     BLOCK_M × HEAD_DIM × 2B = 10 × 64 × 2 = 1280B
K_block_T:   HEAD_DIM × BLOCK_M × 2B = 64 × 10 × 2 = 1280B
V_block:     BLOCK_M × HEAD_DIM × 2B = 10 × 64 × 2 = 1280B
S/P_block:   1 × BLOCK_M × 2B = 1 × 10 × 2 = 20B → 32B (对齐)
O_accum:     1 × HEAD_DIM × 2B = 1 × 64 × 2 = 128B
O_update:    1 × HEAD_DIM × 2B = 1 × 64 × 2 = 128B
exp缓存:     1 × 128B = 128B

总计：1280×3 + 32 + 128×2 + 128 = 4.4KB
```

### 推荐配置

```c
// FlashAttention Token-wise Prefill InterMemoryArray 配置
InterMemory flash_mem[7];
uint32_t base_addr = INTERMEMORY_BASE;

// K/V/S 大块缓存 (3×1280B = 3840B)
flash_mem[0].base_addr = base_addr;           flash_mem[0].byte_size = 1280;  // K_block
flash_mem[1].base_addr = base_addr + 1280;    flash_mem[1].byte_size = 1280;  // K_block_T
flash_mem[2].base_addr = base_addr + 2560;    flash_mem[2].byte_size = 1280;  // V_block

// 小缓存块 (288B)
flash_mem[3].base_addr = base_addr + 3840;    flash_mem[3].byte_size = 32;   // S/P_block
flash_mem[4].base_addr = base_addr + 3872;    flash_mem[4].byte_size = 128;  // O_accum
flash_mem[5].base_addr = base_addr + 4000;    flash_mem[5].byte_size = 128;  // O_update

// exp_v1 中间缓存 (1×128B = 128B)
flash_mem[6].base_addr = base_addr + 4128;    flash_mem[6].byte_size = 128;  // exp缓存

InterMemoryArray flash_intermemory = {
    .memory = flash_mem,
    .length = 7
};
```

## 关键算法实现

### 1. Token-by-Token Processing

```c
// 外层循环：逐个处理query token
for (uint32_t q_idx = 0; q_idx < prompt_len; ++q_idx) {
    // 获取当前token的Q向量
    make_tensor_view(Q_dram, q_idx, 0, 1, HEAD_DIM, &Q_token);
    
    // 因果性：只能看到 ≤ 当前位置的tokens
    uint32_t visible_kv_len = q_idx + 1;
    
    // 内层：标准Online-Softmax处理（类似decode）
    process_single_token_attention(Q_token, visible_kv_len);
}
```

### 2. Decode-like Online-Softmax

```c
// 复用decode的成熟算法
float m_prev = -1e4f;    // 行最大值状态 m_i^{(j-1)}
float d_prev = 0.0f;     // 行分母状态 d_i^{(j-1)}

for (uint32_t blk_start = 0; blk_start < visible_kv_len; blk_start += BLOCK_M) {
    // 1. 计算attention scores: S_ij = Q_i @ K_j^T
    // 2. 缩放: S_ij *= 1/√d_k
    // 3. Online-Softmax更新
    // 4. 累积输出: O_i^{new} = O_i^{old} * r + P_ij @ V_j
}

// 最终归一化: O_i^{final} = O_i^{accum} / d_i^{final}
```

### 3. 天然因果性保证

```c
// 无需显式causal mask的原因：
// - q_idx 范围：[0, prompt_len-1]
// - visible_kv_len = q_idx + 1
// - blk_start..blk_end 范围：[0, visible_kv_len-1]
// - 因此 blk_end ≤ visible_kv_len-1 ≤ q_idx
// - 天然满足 query_position ≥ key_position
```

## 与其他方案的对比

| 方面 | Chunked Prefill | Token-wise Prefill | Decode | 优势说明 |
|------|-----------------|-------------------|--------|----------|
| **算法复杂度** | 高 (4层嵌套) | 中 (3层嵌套) | 低 (2层嵌套) | Token-wise适中 |
| **因果性处理** | 显式mask | 天然保证 | 天然保证 | **无需复杂逻辑** |
| **内存需求** | 12KB | 4.4KB | 4.5KB | **接近decode效率** |
| **状态管理** | 矩阵化 | 标量 | 标量 | **简化实现** |
| **代码复用** | 低 | 高 | - | **复用decode算法** |
| **调试难度** | 高 | 低 | 低 | **易于验证** |

## 性能分析

### 理论性能

| 指标 | 标准Prefill | Chunked Prefill | Token-wise Prefill | 改进效果 |
|------|-------------|----------------|--------------------|----------|
| **内存复杂度** | O(N²) | O(1) | O(1) | **相同优化** |
| **计算复杂度** | O(N²) | O(N²) | O(N²) | 相同 |
| **实现复杂度** | 低 | 高 | 中 | **平衡最优** |
| **因果性开销** | 中 | 高 | 无 | **零开销** |
| **代码维护性** | 高 | 低 | 高 | **易维护** |

### 内存效率对比

```c
// 标准Prefill内存需求：
Attention Matrix: [720, 720] × 2B = 1.03MB

// Token-wise Prefill内存需求：
InterMemory Total: 4.4KB = 0.0044MB

// 节省倍数：1.03MB / 0.0044MB ≈ 234倍内存节省
```

### 计算效率分析

```c
// Token-wise 算法特点：
✅ 优势:
- 无causal mask计算开销
- 复用decode成熟算法
- 内存访问模式规律
- 硬件友好的数据布局

⚠️ 考虑:
- 外层token循环增加控制开销
- 相比chunked少了批处理优化
- 需要720次Online-Softmax初始化
```

## 错误处理与边界情况

### 1. 序列长度处理

```c
// 最后一个block可能不满
uint32_t blk_rows = (blk_start + BLOCK_M <= visible_kv_len) ? 
                    BLOCK_M : (visible_kv_len - blk_start);
```

### 2. 数值稳定性保证

```c
// 每个token独立的状态初始化
float m_prev = -1e4f;    // 足够小的初始最大值
float d_prev = 0.0f;     // 初始分母

// Safe-softmax确保 exp(S - m_new) ≤ 1
```

### 3. 地址计算准确性

```c
// 每个核独立地址空间
uint32_t token_offset = q_idx * HEAD_DIM * BYTES_PER_ELEM;  
uint32_t head_offset = head_iter * prompt_len * HEAD_DIM * BYTES_PER_ELEM;  
uint32_t o_out_base = O_out->base_addr + head_offset + token_offset;
```

## 调用示例

```c
// 准备输入张量
Tensor Q_input;     // [720, 64] 在DRAM上
Tensor O_output;    // [3*720, 64] 输出缓冲区 (每核独立地址)

// 准备中间缓存 (4.4KB)
InterMemory flash_mem[7];
uint32_t base_addr = INTERMEMORY_BASE;

// K/V/S 大块缓存 (3×1280B = 3840B)
flash_mem[0].base_addr = base_addr;             flash_mem[0].byte_size = 1280;  // K_block
flash_mem[1].base_addr = base_addr + 1280;      flash_mem[1].byte_size = 1280;  // K_block_T
flash_mem[2].base_addr = base_addr + 2560;      flash_mem[2].byte_size = 1280;  // V_block

// 小缓存块 (288B)
flash_mem[3].base_addr = base_addr + 3840;      flash_mem[3].byte_size = 32;    // S/P_block
flash_mem[4].base_addr = base_addr + 3872;      flash_mem[4].byte_size = 128;   // O_accum
flash_mem[5].base_addr = base_addr + 4000;      flash_mem[5].byte_size = 128;   // O_update

// exp_v1 中间缓存 (1×128B = 128B)
flash_mem[6].base_addr = base_addr + 4128;      flash_mem[6].byte_size = 128;   // exp缓存

InterMemoryArray flash_intermemory = {.memory = flash_mem, .length = 7};

// 调用Token-wise Prefill FlashAttention
minicpmv2_flashattn_prefill_tokenwise(layer_id, &Q_input, &O_output, &flash_intermemory);
```

## 算法流程图

```mermaid
flowchart TD
    subgraph "初始化阶段"
        A1["设置常量<br/>BLOCK_M=10, HEAD_DIM=64<br/>prompt_len=720"]
        A2["验证InterMemory配置<br/>需要7个块，总计4.4KB"]
        A3["分配内存地址<br/>K/V blocks, S/P blocks<br/>O缓存, exp缓存"]
    end
    
    subgraph "Token-wise Prefill主循环"
        B1{"Token循环<br/>q_idx: 0→719<br/>（外层：逐token处理）"}
        B2["获取当前Query Token<br/>Q_token = Q_dram[q_idx]"]
        B3["计算可见历史长度<br/>visible_kv_len = q_idx + 1<br/>（因果性保证）"]
        
        subgraph "16-Core并行处理"
            C1{"Head迭代循环<br/>head_iter: 0→2"}
            C2{"Core循环<br/>core_idx: 0→15"}
            C3["检查head映射<br/>heads_per_core = minicpmv2_head_maping()"]
            C4["生成单核mask<br/>make_single_core_mask()"]
            C5["初始化Online-Softmax状态<br/>m_prev=-1e4, d_prev=0"]
            C6["初始化O_accum = 0"]
            
            subgraph "Online-Softmax处理"
                D1{"Block循环<br/>blk_start: 0→visible_kv_len<br/>step=BLOCK_M"}
                D2["从KV cache加载块<br/>slice_to_spad K/V_block"]
                D3["转置K_block→K_block_T<br/>为GEMV准备"]
                D4["计算attention scores<br/>S = Q_token @ K_block_T"]
                D5["缩放S *= 1/√d_k"]
                D6["🎉 无需Causal Mask<br/>天然因果性保证"]
                
                subgraph "Flash Attention更新"
                    E1["计算block最大值<br/>max_current = reduce_max(S)"]
                    E2["更新全局最大值<br/>m_new = max(m_prev, max_current)"]
                    E3["计算重标准化因子<br/>r = exp(m_prev - m_new)"]
                    E4["计算softmax<br/>P = exp(S - m_new)"]
                    E5["计算block分母<br/>sum_current = reduce_sum(P)"]
                    E6["更新全局分母<br/>d_new = d_prev * r + sum_current"]
                    E7["更新输出累积<br/>O_accum *= r + P @ V_block"]
                    E8["状态更新<br/>m_prev=m_new, d_prev=d_new"]
                end
            end
            
            C7["最终归一化<br/>O_accum /= d_prev"]
            C8["写回DRAM<br/>store(O_accum → O_out)"]
        end
    end

    A1 --> A2 --> A3 --> B1
    B1 --> B2 --> B3 --> C1
    C1 --> C2 --> C3 --> C4 --> C5 --> C6 --> D1
    
    D1 --> D2 --> D3 --> D4 --> D5 --> D6 --> E1
    E1 --> E2 --> E3 --> E4 --> E5 --> E6 --> E7 --> E8
    E8 --> D1
    D1 -->|完成所有blocks| C7 --> C8 --> C2
    C2 -->|完成所有cores| C1
    C1 -->|完成所有heads| B1
    B1 -->|完成所有tokens| F1["函数完成"]

    style A2 fill:#e1f5fe
    style B3 fill:#fff3e0
    style D6 fill:#f3e5f5
    style E2 fill:#e8f5e8
    style E6 fill:#fce4ec
    style E7 fill:#fff8e1
    style C7 fill:#c8e6c8
    style F1 fill:#e8f5e8
```

## 与标准方案的综合对比

| 方面 | 标准Prefill | Chunked FlashAttention | **Token-wise FlashAttention** | 最优选择 |
|------|-------------|------------------------|-----------------------------|----------|
| **内存使用** | O(N²) 完整矩阵 | O(1) 常数级 | **O(1) 常数级** | Token-wise ≈ Chunked |
| **因果性实现** | 后处理mask | 显式复杂mask | **天然保证，零开销** | **Token-wise** |
| **算法复杂度** | 简单 | 复杂 (4层嵌套) | **中等 (3层嵌套)** | **Token-wise** |
| **代码维护性** | 高 | 低 | **高 (复用decode)** | **Token-wise** |
| **内存需求** | 1.03MB | 12KB | **4.4KB** | **Token-wise** |
| **数值稳定性** | 中 | 高 | **高** | Token-wise ≈ Chunked |
| **调试友好性** | 高 | 低 | **高** | **Token-wise** |

## 总结

MiniCPMV2 FlashAttention Token-wise Prefill 实现具有以下特点：

### 🎯 **核心优势**
1. **天然因果性**：无需显式causal mask，结构保证因果关系
2. **内存高效**：4.4KB内存需求，相比标准attention节省234倍
3. **算法简洁**：复用decode成熟算法，降低实现复杂度
4. **易于维护**：代码结构清晰，复用度高，便于调试验证

### 💡 **关键创新**
1. **Token-wise Strategy**：逐token处理天然满足因果性
2. **Decode Algorithm Reuse**：复用Online-Softmax成熟实现
3. **Zero Causal Overhead**：无mask计算开销
4. **Optimal Memory Layout**：4.4KB高效利用SPAD

### 🚀 **性能表现**
- **内存效率**：相比标准attention节省234倍内存，比chunked方案节省63%
- **算法效率**：无causal mask开销，复用成熟算法
- **实现效率**：代码复用度高，维护成本低
- **硬件效率**：数据访问模式规律，硬件友好

### 📊 **适用场景**
- **快速原型**：需要快速实现和验证的场景
- **内存敏感**：SPAD/DRAM极度受限的嵌入式环境
- **算法研究**：需要清晰理解attention机制的场景
- **生产部署**：追求代码稳定性和可维护性的系统

### 🔄 **与Decode的协同优势**
- **算法一致性**：prefill和decode使用相同的Online-Softmax逻辑
- **代码复用**：大量函数和数据结构可以共享
- **调试便利**：熟悉decode调试的经验可以直接应用
- **性能预测**：基于decode性能可以准确估算prefill性能

这个实现完美展示了如何通过巧妙的算法设计（token-wise processing）来简化复杂问题（causal attention），在保持内存高效的同时大幅降低实现复杂度，是工程实践中算法设计的优秀案例。 