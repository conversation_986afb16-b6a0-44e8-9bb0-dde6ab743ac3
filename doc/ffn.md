```mermaid
flowchart TD
    subgraph "tiled_mm函数流程"
        A1["参数验证和常量设置<br/>TILE_N=16, TILE_D1=256, TILE_D2=64<br/>HALF_TILE_N=8"]
        A2["配置CIM和NPU mask<br/>npu_mask全部激活16核"]
        A3["N方向分块循环<br/>每次处理16行"]
        A4["X数据加载<br/>前8行→SPAD0, 后8行→SPAD1"]
        A5["d2方向分块循环<br/>每次处理64列"]
        A6["初始化输出Tensor<br/>SPAD0和SPAD1对应区域"]
        A7["权重分批处理<br/>最多16页per batch"]
        A8["页批次循环<br/>处理当前批次页面"]
        A9["权重加载到CIM<br/>当前批次所有页"]
        A10["单页处理循环<br/>处理每个256列tile"]
        A11["设置X_tile视图<br/>指向SPAD中对应位置"]
        A12["GEMM运算<br/>X_tile × W_tile → O_tile"]
        A13["存储结果<br/>SPAD→DRAM输出"]
    end
    
    A1 --> A2 --> A3 --> A4 --> A5 --> A6 --> A7 --> A8 --> A9 --> A10 --> A11 --> A12
    A12 --> A10
    A10 -->|完成当前批次| A8
    A8 -->|完成所有批次| A13 --> A5
    A5 -->|完成d2分块| A3
    A3 -->|完成N分块| A14["函数结束"]

    style A1 fill:#e1f5fe
    style A7 fill:#fff3e0
    style A12 fill:#e8f5e8
```


```mermaid
flowchart TD
    subgraph "minicpmv2_gu_proj_prefill函数流程"
        B1["参数设置和验证<br/>TILE_N=8, TILE_D1=256, TILE_D2=64<br/>获取4个group的权重地址"]
        B2["构造权重Tensor<br/>gu_proj [2304, 5760*2]<br/>gate和up projection拼接"]
        B3["N方向分块循环<br/>每次处理8行"]
        B4["X数据加载<br/>slice_to_spad → SPAD0"]
        B5["d2方向分块循环<br/>处理前半部分5760列<br/>每次64列"]
        
        subgraph "Gate Projection处理"
            C1["初始化gate输出<br/>O_tile_spad0 → SPAD2"]
            C2["4个Group循环<br/>每组独立处理"]
            C3["权重分批处理<br/>最多16页per batch"]
            C4["加载gate权重<br/>前半部分权重→CIM"]
            C5["页内循环<br/>处理256列tile"]
            C6["GEMM: X × W_gate → gate_result"]
        end
        
        C7["SiLU激活<br/>silu gate_result → gate_activated"]
        
        subgraph "Up Projection处理"
            D1["初始化up输出<br/>O_tile_spad1 → SPAD2+offset"]
            D2["4个Group循环<br/>每组独立处理"]
            D3["权重分批处理<br/>最多16页per batch"]
            D4["加载up权重<br/>后半部分权重→CIM"]
            D5["页内循环<br/>处理256列tile"]
            D6["GEMM: X × W_up → up_result"]
        end
        
        D7["Element-wise乘法<br/>gate_activated * up_result"]
        D8["存储最终结果<br/>SPAD→DRAM输出"]
    end
    
    B1 --> B2 --> B3 --> B4 --> B5 --> C1 --> C2 --> C3 --> C4 --> C5 --> C6
    C6 --> C5
    C5 -->|完成页内循环| C3
    C3 -->|完成批次| C2
    C2 -->|完成4组| C7 --> D1 --> D2 --> D3 --> D4 --> D5 --> D6
    D6 --> D5
    D5 -->|完成页内循环| D3
    D3 -->|完成批次| D2
    D2 -->|完成4组| D7 --> D8 --> B5
    B5 -->|完成d2分块| B3
    B3 -->|完成N分块| B9["函数结束"]

    style B1 fill:#e1f5fe
    style C7 fill:#fce4ec
    style D7 fill:#e8f5e8
```

```mermaid
sequenceDiagram
    participant Main as 主函数
    participant DRAM as DDR内存
    participant SPAD as Scratchpad
    participant CIM as CIM存储
    participant NPU as NPU核心

    Note over Main,NPU: tiled_mm函数执行
    loop N分块(16行each)
        Main->>DRAM: 读取X数据16行
        Main->>SPAD: 前8行→SPAD0, 后8行→SPAD1
        
        loop d2分块(64列each)
            Main->>SPAD: 初始化输出区域SPAD2
            
            loop 权重批次处理
                Main->>DRAM: 读取权重批次
                Main->>CIM: 加载权重到多页
                
                loop 页内处理
                    Main->>SPAD: 设置X_tile视图
                    NPU->>NPU: GEMM运算
                end
            end
            
            Main->>DRAM: 存储结果到输出
        end
    end

    Note over Main,NPU: minicpmv2_gu_proj_prefill函数执行
    loop N分块(8行each)
        Main->>DRAM: 读取X数据8行
        Main->>SPAD: X数据→SPAD0
        
        loop d2分块(前5760列,64列each)
            Note over Main,NPU: Gate Projection阶段
            Main->>SPAD: 初始化gate输出→SPAD2
            
            loop 4个Group
                loop 权重批次
                    Main->>DRAM: 读取gate权重
                    Main->>CIM: 加载到CIM页
                    
                    loop 页内tile
                        NPU->>NPU: GEMM gate运算
                    end
                end
            end
            
            NPU->>NPU: SiLU激活函数
            
            Note over Main,NPU: Up Projection阶段
            Main->>SPAD: 初始化up输出→SPAD2+offset
            
            loop 4个Group
                loop 权重批次
                    Main->>DRAM: 读取up权重
                    Main->>CIM: 加载到CIM页
                    
                    loop 页内tile
                        NPU->>NPU: GEMM up运算
                    end
                end
            end
            
            NPU->>NPU: Element-wise乘法
            Main->>DRAM: 存储最终结果
        end
    end
```

