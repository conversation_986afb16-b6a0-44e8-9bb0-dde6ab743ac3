#ifndef __CPUFEATURE__
#define __CPUFEATURE__
#define CPU_ISA "rv32imafdu_zicsr_zicntr_zifencei_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbs_zicbom_zicboz_zicbop_zicond_smepmp_zve32d_zvl128b_xxlnice"
#define CFG_CPU_NAME "n900vk"
#define CFG_CPU_VERSION "v4.2.0"
#define CFG_CPU_REV 0x40200
#define CFG_E16 0
#define CFG_XLEN 32
#define CFG_FLEN 64
// cpu feature 
#define CFG_HAS_EXCP
#define CFG_HAS_PMA
#define CFG_HAS_PMA_MACRO
#define CFG_PMA_CSR_NUM 8
#define CFG_PMA_SEC_CSR_NUM 0
#define CFG_CORE_PFX n900vk_
#define CFG_CPU_GEN_RTL_MODE part
#define CFG_CPU N900VKVK
#define CFG_CPU_IS_900
#define CFG_CPU_IS_N900VK
#define CFG_PA_SIZE 32
#define CFG_IREGION_BASE_ADDR 0x18000000
#define CFG_BOOTH_MUL_2CYC
#define CFG_HAS_ISA_ZBA_ZBB_ZBS
#define CFG_HAS_CODE_SIZE_DEC_EXT
#define CFG_HAS_PMP
#define CFG_PMP_ENTRY_NUM 8
#define CFG_HAS_DEBUG
#define CFG_DEBUG_TRIGM_NUM 4
#define CFG_NO_DEBUG_SBA
#define CFG_HAS_ILM
#define CFG_ILM_BASE_ADDR 0x70000000
#define CFG_ILM_ADDR_WIDTH 19
#define CFG_HAS_DLM
#define CFG_DLM_BASE_ADDR 0x70100000
#define CFG_DLM_ADDR_WIDTH 19
#define CFG_DLM_CTRL_NUM 1
#define CFG_ILM_ITF_TYPE_SRAM
#define CFG_DLM_ITF_TYPE_SRAM
#define CFG_SLV_AXI_IDW 8
#define CFG_HAS_ICACHE
#define CFG_ICACHE_ADDR_WIDTH 15
#define CFG_HAS_DCACHE
#define CFG_DCACHE_ADDR_WIDTH 15
#define CFG_DEVICE_REGION_NUM 1
#define CFG_DEVICE_REGION0_BASE 0x10000000
#define CFG_DEVICE_REGION0_MASK 0x00ffffff
#define CFG_CACHEABLE_REGION_NUM 0
#define CFG_NC_REGION_NUM 1
#define CFG_NC_REGION0_BASE 0x30000000
#define CFG_NC_REGION0_MASK 0x0000ffff
#define CFG_IRQ_NUM 64
#define CFG_NO_PLIC
#define CFG_HAS_CLIC
#define CFG_CLICINTCTLBITS 3
#define CFG_HAS_NICE
#define CFG_HAS_NICE_DECODE_EXT_OUTPUT_W 0
#define CFG_FPU_DOUBLE
#define CFG_FPU_SINGLE
#define CFG_5CYC_FPU
#define CFG_HAS_VPU
#define CFG_VLEN 128
#define CFG_VPU_PARAL 1
#define CFG_HAS_VNICE
#define CFG_VPU_DCACHE_DW 64
#define CFG_BHT_ENTRY_WIDTH 12
#define CFG_HAS_AGU_QUICK_FWD
#define CFG_RAM_DFT_IN_WID 0
#define CFG_RAM_DFT_OUT_WID 0
#define CFG_HAS_CSR_TIMING_OPT
#define CFG_HAS_ALL_DFF_RESET
#define CFG_MMU_TLB_ENTRIES 1024
#define CFG_HAS_FPU
#define CFG_HAS_RST_OPT
#define CFG_HAS_SHAD_WBCK
#define CFG_CC_SIZE 0
#define CFG_SMP_ASYNC 0
#define CFG_SMP_RATIO 0
#define CFG_HAS_AGU_ADDR_CROSS_4K
#define CFG_HAS_SDB_CMT_PRDT
#define CFG_HAS_STB_TIMEOUT
#define CFG_HAS_FENCE_DMB
#define CFG_CC_ACE_NUM 0
#define CFG_HAS_CLIC_INSIDE_CORE
#define CFG_HAS_DEBUG_PRIVATE
#define CFG_LINE_BYTES 64
#define CFG_HAS_BASE_ISA_EXTENSION
#define CFG_HAS_ZC_EXT
#define CFG_ADDR_SIZE 32
#define CFG_PPI_ADDR_WIDTH 20
#define CFG_PPI_BASE_ADDR 0x10000000
#define CFG_SYS_MEM_BASE 0xa0000000
#define CFG_HAS_AMO
#define CFG_MISALIGNED_ACCESS
#define CFG_HAS_ZAWRS
#define CFG_HAS_FSIM
#define CFG_HAS_RV32D
#define CFG_HAS_UMODE
#define CFG_HAS_BMU
#define CFG_HAS_ZCB
#define CFG_HAS_RF_CRC 0
#define CFG_HAS_ASILB 0
#define CFG_HAS_VADD
#define CFG_HAS_VMIC
#define CFG_HAS_VRED
#define CFG_HAS_VMAC
#define CFG_HAS_VDIV
#define CFG_HAS_VFPU
#define CFG_HAS_VPU_SHAD_FWD
#define CFG_4CYC_VFPU
#define CFG_HAS_RS_HI
#define CFG_HAS_RD_HI
#define CFG_HAS_RS3
#define CFG_HAS_RS3_PSTG
#define CFG_HAS_RS3_PSTG_FPUONLY 0
#define CFG_HAS_SMEPMP
#define CFG_HAS_PMONITOR
#define CFG_HAS_COFPMF
#define CFG_HAS_PMON_EXT
#define CFG_HPM_VER 2
#define CFG_PMON_NUM 0
#define CFG_HAS_HPM
#define CFG_XLEN_IS_32
#define CFG_REGNUM_IS_32
#define CFG_CLIC_VERSION 1
#define CFG_HAS_ECLIC
#define CFG_HAS_CLIC_EDGE
#define CFG_HAS_2_WIRE_DBG
#define CFG_HAS_DEBUG13
#define CFG_HAS_DEBUG_NEW
#define CFG_HAS_TRIGM
#define CFG_REGFILE_2WP
#define CFG_SYSMEM_DATA_WIDTH_IS_64
#define CFG_HAS_MEM_ITF
#define CFG_HAS_LM 1
#define CFG_HAS_ILM_SRAM 1
#define CFG_LSU_ACCESS_ILM
#define CFG_HAS_DLM_SRAM 1
#define CFG_HAS_L0BTB
#define CFG_HAS_BPU
#define CFG_HAS_DYNAMIC_BPU
#define CFG_HAS_DEVICE
#define CFG_MEM_REGION_NUM 0
#define CFG_HAS_NC
#define CFG_HAS_CMO
#define CFG_HAS_CACHE
#define CFG_HAS_IOCC
#define CFG_HAS_NC_BIU
#define CFG_HAS_ICACHE_IOCC
#define CFG_ICACHE_CANCEL
#define CFG_ICACHE_DLRU
#define CFG_HAS_DM_MAP
#define CFG_HAS_MULDIV
#define CFG_HAS_ZICOND_EXTENSION
#define CFG_HAS_RV32NICE
#define CFG_ITAG_DEPTH 15
#define CFG_HAS_LBIU
#define CFG_HAS_FLOAT_WORD_INS
#define CFG_HAS_NCDEV_REG
#define CFG_HAS_SUBSYS_LM 1
#define CFG_HAS_SUBSYS_CORE_SRAM 1
#define CFG_TMR_PRIVATE
#define CFG_TMR_BASE_ADDR 0x18030000
#define CFG_PMP_GRAIN 10
#define CFG_ILM_DATA_WIDTH 64
#define CFG_L1D_PREFETCH_VERSION 4
#define CFG_LSU_DW 128
// evalsoc feature 
#define EVALSOC_PERIPS_BASE 0x10000000
#define EVALSOC_FLASH_XIP_BASE 0x20000000
#define EVALSOC_FLASH_XIP_ADDR_WIDTH 28
#define EVALSOC_SYSMEM_BASE 0x80000000
#define EVALSOC_SYSMEM_ADDR_WIDTH 31
#define EVALSOC_CFG_IRAM_BASE_ADDR 0x60000000
#define EVALSOC_CFG_IRAM_ADDR_WIDTH 16
#define EVALSOC_CFG_DRAM_BASE_ADDR 0x68000000
#define EVALSOC_CFG_DRAM_ADDR_WIDTH 16
#define EVALSOC_CFG_PERIPS_BASE 0x10000000
#define EVALSOC_CFG_FLASH_XIP_BASE 0x20000000
#define EVALSOC_CFG_FLASH_XIP_ADDR_WIDTH 28
#define EVALSOC_CFG_SYSMEM_BASE 0x80000000
#define EVALSOC_CFG_SYSMEM_ADDR_WIDTH 31
#define EVALSOC_HAS_DDR_ICB_CMD_DELAY
#define EVALSOC_HAS_CPU_RAM_WRAPPER
#define EVALSOC_VERSION_IS_2
#define EVALSOC_HAS_BIU2ILM
#define EVALSOC_HAS_BIU2DLM
#define EVALSOC_HAS_SUBSYS_MISC
#define EVALSOC_HAS_SDIO
#define EVALSOC_IOCP_BASE 0x50000000
#define EVALSOC_IOCP_ADDR_WIDTH 28
#define EVALSOC_HAS_ETHERNET
#define EVALSOC_HAS_UART_MODEL
#define EVALSOC_HAS_IRAM_DRAM
#define EVALSOC_IRAM_BASE_ADDR 0x60000000
#define EVALSOC_IRAM_DATA_WIDTH 64
#define EVALSOC_IRAM_WMSK_WIDTH 8
#define EVALSOC_IRAM_AW_LSB 3
#define EVALSOC_IRAM_ADDR_WIDTH 16
#define EVALSOC_DRAM_BASE_ADDR 0x68000000
#define EVALSOC_DRAM_DATA_WIDTH 64
#define EVALSOC_DRAM_WMSK_WIDTH 8
#define EVALSOC_DRAM_AW_LSB 3
#define EVALSOC_DRAM_ADDR_WIDTH 16
#define EVALSOC_IRAM_RAM_DP 8192
#define EVALSOC_IRAM_RAM_AW 13
#define EVALSOC_DRAM_RAM_DP 8192
#define EVALSOC_DRAM_RAM_AW 13
#endif
