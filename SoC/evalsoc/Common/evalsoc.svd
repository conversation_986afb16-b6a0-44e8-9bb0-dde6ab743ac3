<?xml version="1.0" encoding="utf-8"?>
<device schemaVersion="1.1" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="CMSIS-SVD_Schema_1_0.xsd" >
  <vendor>Nuclei</vendor>
  <vendorID>Nuclei</vendorID>
  <name>evalsoc</name>
  <series>Nuclei Evaluation SoC</series>
  <version></version>
  <description>Nuclei Evaluation SoC using Nuclei N/NX Core
  </description>

  <addressUnitBits>8</addressUnitBits>
  <width>32</width>
  <size>32</size>
  <resetValue>0x00000000</resetValue>
  <resetMask>0xFFFFFFFF</resetMask>

  <cpu>
    <name>Evalsoc</name>
    <endian>little</endian>
  </cpu>

  <peripherals>

    <peripheral>
      <name>ECLIC</name>
      <description>Enhanced Core Local Interrupt Controller</description>
      <groupName>ECLIC</groupName>
      <baseAddress>0x18020000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0xFFFF</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>CLICCFG</name>
          <displayName>CLICCFG</displayName>
          <description>cliccfg Register</description>
          <addressOffset>0x0</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>NLBITS</name>
              <description>NLBITS</description>
              <bitOffset>1</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CLICINFO</name>
          <displayName>CLICINFO</displayName>
          <description>clicinfo Register</description>
          <addressOffset>0x04</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NUM_INTERRUPT</name>
              <description>NUM_INTERRUPT</description>
              <bitOffset>0</bitOffset>
              <bitWidth>13</bitWidth>
            </field>
            <field>
              <name>VERSION</name>
              <description>VERSION</description>
              <bitOffset>13</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>CLICINTCTLBITS</name>
              <description>CLICINTCTLBITS</description>
              <bitOffset>21</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>MTH</name>
          <displayName>MTH</displayName>
          <description>MTH Register</description>
          <addressOffset>0x0b</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>MTH</name>
              <description>MTH</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CLICINTIP_0</name>
          <displayName>CLICINTIP_0</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1000</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_1</name>
          <displayName>CLICINTIP_1</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1004</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CLICINTIP_2</name>
          <displayName>CLICINTIP_2</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1008</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_3</name>
          <displayName>CLICINTIP_3</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x100C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_4</name>
          <displayName>CLICINTIP_4</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1010</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CLICINTIP_5</name>
          <displayName>CLICINTIP_5</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1014</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_6</name>
          <displayName>CLICINTIP_6</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1018</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_7</name>
          <displayName>CLICINTIP_7</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x101C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_8</name>
          <displayName>CLICINTIP_8</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1020</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_9</name>
          <displayName>CLICINTIP_9</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1024</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_10</name>
          <displayName>CLICINTIP_10</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1028</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_11</name>
          <displayName>CLICINTIP_11</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x102C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_12</name>
          <displayName>CLICINTIP_12</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1030</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_13</name>
          <displayName>CLICINTIP_13</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1034</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_14</name>
          <displayName>CLICINTIP_14</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1038</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_15</name>
          <displayName>CLICINTIP_15</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x103C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_16</name>
          <displayName>CLICINTIP_16</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1040</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_17</name>
          <displayName>CLICINTIP_17</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1044</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_18</name>
          <displayName>CLICINTIP_18</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1048</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_19</name>
          <displayName>CLICINTIP_19</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x104C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_20</name>
          <displayName>CLICINTIP_20</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1050</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_21</name>
          <displayName>CLICINTIP_21</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1054</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_22</name>
          <displayName>CLICINTIP_22</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1058</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_23</name>
          <displayName>CLICINTIP_23</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x105C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_24</name>
          <displayName>CLICINTIP_24</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1060</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_25</name>
          <displayName>CLICINTIP_25</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1064</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_26</name>
          <displayName>CLICINTIP_26</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1068</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_27</name>
          <displayName>CLICINTIP_27</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x106C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_28</name>
          <displayName>CLICINTIP_28</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1070</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_29</name>
          <displayName>CLICINTIP_29</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1074</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_30</name>
          <displayName>CLICINTIP_30</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1078</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_31</name>
          <displayName>CLICINTIP_31</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x107C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_32</name>
          <displayName>CLICINTIP_32</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1080</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_33</name>
          <displayName>CLICINTIP_33</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1084</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_34</name>
          <displayName>CLICINTIP_34</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1088</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_35</name>
          <displayName>CLICINTIP_35</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x108C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_36</name>
          <displayName>CLICINTIP_36</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1090</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_37</name>
          <displayName>CLICINTIP_37</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1094</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_38</name>
          <displayName>CLICINTIP_38</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1098</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_39</name>
          <displayName>CLICINTIP_39</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x109C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_40</name>
          <displayName>CLICINTIP_40</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10A0</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_41</name>
          <displayName>CLICINTIP_41</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10A4</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_42</name>
          <displayName>CLICINTIP_42</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10A8</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_43</name>
          <displayName>CLICINTIP_43</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10AC</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_44</name>
          <displayName>CLICINTIP_44</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10B0</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_45</name>
          <displayName>CLICINTIP_45</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10B4</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_46</name>
          <displayName>CLICINTIP_46</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10B8</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_47</name>
          <displayName>CLICINTIP_47</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10BC</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_48</name>
          <displayName>CLICINTIP_48</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10C0</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_49</name>
          <displayName>CLICINTIP_49</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10C4</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_50</name>
          <displayName>CLICINTIP_50</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10C8</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_51</name>
          <displayName>CLICINTIP_51</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10CC</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_52</name>
          <displayName>CLICINTIP_52</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10D0</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_53</name>
          <displayName>CLICINTIP_53</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10D4</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_54</name>
          <displayName>CLICINTIP_54</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10D8</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_55</name>
          <displayName>CLICINTIP_55</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10DC</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_56</name>
          <displayName>CLICINTIP_56</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10E0</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_57</name>
          <displayName>CLICINTIP_57</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10E4</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_58</name>
          <displayName>CLICINTIP_58</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10E8</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_59</name>
          <displayName>CLICINTIP_59</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10EC</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_60</name>
          <displayName>CLICINTIP_60</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10F0</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_61</name>
          <displayName>CLICINTIP_61</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10F4</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_62</name>
          <displayName>CLICINTIP_62</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10F8</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_63</name>
          <displayName>CLICINTIP_63</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x10FC</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_64</name>
          <displayName>CLICINTIP_64</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1100</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_65</name>
          <displayName>CLICINTIP_65</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1104</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_66</name>
          <displayName>CLICINTIP_66</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1108</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_67</name>
          <displayName>CLICINTIP_67</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x110C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
       <register>
          <name>CLICINTIP_68</name>
          <displayName>CLICINTIP_68</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1110</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_69</name>
          <displayName>CLICINTIP_69</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1114</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_70</name>
          <displayName>CLICINTIP_70</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1118</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_71</name>
          <displayName>CLICINTIP_71</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x111C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_72</name>
          <displayName>CLICINTIP_72</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1120</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_73</name>
          <displayName>CLICINTIP_73</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1124</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_74</name>
          <displayName>CLICINTIP_74</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1128</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_75</name>
          <displayName>CLICINTIP_75</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x112C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_76</name>
          <displayName>CLICINTIP_76</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1130</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_77</name>
          <displayName>CLICINTIP_77</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1134</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_78</name>
          <displayName>CLICINTIP_78</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1138</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_79</name>
          <displayName>CLICINTIP_79</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x113C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_80</name>
          <displayName>CLICINTIP_80</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1140</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_81</name>
          <displayName>CLICINTIP_81</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1144</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_82</name>
          <displayName>CLICINTIP_82</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1148</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_83</name>
          <displayName>CLICINTIP_83</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x114C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_84</name>
          <displayName>CLICINTIP_84</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1150</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_85</name>
          <displayName>CLICINTIP_85</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x1158</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      <register>
          <name>CLICINTIP_86</name>
          <displayName>CLICINTIP_86</displayName>
          <description>clicintip  Register</description>
          <addressOffset>0x115C</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IP</name>
              <description>IP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CLICINTIE_0</name>
          <displayName>CLICINTIE_0</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1001</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
           </fields>
        </register>
       <register>
          <name>CLICINTIE_1</name>
          <displayName>CLICINTIE_1</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1005</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_2</name>
          <displayName>CLICINTIE_2</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1009</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_3</name>
          <displayName>CLICINTIE_3</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x100D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_4</name>
          <displayName>CLICINTIE_4</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1011</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_5</name>
          <displayName>CLICINTIE_5</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1015</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_6</name>
          <displayName>CLICINTIE_6</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1019</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_7</name>
          <displayName>CLICINTIE_7</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x101D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_8</name>
          <displayName>CLICINTIE_8</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1021</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_9</name>
          <displayName>CLICINTIE_9</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1025</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_10</name>
          <displayName>CLICINTIE_10</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1029</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_11</name>
          <displayName>CLICINTIE_11</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x102D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_12</name>
          <displayName>CLICINTIE_12</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1031</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_13</name>
          <displayName>CLICINTIE_13</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1035</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_14</name>
          <displayName>CLICINTIE_14</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1039</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_15</name>
          <displayName>CLICINTIE_15</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x103D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_16</name>
          <displayName>CLICINTIE_16</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1041</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_17</name>
          <displayName>CLICINTIE_17</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1045</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_18</name>
          <displayName>CLICINTIE_18</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1049</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_19</name>
          <displayName>CLICINTIE_19</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x104D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_20</name>
          <displayName>CLICINTIE_20</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1051</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_21</name>
          <displayName>CLICINTIE_21</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1055</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_22</name>
          <displayName>CLICINTIE_22</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1059</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_23</name>
          <displayName>CLICINTIE_23</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x105D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_24</name>
          <displayName>CLICINTIE_24</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1061</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_25</name>
          <displayName>CLICINTIE_25</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1065</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_26</name>
          <displayName>CLICINTIE_26</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1069</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_27</name>
          <displayName>CLICINTIE_27</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x106D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_28</name>
          <displayName>CLICINTIE_28</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1071</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_29</name>
          <displayName>CLICINTIE_29</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1075</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_30</name>
          <displayName>CLICINTIE_30</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1079</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_31</name>
          <displayName>CLICINTIE_31</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x107D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_32</name>
          <displayName>CLICINTIE_32</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1081</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_33</name>
          <displayName>CLICINTIE_33</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1085</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_34</name>
          <displayName>CLICINTIE_34</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1089</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_35</name>
          <displayName>CLICINTIE_35</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x108D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_36</name>
          <displayName>CLICINTIE_36</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1091</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_37</name>
          <displayName>CLICINTIE_37</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1095</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_38</name>
          <displayName>CLICINTIE_38</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1099</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_39</name>
          <displayName>CLICINTIE_39</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x109D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_40</name>
          <displayName>CLICINTIE_40</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10A1</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_41</name>
          <displayName>CLICINTIE_41</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10A5</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_42</name>
          <displayName>CLICINTIE_42</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10A9</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_43</name>
          <displayName>CLICINTIE_43</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10AD</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_44</name>
          <displayName>CLICINTIE_44</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10B1</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_45</name>
          <displayName>CLICINTIE_45</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10B5</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_46</name>
          <displayName>CLICINTIE_46</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10B9</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_47</name>
          <displayName>CLICINTIE_47</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10BD</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_48</name>
          <displayName>CLICINTIE_48</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10C1</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_49</name>
          <displayName>CLICINTIE_49</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10C5</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_50</name>
          <displayName>CLICINTIE_50</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10C9</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_51</name>
          <displayName>CLICINTIE_51</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10CD</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_52</name>
          <displayName>CLICINTIE_52</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10D1</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_53</name>
          <displayName>CLICINTIE_53</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10D5</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_54</name>
          <displayName>CLICINTIE_54</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10D9</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_55</name>
          <displayName>CLICINTIE_7</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10DD</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_56</name>
          <displayName>CLICINTIE_56</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10E1</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_57</name>
          <displayName>CLICINTIE_57</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10E5</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_58</name>
          <displayName>CLICINTIE_58</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10E9</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_59</name>
          <displayName>CLICINTIE_59</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10ED</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_60</name>
          <displayName>CLICINTIE_60</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10F1</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_61</name>
          <displayName>CLICINTIE_61</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10F5</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_62</name>
          <displayName>CLICINTIE_62</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10F9</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_63</name>
          <displayName>CLICINTIE_63</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x10FD</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_64</name>
          <displayName>CLICINTIE_64</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1101</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_65</name>
          <displayName>CLICINTIE_65</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1105</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_66</name>
          <displayName>CLICINTIE_66</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1109</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_67</name>
          <displayName>CLICINTIE_67</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x110D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_68</name>
          <displayName>CLICINTIE_68</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1111</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_69</name>
          <displayName>CLICINTIE_69</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1115</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_70</name>
          <displayName>CLICINTIE_70</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1119</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_71</name>
          <displayName>CLICINTIE_71</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x111D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_72</name>
          <displayName>CLICINTIE_72</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1121</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_73</name>
          <displayName>CLICINTIE_73</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1125</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_74</name>
          <displayName>CLICINTIE_74</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1129</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_75</name>
          <displayName>CLICINTIE_75</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x112D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_76</name>
          <displayName>CLICINTIE_76</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1131</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_77</name>
          <displayName>CLICINTIE_77</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1135</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_78</name>
          <displayName>CLICINTIE_78</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1139</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_79</name>
          <displayName>CLICINTIE_79</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x113D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_80</name>
          <displayName>CLICINTIE_80</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1141</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_81</name>
          <displayName>CLICINTIE_81</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1145</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_82</name>
          <displayName>CLICINTIE_82</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1149</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_83</name>
          <displayName>CLICINTIE_83</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x114D</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_84</name>
          <displayName>CLICINTIE_84</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1151</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_85</name>
          <displayName>CLICINTIE_85</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1155</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>
       <register>
          <name>CLICINTIE_86</name>
          <displayName>CLICINTIE_86</displayName>
          <description>clicintie Register</description>
          <addressOffset>0x1159</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>IE</name>
              <description>IE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
         </fields>
        </register>

      <register>
          <name>CLICINTATTR_0</name>
          <displayName>CLICINTIE_0</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1002</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_1</name>
          <displayName>CLICINTIE_1</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1006</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_2</name>
          <displayName>CLICINTIE_2</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x100A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_3</name>
          <displayName>CLICINTIE_3</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x100E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_4</name>
          <displayName>CLICINTIE_4</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1012</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_5</name>
          <displayName>CLICINTIE_5</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1016</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_6</name>
          <displayName>CLICINTIE_6</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x101A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_7</name>
          <displayName>CLICINTIE_7</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x101E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_8</name>
          <displayName>CLICINTIE_8</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1022</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_9</name>
          <displayName>CLICINTIE_9</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1026</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_10</name>
          <displayName>CLICINTIE_10</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x102A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_11</name>
          <displayName>CLICINTIE_11</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x102E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_12</name>
          <displayName>CLICINTIE_12</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1032</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_13</name>
          <displayName>CLICINTIE_13</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1036</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_14</name>
          <displayName>CLICINTIE_14</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x103A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_15</name>
          <displayName>CLICINTIE_15</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x103E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_16</name>
          <displayName>CLICINTIE_16</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1042</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_17</name>
          <displayName>CLICINTIE_17</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1046</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_18</name>
          <displayName>CLICINTIE_18</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x104A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_19</name>
          <displayName>CLICINTIE_19</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x104E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_20</name>
          <displayName>CLICINTIE_20</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1052</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_21</name>
          <displayName>CLICINTIE_21</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1056</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_22</name>
          <displayName>CLICINTIE_22</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x105A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_23</name>
          <displayName>CLICINTIE_23</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x105E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_24</name>
          <displayName>CLICINTIE_24</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1062</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_25</name>
          <displayName>CLICINTIE_25</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1066</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_26</name>
          <displayName>CLICINTIE_26</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x106A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_27</name>
          <displayName>CLICINTIE_27</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x106E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_28</name>
          <displayName>CLICINTIE_28</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1072</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_29</name>
          <displayName>CLICINTIE_29</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1076</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_30</name>
          <displayName>CLICINTIE_30</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x107A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_31</name>
          <displayName>CLICINTIE_31</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x107E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_32</name>
          <displayName>CLICINTIE_32</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1082</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_33</name>
          <displayName>CLICINTIE_33</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1086</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_34</name>
          <displayName>CLICINTIE_34</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x108A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_35</name>
          <displayName>CLICINTIE_35</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x108E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_36</name>
          <displayName>CLICINTIE_36</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1092</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_37</name>
          <displayName>CLICINTIE_37</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1096</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_38</name>
          <displayName>CLICINTIE_38</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x109A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_39</name>
          <displayName>CLICINTIE_39</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x109E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_40</name>
          <displayName>CLICINTIE_40</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10A2</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_41</name>
          <displayName>CLICINTIE_41</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10A6</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_42</name>
          <displayName>CLICINTIE_42</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10AA</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_43</name>
          <displayName>CLICINTIE_43</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10AE</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_44</name>
          <displayName>CLICINTIE_44</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10B2</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_45</name>
          <displayName>CLICINTIE_45</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10B6</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_46</name>
          <displayName>CLICINTIE_46</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10BA</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_47</name>
          <displayName>CLICINTIE_47</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10BE</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_48</name>
          <displayName>CLICINTIE_48</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10C2</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_49</name>
          <displayName>CLICINTIE_49</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10C6</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_50</name>
          <displayName>CLICINTIE_50</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10CA</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_51</name>
          <displayName>CLICINTIE_51</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10CE</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_52</name>
          <displayName>CLICINTIE_52</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10D2</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_53</name>
          <displayName>CLICINTIE_53</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10D6</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_54</name>
          <displayName>CLICINTIE_54</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10DA</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_55</name>
          <displayName>CLICINTIE_55</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10DE</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_56</name>
          <displayName>CLICINTIE_56</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10E2</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_57</name>
          <displayName>CLICINTIE_57</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10E6</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_58</name>
          <displayName>CLICINTIE_58</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10EA</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_59</name>
          <displayName>CLICINTIE_59</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10EE</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_60</name>
          <displayName>CLICINTIE_60</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10F2</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_61</name>
          <displayName>CLICINTIE_61</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10F6</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_62</name>
          <displayName>CLICINTIE_62</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10FA</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_63</name>
          <displayName>CLICINTIE_63</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x10FE</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_64</name>
          <displayName>CLICINTIE_64</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1102</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_65</name>
          <displayName>CLICINTIE_65</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1106</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_66</name>
          <displayName>CLICINTIE_66</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x110A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_67</name>
          <displayName>CLICINTIE_67</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x110E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_68</name>
          <displayName>CLICINTIE_68</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1112</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_69</name>
          <displayName>CLICINTIE_69</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1116</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_70</name>
          <displayName>CLICINTIE_70</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x111A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_71</name>
          <displayName>CLICINTIE_71</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x111E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_72</name>
          <displayName>CLICINTIE_72</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1122</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_73</name>
          <displayName>CLICINTIE_73</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1126</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_74</name>
          <displayName>CLICINTIE_74</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x112A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_75</name>
          <displayName>CLICINTIE_75</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x112E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_76</name>
          <displayName>CLICINTIE_76</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1132</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_77</name>
          <displayName>CLICINTIE_77</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1136</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_78</name>
          <displayName>CLICINTIE_78</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x113A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_79</name>
          <displayName>CLICINTIE_79</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x113E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_80</name>
          <displayName>CLICINTIE_80</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1142</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_81</name>
          <displayName>CLICINTIE_81</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1146</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_82</name>
          <displayName>CLICINTIE_82</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x114A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_83</name>
          <displayName>CLICINTIE_83</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x114E</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTATTR_84</name>
          <displayName>CLICINTIE_84</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1152</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_85</name>
          <displayName>CLICINTIE_85</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x1156</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTATTR_86</name>
          <displayName>CLICINTIE_86</displayName>
          <description>clicintattr Register</description>
          <addressOffset>0x115A</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>SHV</name>
              <description>SHV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
             <field>
              <name>TRIG</name>
              <description>TRIG</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
         </fields>
        </register>

      <register>
          <name>CLICINTCTL_0</name>
          <displayName>CLICINTCTL_0</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1003</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_1</name>
          <displayName>CLICINTCTL_1</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1007</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_2</name>
          <displayName>CLICINTCTL_2</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x100B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_3</name>
          <displayName>CLICINTCTL_3</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x100F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_4</name>
          <displayName>CLICINTCTL_4</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1013</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_5</name>
          <displayName>CLICINTCTL_5</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1017</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_6</name>
          <displayName>CLICINTCTL_6</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x101B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_7</name>
          <displayName>CLICINTCTL_7</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x101F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_8</name>
          <displayName>CLICINTCTL_8</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1023</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_9</name>
          <displayName>CLICINTCTL_9</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1027</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_10</name>
          <displayName>CLICINTCTL_10</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x102B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_11</name>
          <displayName>CLICINTCTL_11</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x102F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_12</name>
          <displayName>CLICINTCTL_12</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1033</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_13</name>
          <displayName>CLICINTCTL_13</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1037</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_14</name>
          <displayName>CLICINTCTL_14</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x103B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_15</name>
          <displayName>CLICINTCTL_15</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x103F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_16</name>
          <displayName>CLICINTCTL_16</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1043</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_17</name>
          <displayName>CLICINTCTL_17</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1047</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_18</name>
          <displayName>CLICINTCTL_18</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x104B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_19</name>
          <displayName>CLICINTCTL_19</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x104F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_20</name>
          <displayName>CLICINTCTL_20</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1053</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_21</name>
          <displayName>CLICINTCTL_21</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1057</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_22</name>
          <displayName>CLICINTCTL_22</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x105B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_23</name>
          <displayName>CLICINTCTL_23</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x105F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_24</name>
          <displayName>CLICINTCTL_24</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1063</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_25</name>
          <displayName>CLICINTCTL_25</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1067</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_26</name>
          <displayName>CLICINTCTL_26</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x106B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_27</name>
          <displayName>CLICINTCTL_27</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x106F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_28</name>
          <displayName>CLICINTCTL_28</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1073</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_29</name>
          <displayName>CLICINTCTL_29</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1077</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_30</name>
          <displayName>CLICINTCTL_30</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x107B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_31</name>
          <displayName>CLICINTCTL_31</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x107F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_32</name>
          <displayName>CLICINTCTL_32</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1083</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_33</name>
          <displayName>CLICINTCTL_33</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1087</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_34</name>
          <displayName>CLICINTCTL_34</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x108B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_35</name>
          <displayName>CLICINTCTL_35</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x108F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_36</name>
          <displayName>CLICINTCTL_36</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1093</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_37</name>
          <displayName>CLICINTCTL_37</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1097</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_38</name>
          <displayName>CLICINTCTL_38</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x109B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_39</name>
          <displayName>CLICINTCTL_39</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x109F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_40</name>
          <displayName>CLICINTCTL_40</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10A3</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_41</name>
          <displayName>CLICINTCTL_41</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10A7</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_42</name>
          <displayName>CLICINTCTL_42</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10AB</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_43</name>
          <displayName>CLICINTCTL_43</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10AF</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_44</name>
          <displayName>CLICINTCTL_44</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10B3</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_45</name>
          <displayName>CLICINTCTL_45</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10B7</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_46</name>
          <displayName>CLICINTCTL_46</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10BB</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_47</name>
          <displayName>CLICINTCTL_47</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10BF</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_48</name>
          <displayName>CLICINTCTL_48</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10C3</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_49</name>
          <displayName>CLICINTCTL_49</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10C7</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_50</name>
          <displayName>CLICINTCTL_50</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10CB</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_51</name>
          <displayName>CLICINTCTL_51</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10CF</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_52</name>
          <displayName>CLICINTCTL_52</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10D3</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_53</name>
          <displayName>CLICINTCTL_53</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10D7</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_54</name>
          <displayName>CLICINTCTL_54</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10DB</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_55</name>
          <displayName>CLICINTCTL_55</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10DF</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_56</name>
          <displayName>CLICINTCTL_56</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10E3</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_57</name>
          <displayName>CLICINTCTL_57</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10E7</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_58</name>
          <displayName>CLICINTCTL_58</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10EB</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_59</name>
          <displayName>CLICINTCTL_59</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10EF</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_60</name>
          <displayName>CLICINTCTL_60</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10F3</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_61</name>
          <displayName>CLICINTCTL_61</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10F7</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_62</name>
          <displayName>CLICINTCTL_62</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10FB</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_63</name>
          <displayName>CLICINTCTL_63</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x10FF</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_64</name>
          <displayName>CLICINTCTL_64</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1103</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_65</name>
          <displayName>CLICINTCTL_65</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1107</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_66</name>
          <displayName>CLICINTCTL_66</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x110B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_67</name>
          <displayName>CLICINTCTL_67</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x110F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_68</name>
          <displayName>CLICINTCTL_68</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1113</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_69</name>
          <displayName>CLICINTCTL_69</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1117</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_70</name>
          <displayName>CLICINTCTL_70</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x111B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_71</name>
          <displayName>CLICINTCTL_71</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x111F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_72</name>
          <displayName>CLICINTCTL_72</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1123</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_73</name>
          <displayName>CLICINTCTL_73</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1127</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_74</name>
          <displayName>CLICINTCTL_74</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x112B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_75</name>
          <displayName>CLICINTCTL_75</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x112F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_76</name>
          <displayName>CLICINTCTL_76</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1133</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_77</name>
          <displayName>CLICINTCTL_77</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1137</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_78</name>
          <displayName>CLICINTCTL_78</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x113B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_79</name>
          <displayName>CLICINTCTL_79</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x113F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_80</name>
          <displayName>CLICINTCTL_80</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1143</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_81</name>
          <displayName>CLICINTCTL_81</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1147</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_82</name>
          <displayName>CLICINTCTL_82</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x114B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_83</name>
          <displayName>CLICINTCTL_83</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x114F</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_84</name>
          <displayName>CLICINTCTL_84</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1153</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
      <register>
          <name>CLICINTCTL_85</name>
          <displayName>CLICINTCTL_85</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x1157</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
     <register>
          <name>CLICINTCTL_86</name>
          <displayName>CLICINTCTL_86</displayName>
          <description>clicintctl Register</description>
          <addressOffset>0x115B</addressOffset>
          <size>0x08</size>
          <access>read-write</access>
          <resetValue>0x00</resetValue>
          <fields>
            <field>
              <name>LEVEL_PRIORITY</name>
              <description>LEVEL_PRIORITY</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
         </fields>
        </register>
       </registers>
      </peripheral>

    <peripheral>
      <name>MTIMER</name>
      <description>System Timer.</description>
      <baseAddress>0x18030000</baseAddress>
      <groupName>TIMER</groupName>
      <size>32</size>
      <access>read-write</access>

      <addressBlock>
        <offset>0</offset>
        <size>0x10000</size>
        <usage>registers</usage>
      </addressBlock>

      <registers>

        <register>
          <name>MTIME_LO</name>
          <description>Machine Timer Register Low.</description>
          <addressOffset>0x0</addressOffset>
        </register><register>
          <name>MTIME_HI</name>
          <description>Machine Timer Register High.</description>
          <addressOffset>0x4</addressOffset>
        </register>

        <register>
          <name>MTIMECMP_LO</name>
          <description>Machine Timer Compare Register Low.</description>
          <addressOffset>0x8</addressOffset>
        </register>

        <register>
          <name>MTIMECMP_HI</name>
          <description>Machine Timer Compare Register High.</description>
          <addressOffset>0xC</addressOffset>
        </register><register>
          <name>MSFTRST</name>
          <description>Machine Timer Software Core Reset Register.</description>
          <addressOffset>0xFF0</addressOffset>
        </register><register>
          <name>MIMECTL</name>
          <description>Machine Timer Control Register.</description>
          <addressOffset>0xFF8</addressOffset>
        </register><register>
          <name>MSIP</name>
          <description>Machine Software Interrupt Pending Register.</description>
          <addressOffset>0xFFC</addressOffset>
        </register>
      </registers>
    </peripheral> <!-- TIMER -->

    <peripheral>
      <name>GPIO0</name>
      <description>General purpose input/output controller.</description>
      <baseAddress>0x10012000</baseAddress>
      <groupName>GPIO</groupName>
      <size>32</size>
      <access>read-write</access>

      <addressBlock>
        <offset>0</offset>
        <size>0x1000</size>
        <usage>registers</usage>
      </addressBlock>

      <interrupt><name>GPIO_0_IRQ</name><value>8</value></interrupt>
      <interrupt><name>GPIO_1_IRQ</name><value>9</value></interrupt>
      <interrupt><name>GPIO_2_IRQ</name><value>10</value></interrupt>
      <interrupt><name>GPIO_3_IRQ</name><value>11</value></interrupt>
      <interrupt><name>GPIO_4_IRQ</name><value>12</value></interrupt>
      <interrupt><name>GPIO_5_IRQ</name><value>13</value></interrupt>
      <interrupt><name>GPIO_6_IRQ</name><value>14</value></interrupt>
      <interrupt><name>GPIO_7_IRQ</name><value>15</value></interrupt>
      <interrupt><name>GPIO_8_IRQ</name><value>16</value></interrupt>
      <interrupt><name>GPIO_9_IRQ</name><value>17</value></interrupt>
      <interrupt><name>GPIO_10_IRQ</name><value>18</value></interrupt>
      <interrupt><name>GPIO_11_IRQ</name><value>19</value></interrupt>
      <interrupt><name>GPIO_12_IRQ</name><value>20</value></interrupt>
      <interrupt><name>GPIO_12_IRQ</name><value>21</value></interrupt>
      <interrupt><name>GPIO_14_IRQ</name><value>22</value></interrupt>
      <interrupt><name>GPIO_14_IRQ</name><value>23</value></interrupt>
      <interrupt><name>GPIO_16_IRQ</name><value>24</value></interrupt>
      <interrupt><name>GPIO_17_IRQ</name><value>25</value></interrupt>
      <interrupt><name>GPIO_18_IRQ</name><value>26</value></interrupt>
      <interrupt><name>GPIO_19_IRQ</name><value>27</value></interrupt>
      <interrupt><name>GPIO_20_IRQ</name><value>28</value></interrupt>
      <interrupt><name>GPIO_21_IRQ</name><value>29</value></interrupt>
      <interrupt><name>GPIO_22_IRQ</name><value>30</value></interrupt>
      <interrupt><name>GPIO_23_IRQ</name><value>31</value></interrupt>
      <interrupt><name>GPIO_24_IRQ</name><value>32</value></interrupt>
      <interrupt><name>GPIO_25_IRQ</name><value>33</value></interrupt>
      <interrupt><name>GPIO_26_IRQ</name><value>34</value></interrupt>
      <interrupt><name>GPIO_27_IRQ</name><value>35</value></interrupt>
      <interrupt><name>GPIO_28_IRQ</name><value>36</value></interrupt>
      <interrupt><name>GPIO_28_IRQ</name><value>37</value></interrupt>
      <interrupt><name>GPIO_30_IRQ</name><value>38</value></interrupt>
      <interrupt><name>GPIO_31_IRQ</name><value>39</value></interrupt>

      <registers>
        <register>
          <name>VALUE</name>
          <description>Pin value.</description>
          <addressOffset>0x000</addressOffset>
          <fields>
            <field><name>PIN0</name><lsb>0</lsb><msb>0</msb></field>
            <field><name>PIN1</name><lsb>1</lsb><msb>1</msb></field>
            <field><name>PIN2</name><lsb>2</lsb><msb>2</msb></field>
            <field><name>PIN3</name><lsb>3</lsb><msb>3</msb></field>
            <field><name>PIN4</name><lsb>4</lsb><msb>4</msb></field>
            <field><name>PIN5</name><lsb>5</lsb><msb>5</msb></field>
            <field><name>PIN6</name><lsb>6</lsb><msb>6</msb></field>
            <field><name>PIN7</name><lsb>7</lsb><msb>7</msb></field>
            <field><name>PIN8</name><lsb>8</lsb><msb>8</msb></field>
            <field><name>PIN9</name><lsb>9</lsb><msb>9</msb></field>
            <field><name>PIN10</name><lsb>10</lsb><msb>10</msb></field>
            <field><name>PIN11</name><lsb>11</lsb><msb>11</msb></field>
            <field><name>PIN12</name><lsb>12</lsb><msb>12</msb></field>
            <field><name>PIN13</name><lsb>13</lsb><msb>13</msb></field>
            <field><name>PIN14</name><lsb>14</lsb><msb>14</msb></field>
            <field><name>PIN15</name><lsb>15</lsb><msb>15</msb></field>
            <field><name>PIN16</name><lsb>16</lsb><msb>16</msb></field>
            <field><name>PIN17</name><lsb>17</lsb><msb>17</msb></field>
            <field><name>PIN18</name><lsb>18</lsb><msb>18</msb></field>
            <field><name>PIN19</name><lsb>19</lsb><msb>19</msb></field>
            <field><name>PIN20</name><lsb>20</lsb><msb>20</msb></field>
            <field><name>PIN21</name><lsb>21</lsb><msb>21</msb></field>
            <field><name>PIN22</name><lsb>22</lsb><msb>22</msb></field>
            <field><name>PIN23</name><lsb>23</lsb><msb>23</msb></field>
            <field><name>PIN24</name><lsb>24</lsb><msb>24</msb></field>
            <field><name>PIN25</name><lsb>25</lsb><msb>25</msb></field>
            <field><name>PIN26</name><lsb>26</lsb><msb>26</msb></field>
            <field><name>PIN27</name><lsb>27</lsb><msb>27</msb></field>
            <field><name>PIN28</name><lsb>28</lsb><msb>28</msb></field>
            <field><name>PIN29</name><lsb>29</lsb><msb>29</msb></field>
            <field><name>PIN30</name><lsb>30</lsb><msb>30</msb></field>
            <field><name>PIN31</name><lsb>31</lsb><msb>31</msb></field>
          </fields>
        </register>
        <register derivedFrom="VALUE">
          <name>INPUT_EN</name>
          <description>Pin input enable.</description>
          <addressOffset>0x004</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>OUTPUT_EN</name>
          <description>Pin output enable.</description>
          <addressOffset>0x008</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>PORT</name>
          <description>Output port value.</description>
          <addressOffset>0x00C</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>PULLUP</name>
          <description>Internal Pull-Up enable.</description>
          <addressOffset>0x010</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>DRIVE</name>
          <description>Drive Strength.</description>
          <addressOffset>0x014</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>RISE_INT_EN</name>
          <description>Rise interrupt enable.</description>
          <addressOffset>0x018</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>RISE_INT_PEMD</name>
          <description>Rise interrupt pending.</description>
          <addressOffset>0x01C</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>FALL_INT_EN</name>
          <description>Fall interrupt enable.</description>
          <addressOffset>0x020</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>FALL_INT_PEND</name>
          <description>Fall interrupt pending.</description>
          <addressOffset>0x024</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>HIGH_INT_EN</name>
          <description>High interrupt enable.</description>
          <addressOffset>0x028</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>HIGH_INT_PEND</name>
          <description>High interrupt pending.</description>
          <addressOffset>0x02C</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>LOW_INT_EN</name>
          <description>Low interrupt enable.</description>
          <addressOffset>0x030</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>LOW_INT_PEND</name>
          <description>Low interrupt pending.</description>
          <addressOffset>0x034</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>IO_FUNC_EN</name>
          <description>HW I/O function enable.</description>
          <addressOffset>0x038</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>IO_FUNC_SEL</name>
          <description>HW I/O function select.</description>
          <addressOffset>0x03C</addressOffset>
        </register>
        <register derivedFrom="VALUE">
          <name>OUT_XOR</name>
          <description>Output XOR (invert).</description>
          <addressOffset>0x040</addressOffset>
        </register>
      </registers>
    </peripheral> <!-- GPIO -->

    <peripheral>
      <name>QSPI0</name>
      <description>Serial Peripheral Interface.</description>
      <baseAddress>0x10014000</baseAddress>
      <groupName>SPI</groupName>
      <size>32</size>
      <access>read-write</access>

      <addressBlock>
        <offset>0</offset>
        <size>0x1000</size>
        <usage>registers</usage>
      </addressBlock>

      <interrupt><name>QSPI0_IRQ</name><value>5</value></interrupt>

      <registers>

        <register>
          <name>SCKDIV</name>
          <description>Serial Clock Divisor Register.</description>
          <addressOffset>0x000</addressOffset>
          <fields>
            <field><name>SCALE</name><lsb>0</lsb><msb>11</msb></field>
          </fields>
        </register>

        <register>
          <name>SCKMODE</name>
          <description>Serial Clock Mode Register.</description>
          <addressOffset>0x004</addressOffset>
          <fields>
            <field>
              <name>PHA</name><lsb>0</lsb><msb>0</msb>
              <description>Serial Clock Phase</description>
              <enumeratedValues>
                <name>CPHA</name>
                <enumeratedValue>
                  <name>0</name>
                  <description>
                  Data is sampled on the leading edge of SCK and shifted on the trailing edge of SCK.
                  </description>
                  <value>0</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>1</name>
                  <description>
                  Data is shifted on the leading edge of SCK and sampled on the trailing edge of SCK.
                  </description>
                  <value>1</value>
                </enumeratedValue>
              </enumeratedValues>
            </field>
            <field>
              <name>POL</name><lsb>1</lsb><msb>1</msb>
              <description>Serial Clock Polarity</description>
              <enumeratedValues>
                <name>CPOL</name>
                <enumeratedValue>
                  <name>0</name>
                  <description>Inactive state of SCK is logical 0.</description>
                  <value>0</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>1</name>
                  <description>Inactive state of SCK is logical 1.</description>
                  <value>1</value>
                </enumeratedValue>
              </enumeratedValues>
            </field>
          </fields>
        </register>

        <register>
          <name>CSID</name>
          <description>Chip Select ID Register.</description>
          <addressOffset>0x010</addressOffset>
        </register>

        <register>
          <name>CSDEF</name>
          <description>Chip Select Default Register.</description>
          <addressOffset>0x014</addressOffset>
        </register>

        <register>
          <name>CSMODE</name>
          <description>Chip Select Mode Register.</description>
          <addressOffset>0x018</addressOffset>
          <fields>
            <field>
              <name>MODE</name><lsb>0</lsb><msb>1</msb>
              <enumeratedValues>
                <name>Chip_Select_Modes</name>
                <enumeratedValue>
                  <name>AUTO</name>
                  <description>Assert/de-assert CS at the beginning/end of each frame.</description>
                  <value>0</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>HOLD</name>
                  <description>Keep CS continuously asserted after the initial frame.</description>
                  <value>2</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>OFF</name>
                  <description>Disable hardware control of the CS pin.</description>
                  <value>3</value>
                </enumeratedValue>
              </enumeratedValues>
            </field>
          </fields>
        </register>

        <register>
          <name>DELAY0</name>
          <description>Delay Control Register 0.</description>
          <addressOffset>0x028</addressOffset>
          <fields>
            <field><name>CSSCK</name><lsb>0</lsb><msb>7</msb></field>
            <field><name>SCKCS</name><lsb>16</lsb><msb>23</msb></field>
          </fields>
        </register>

        <register>
          <name>DELAY1</name>
          <description>Delay Control Register 1.</description>
          <addressOffset>0x02C</addressOffset>
          <fields>
            <field><name>INTERCS</name><lsb>0</lsb><msb>7</msb></field>
            <field><name>INTERXFR</name><lsb>16</lsb><msb>23</msb></field>
          </fields>
        </register>

        <register>
          <name>FMT</name>
          <description>Frame Format Register.</description>
          <addressOffset>0x040</addressOffset>
          <fields>
            <field>
              <name>PROTO</name><lsb>0</lsb><msb>1</msb>
              <enumeratedValues>
                <name>SPI_Protocol</name>
                <enumeratedValue>
                  <name>Single</name>
                  <description>Data Pins: DQ0 (MOSI), DQ1 (MISO).</description>
                  <value>0</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>Dual</name>
                  <description>Data Pins: DQ0, DQ1.</description>
                  <value>1</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>Quad</name>
                  <description>Data Pins: DQ0, DQ1, DQ2, DQ3.</description>
                  <value>2</value>
                </enumeratedValue>
              </enumeratedValues>
            </field>
            <field>
              <name>ENDIAN</name><lsb>2</lsb><msb>2</msb>
              <enumeratedValues>
                <name>SPI_Endianness</name>
                <enumeratedValue>
                  <name>MSB_First</name>
                  <description>Tansmit most-significant bit first.</description>
                  <value>0</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>LSB_First</name>
                  <description>Transmit least-significant bit first.</description>
                  <value>1</value>
                </enumeratedValue>
              </enumeratedValues>
            </field>
            <field>
              <name>DIR</name><lsb>3</lsb><msb>3</msb>
              <enumeratedValues>
                <name>SPI_IO_Direction</name>
                <enumeratedValue>
                  <name>RX</name>
                  <description>
                  For dual and quad protocols, the DQ pins are tri-stated.
                  For the single protocol, the DQ0 pin is driven with the transmit data as normal.
                  </description>
                  <value>0</value>
                </enumeratedValue>
                <enumeratedValue>
                  <name>TX</name>
                  <description>The receive FIFO is not populated.</description>
                  <value>1</value>
                </enumeratedValue>
              </enumeratedValues>
            </field>
            <field>
              <name>LEN</name><lsb>16</lsb><msb>19</msb>
            </field>
          </fields>
        </register>

        <register>
          <name>TXDATA</name>
          <description>Transmit Data Register.</description>
          <addressOffset>0x048</addressOffset>
          <fields>
            <field><name>DATA</name><lsb>0</lsb><msb>7</msb></field>
            <field><name>FULL</name><lsb>31</lsb><msb>31</msb></field>
          </fields>
        </register>

        <register>
          <name>RXDATA</name>
          <description>Receive Data Register.</description>
          <addressOffset>0x04C</addressOffset>
          <fields>
            <field><name>DATA</name><lsb>0</lsb><msb>7</msb></field>
            <field><name>EMPTY</name><lsb>31</lsb><msb>31</msb></field>
          </fields>
        </register>

        <register>
          <name>TXMARK</name>
          <description>Transmit Watermark Register.</description>
          <addressOffset>0x050</addressOffset>
          <fields>
            <field><name>TXMARK</name><lsb>0</lsb><msb>2</msb></field>
          </fields>
        </register>

        <register>
          <name>RXMARK</name>
          <description>Receive Watermark Register.</description>
          <addressOffset>0x054</addressOffset>
          <fields>
            <field><name>RXMARK</name><lsb>0</lsb><msb>2</msb></field>
          </fields>
        </register>

        <register>
          <name>IE</name>
          <description>SPI Interrupt Enable Register.</description>
          <addressOffset>0x070</addressOffset>
          <fields>
            <field><name>TXWM</name><lsb>0</lsb><msb>0</msb></field>
            <field><name>RXWM</name><lsb>1</lsb><msb>1</msb></field>
          </fields>
        </register>

        <register>
          <name>IP</name>
          <description>SPI Interrupt Pending Register.</description>
          <addressOffset>0x074</addressOffset>
          <fields>
            <field><name>TXWM</name><lsb>0</lsb><msb>0</msb></field>
            <field><name>RXWM</name><lsb>1</lsb><msb>1</msb></field>
          </fields>
        </register>

        <register>
          <name>FCTRL</name>
          <description>SPI Flash Interface Control Register.</description>
          <addressOffset>0x060</addressOffset>
          <fields>
            <field><name>ENABLE</name><lsb>0</lsb><msb>0</msb></field>
          </fields>
        </register>

        <register>
          <name>FFMT</name>
          <description>SPI Flash Instruction Format Register.</description>
          <addressOffset>0x064</addressOffset>
          <fields>
            <field><name>CMD_EN</name><lsb>0</lsb><msb>0</msb></field>
            <field><name>ADDR_LEN</name><lsb>1</lsb><msb>3</msb></field>
            <field><name>PAD_CNT</name><lsb>4</lsb><msb>7</msb></field>
            <field><name>CMD_PROTO</name><lsb>8</lsb><msb>9</msb></field>
            <field><name>ADDR_PROTO</name><lsb>10</lsb><msb>11</msb></field>
            <field><name>DATA_PROTO</name><lsb>12</lsb><msb>13</msb></field>
            <field><name>CMD_CODE</name><lsb>16</lsb><msb>23</msb></field>
            <field><name>PAD_CODE</name><lsb>24</lsb><msb>31</msb></field>
          </fields>
        </register>

      </registers>

    </peripheral> <!-- QSPI0 -->

    <peripheral derivedFrom="QSPI0">
      <name>QSPI1</name>
      <description>Serial Peripheral Interface.</description>
      <baseAddress>0x10024000</baseAddress>
      <groupName>SPI</groupName>
      <interrupt><name>QSPI1_IRQ</name><value>6</value></interrupt>
    </peripheral> <!-- QSPI1 -->

    <peripheral derivedFrom="QSPI0">
      <name>QSPI2</name>
      <description>Serial Peripheral Interface.</description>
      <baseAddress>0x10034000</baseAddress>
      <groupName>SPI</groupName>
      <interrupt><name>QSPI2_IRQ</name><value>7</value></interrupt>
    </peripheral> <!-- QSPI2 -->

    <peripheral>
      <name>UART0</name>
      <description>Universal Asynchronous Receiver/Transmitter.</description>
      <baseAddress>0x10013000</baseAddress>
      <groupName>UART</groupName>
      <size>32</size>
      <access>read-write</access>

      <addressBlock>
        <offset>0</offset>
        <size>0x1000</size>
        <usage>registers</usage>
      </addressBlock>

      <interrupt><name>UART0_IRQ</name><value>3</value></interrupt>

      <registers>
        <register>
          <name>TXDATA</name>
          <description>Transmit Data Register.</description>
          <addressOffset>0x000</addressOffset>
          <fields>
            <field><name>DATA</name><lsb>0</lsb><msb>7</msb></field>
            <field><name>FULL</name><lsb>31</lsb><msb>31</msb></field>
          </fields>
        </register>
        <register>
          <name>RXDATA</name>
          <description>Receive Data Register.</description>
          <addressOffset>0x004</addressOffset>
          <fields>
            <field><name>DATA</name><lsb>0</lsb><msb>7</msb></field>
            <field><name>EMPTY</name><lsb>31</lsb><msb>31</msb></field>
          </fields>
        </register>
        <register>
          <name>TXCTRL</name>
          <description>Transmit Control Register.</description>
          <addressOffset>0x008</addressOffset>
          <fields>
            <field><name>ENABLE</name><lsb>0</lsb><msb>0</msb></field>
            <field><name>NSTOP</name><lsb>1</lsb><msb>1</msb></field>
            <field><name>TXCNT</name><lsb>16</lsb><msb>18</msb></field>
          </fields>
        </register>
        <register>
          <name>RXCTRL</name>
          <description>Receive Control Register.</description>
          <addressOffset>0x00C</addressOffset>
          <fields>
            <field><name>ENABLE</name><lsb>0</lsb><msb>0</msb></field>
            <field><name>RXCNT</name><lsb>16</lsb><msb>18</msb></field>
          </fields>
        </register>
        <register>
          <name>IP</name>
          <description>Interrupt Pending Register.</description>
          <addressOffset>0x0010</addressOffset>
          <fields>
            <field><name>TXWM</name><lsb>0</lsb><msb>0</msb></field>
            <field><name>RXWM</name><lsb>1</lsb><msb>1</msb></field>
          </fields>
        </register>
        <register derivedFrom="IP">
          <name>IE</name>
          <description>Interrupt Enable Register.</description>
          <addressOffset>0x0014</addressOffset>
        </register>
        <register>
          <name>DIV</name>
          <description>Baud Rate Divisor Register (BAUD = Fin / (DIV + 1)).
          </description>
          <addressOffset>0x0018</addressOffset>
          <fields>
            <field><name>DIV</name><lsb>0</lsb><msb>15</msb></field>
          </fields>
        </register>
      </registers>
    </peripheral> <!-- UART0 -->
    <peripheral derivedFrom="UART0">
      <name>UART1</name>
      <description>Universal Asynchronous Receiver/Transmitter.</description>
      <baseAddress>0x10023000</baseAddress>
      <groupName>UART</groupName>
      <interrupt><name>UART1_IRQ</name><value>4</value></interrupt>
    </peripheral> <!-- UART1 -->
  </peripherals>
</device>
