# please use >= 2022.08 openocd
# some commands are changed to match latest openocd changes
adapter speed 1000

adapter driver ftdi
ftdi vid_pid 0x0403 0x6010
# for 2023.10 openocd, change oscan1_mode to nscan1_mode
ftdi oscan1_mode off

## bindto 0.0.0.0 can be used to cover all available interfaces.
## Uncomment bindto line to enable remote machine debug
# bindto 0.0.0.0

## you can also specify adapter serial to select a ftdi chip
# adapter serial "FT6S9RD6"

## Bind JTAG with specified serial number passed by JTAGSN
# https://doc.nucleisys.com/nuclei_sdk/develop/buildsystem.html#jtagsn
if { [ info exists JTAGSN ] } {
    puts "Bind JTAG with serial number $JTAGSN"
    adapter serial $JTAGSN
}

transport select jtag

ftdi layout_init 0x0008 0x001b
ftdi layout_signal nSRST -oe 0x0020 -data 0x0020
ftdi layout_signal TCK -data 0x0001
ftdi layout_signal TDI -data 0x0002
ftdi layout_signal TDO -input 0x0004
ftdi layout_signal TMS -data 0x0008
ftdi layout_signal JTAG_SEL -data 0x0100 -oe 0x0100

# openocd variable set
if { [ info exists BOOT_HARTID ] } {
    puts "boot hart id expected to be $BOOT_HARTID"
    set BOOTHART $BOOT_HARTID
} else {
    puts "Set boot hart id to default 0"
    set BOOTHART 0
}

if { [ info exists SMP ] } {
    puts "SMP CPU count expected to be $SMP"
    set CORECNT $SMP
} else {
    puts "Set default SMP CPU count to default 1"
    set CORECNT 1
}

# TODO: variables should be replaced by nuclei_gen
set workmem_base 0x80000000
set workmem_size    0x10000
set flashxip_base   0x20000000
set xipnuspi_base  0x10140000

# if NOFLASH variable exist or passed by openocd command
# will not probe flash device
set _noflash [ info exists NOFLASH ]

# Create JTAG chain
set _CHIPNAME riscv
if { [ info exists SPLITMODE ] } {
    # SPLIT MODE mainly for Nuclei NA product line
    # It can split the lock-step CPU into two standalone CPU core
    if { $SPLITMODE > 1 } { # when SPLITMODE > 1, it means you wan to expose two harts for gdb connections
        jtag newtap $_CHIPNAME.1 cpu -irlen 5
        jtag newtap $_CHIPNAME.0 cpu -irlen 5
        set _TARGETNAME $_CHIPNAME.1.cpu
    } else {
        # hart1 hart0 in split mode
        # only one hart will be exposed to be connnected via gdb
        # the hart is selected by BOOTHART
        if { $BOOTHART == 0 } {
            jtag newtap $_CHIPNAME.split cpu -irlen 5
            jtag newtap $_CHIPNAME cpu -irlen 5
        } else {
            jtag newtap $_CHIPNAME cpu -irlen 5
            jtag newtap $_CHIPNAME.split cpu -irlen 5
        }
        set _TARGETNAME $_CHIPNAME.cpu
    }
} else {
    jtag newtap $_CHIPNAME cpu -irlen 5
    set _TARGETNAME $_CHIPNAME.cpu
}

set _smp_command ""

if { $CORECNT > 1 } {
    set _BOOTHARTNAME $_TARGETNAME.$BOOTHART
    for { set _core 0 } { $_core < $CORECNT } { incr _core } {
        set _command "target create $_TARGETNAME.$_core riscv -chain-position $_TARGETNAME"
        if { $_core == 0 } {
            set _command "$_command -rtos hwthread"
            set _smp_command "target smp $_TARGETNAME.$_core"
        } else {
            set _command "$_command -coreid $_core"
            set _smp_command "$_smp_command $_TARGETNAME.$_core"
        }
        # do target create for each cpu
        eval $_command
    }
    # do target smp for all cpus
    eval $_smp_command
} else {
    set _BOOTHARTNAME $_TARGETNAME
    if { [ info exists SPLITMODE ] } {
        if { $SPLITMODE > 1 } {
            target create $_CHIPNAME.0.cpu riscv -chain-position $_CHIPNAME.0.cpu
            target create $_CHIPNAME.1.cpu riscv -chain-position $_CHIPNAME.1.cpu
        } else {
            # split mode coreid is always 0
            target create $_TARGETNAME riscv -chain-position $_TARGETNAME
        }
    } else {
        # create a single hart target with coreid = $BOOTHART
        target create $_TARGETNAME riscv -chain-position $_TARGETNAME -coreid $BOOTHART
    }
}

$_BOOTHARTNAME configure -work-area-phys $workmem_base -work-area-size $workmem_size -work-area-backup 1

if { $_noflash == 0 } {
    set _FLASHNAME $_CHIPNAME.flash
    flash bank $_FLASHNAME nuspi $flashxip_base 0 0 0 $_BOOTHARTNAME $xipnuspi_base
}

# Expose Nuclei self-defined CSRS
# See https://github.com/riscv/riscv-gnu-toolchain/issues/319#issuecomment-*********
# Then user can view the csr register value in gdb using: info reg csr775 for CSR MTVT(0x307)
# No need to expose it now for openocd >= 2022.01
# riscv expose_csrs 416-496,770-800,835-850,1227-1231,1483-1486,1984-2040,2064-2070,2370-2380,2490-2500,4032-4040

init

if { [ info exists pulse_srst ] } {
  ftdi set_signal nSRST 0
  ftdi set_signal nSRST z
}

halt

# We must turn on this because otherwise the IDE version debug cannot download the program into flash
if { $_noflash == 0 } {
    flash protect 0 0 last off
}

arm semihosting enable
