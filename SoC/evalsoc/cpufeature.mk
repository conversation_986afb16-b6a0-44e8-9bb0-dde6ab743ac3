export CPU_CONFIG_MK=1
export CPU_ISA=rv32imafdu_zicsr_zicntr_zifencei_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbs_zicbom_zicboz_zicbop_zicond_smepmp_zve32d_zvl128b_xxlnice
export CFG_CPU_NAME=n900vk
export CFG_CPU_VERSION=v4.2.0
export CFG_CPU_REV=0x40200
# for nuclei-sdk used start
export CORE=n900fd
export SDK_CORE=n900fd
export SDK_DHRYSTONE_CORE=n900fd
export SDK_COREMARK_CORE=n900f
export SDK_WHETSTONE_CORE=n900f
export ARCH_EXT=_zve32f_zicond_zba_zbb_zbs_zicbom_zicboz_zicbop_zca_zcb_zcf_zcmp_zcmt
export SDK_DHRYSTONE_ARCH_EXT=_zba_zbb_zbs
export SDK_COREMARK_ARCH_EXT=_zba_zbb_zbs_zicond
export SDK_WHETSTONE_ARCH_EXT=_zba_zbb_zbs
export SOC=evalsoc
export QEMU_SOCCFG=evalsoc.json
export SMP=
# for nuclei-sdk used end
export CFG_E16=0
export CFG_XLEN=32
export CFG_FLEN=64
# cpu feature 
export CFG_HAS_EXCP=1
export CFG_HAS_PMA=1
export CFG_HAS_PMA_MACRO=1
export CFG_PMA_CSR_NUM=8
export CFG_PMA_SEC_CSR_NUM=0
export CFG_CORE_PFX=n900vk_
export CFG_CPU_GEN_RTL_MODE=part
export CFG_CPU=N900VKVK
export CFG_CPU_IS_900=1
export CFG_CPU_IS_N900VK=1
export CFG_PA_SIZE=32
export CFG_IREGION_BASE_ADDR=0x18000000
export CFG_BOOTH_MUL_2CYC=1
export CFG_HAS_ISA_ZBA_ZBB_ZBS=1
export CFG_HAS_CODE_SIZE_DEC_EXT=1
export CFG_HAS_PMP=1
export CFG_PMP_ENTRY_NUM=8
export CFG_HAS_DEBUG=1
export CFG_DEBUG_TRIGM_NUM=4
export CFG_NO_DEBUG_SBA=1
export CFG_HAS_ILM=1
export CFG_ILM_BASE_ADDR=0x70000000
export CFG_ILM_ADDR_WIDTH=19
export CFG_HAS_DLM=1
export CFG_DLM_BASE_ADDR=0x70100000
export CFG_DLM_ADDR_WIDTH=19
export CFG_DLM_CTRL_NUM=1
export CFG_ILM_ITF_TYPE_SRAM=1
export CFG_DLM_ITF_TYPE_SRAM=1
export CFG_SLV_AXI_IDW=8
export CFG_HAS_ICACHE=1
export CFG_ICACHE_ADDR_WIDTH=15
export CFG_HAS_DCACHE=1
export CFG_DCACHE_ADDR_WIDTH=15
export CFG_DEVICE_REGION_NUM=1
export CFG_DEVICE_REGION0_BASE=0x10000000
export CFG_DEVICE_REGION0_MASK=0x00ffffff
export CFG_CACHEABLE_REGION_NUM=0
export CFG_NC_REGION_NUM=1
export CFG_NC_REGION0_BASE=0x30000000
export CFG_NC_REGION0_MASK=0x0000ffff
export CFG_IRQ_NUM=64
export CFG_NO_PLIC=1
export CFG_HAS_CLIC=1
export CFG_CLICINTCTLBITS=3
export CFG_HAS_NICE=1
export CFG_HAS_NICE_DECODE_EXT_OUTPUT_W=0
export CFG_FPU_DOUBLE=1
export CFG_FPU_SINGLE=1
export CFG_5CYC_FPU=1
export CFG_HAS_VPU=1
export CFG_VLEN=128
export CFG_VPU_PARAL=1
export CFG_HAS_VNICE=1
export CFG_VPU_DCACHE_DW=64
export CFG_BHT_ENTRY_WIDTH=12
export CFG_HAS_AGU_QUICK_FWD=1
export CFG_RAM_DFT_IN_WID=0
export CFG_RAM_DFT_OUT_WID=0
export CFG_HAS_CSR_TIMING_OPT=1
export CFG_HAS_ALL_DFF_RESET=1
export CFG_MMU_TLB_ENTRIES=1024
export CFG_HAS_FPU=1
export CFG_HAS_RST_OPT=1
export CFG_HAS_SHAD_WBCK=1
export CFG_CC_SIZE=0
export CFG_SMP_ASYNC=0
export CFG_SMP_RATIO=0
export CFG_HAS_AGU_ADDR_CROSS_4K=1
export CFG_HAS_SDB_CMT_PRDT=1
export CFG_HAS_STB_TIMEOUT=1
export CFG_HAS_FENCE_DMB=1
export CFG_CC_ACE_NUM=0
export CFG_HAS_CLIC_INSIDE_CORE=1
export CFG_HAS_DEBUG_PRIVATE=1
export CFG_LINE_BYTES=64
export CFG_HAS_BASE_ISA_EXTENSION=1
export CFG_HAS_ZC_EXT=1
export CFG_ADDR_SIZE=32
export CFG_PPI_ADDR_WIDTH=20
export CFG_PPI_BASE_ADDR=0x10000000
export CFG_SYS_MEM_BASE=0xa0000000
export CFG_HAS_AMO=1
export CFG_MISALIGNED_ACCESS=1
export CFG_HAS_ZAWRS=1
export CFG_HAS_FSIM=1
export CFG_HAS_RV32D=1
export CFG_HAS_UMODE=1
export CFG_HAS_BMU=1
export CFG_HAS_ZCB=1
export CFG_HAS_RF_CRC=0
export CFG_HAS_ASILB=0
export CFG_HAS_VADD=1
export CFG_HAS_VMIC=1
export CFG_HAS_VRED=1
export CFG_HAS_VMAC=1
export CFG_HAS_VDIV=1
export CFG_HAS_VFPU=1
export CFG_HAS_VPU_SHAD_FWD=1
export CFG_4CYC_VFPU=1
export CFG_HAS_RS_HI=1
export CFG_HAS_RD_HI=1
export CFG_HAS_RS3=1
export CFG_HAS_RS3_PSTG=1
export CFG_HAS_RS3_PSTG_FPUONLY=0
export CFG_HAS_SMEPMP=1
export CFG_HAS_PMONITOR=1
export CFG_HAS_COFPMF=1
export CFG_HAS_PMON_EXT=1
export CFG_HPM_VER=2
export CFG_PMON_NUM=0
export CFG_HAS_HPM=1
export CFG_XLEN_IS_32=1
export CFG_REGNUM_IS_32=1
export CFG_CLIC_VERSION=1
export CFG_HAS_ECLIC=1
export CFG_HAS_CLIC_EDGE=1
export CFG_HAS_2_WIRE_DBG=1
export CFG_HAS_DEBUG13=1
export CFG_HAS_DEBUG_NEW=1
export CFG_HAS_TRIGM=1
export CFG_REGFILE_2WP=1
export CFG_SYSMEM_DATA_WIDTH_IS_64=1
export CFG_HAS_MEM_ITF=1
export CFG_HAS_LM=1
export CFG_HAS_ILM_SRAM=1
export CFG_LSU_ACCESS_ILM=1
export CFG_HAS_DLM_SRAM=1
export CFG_HAS_L0BTB=1
export CFG_HAS_BPU=1
export CFG_HAS_DYNAMIC_BPU=1
export CFG_HAS_DEVICE=1
export CFG_MEM_REGION_NUM=0
export CFG_HAS_NC=1
export CFG_HAS_CMO=1
export CFG_HAS_CACHE=1
export CFG_HAS_IOCC=1
export CFG_HAS_NC_BIU=1
export CFG_HAS_ICACHE_IOCC=1
export CFG_ICACHE_CANCEL=1
export CFG_ICACHE_DLRU=1
export CFG_HAS_DM_MAP=1
export CFG_HAS_MULDIV=1
export CFG_HAS_ZICOND_EXTENSION=1
export CFG_HAS_RV32NICE=1
export CFG_ITAG_DEPTH=15
export CFG_HAS_LBIU=1
export CFG_HAS_FLOAT_WORD_INS=1
export CFG_HAS_NCDEV_REG=1
export CFG_HAS_SUBSYS_LM=1
export CFG_HAS_SUBSYS_CORE_SRAM=1
export CFG_TMR_PRIVATE=1
export CFG_TMR_BASE_ADDR=0x18030000
export CFG_PMP_GRAIN=10
export CFG_ILM_DATA_WIDTH=64
export CFG_L1D_PREFETCH_VERSION=4
export CFG_LSU_DW=128
# evalsoc feature 
export EVALSOC_PERIPS_BASE=0x10000000
export EVALSOC_FLASH_XIP_BASE=0x20000000
export EVALSOC_FLASH_XIP_ADDR_WIDTH=28
export EVALSOC_SYSMEM_BASE=0x80000000
export EVALSOC_SYSMEM_ADDR_WIDTH=31
export EVALSOC_CFG_IRAM_BASE_ADDR=0x60000000
export EVALSOC_CFG_IRAM_ADDR_WIDTH=16
export EVALSOC_CFG_DRAM_BASE_ADDR=0x68000000
export EVALSOC_CFG_DRAM_ADDR_WIDTH=16
export EVALSOC_CFG_PERIPS_BASE=0x10000000
export EVALSOC_CFG_FLASH_XIP_BASE=0x20000000
export EVALSOC_CFG_FLASH_XIP_ADDR_WIDTH=28
export EVALSOC_CFG_SYSMEM_BASE=0x80000000
export EVALSOC_CFG_SYSMEM_ADDR_WIDTH=31
export EVALSOC_HAS_DDR_ICB_CMD_DELAY=1
export EVALSOC_HAS_CPU_RAM_WRAPPER=1
export EVALSOC_VERSION_IS_2=1
export EVALSOC_HAS_BIU2ILM=1
export EVALSOC_HAS_BIU2DLM=1
export EVALSOC_HAS_SUBSYS_MISC=1
export EVALSOC_HAS_SDIO=1
export EVALSOC_IOCP_BASE=0x50000000
export EVALSOC_IOCP_ADDR_WIDTH=28
export EVALSOC_HAS_ETHERNET=1
export EVALSOC_HAS_UART_MODEL=1
export EVALSOC_HAS_IRAM_DRAM=1
export EVALSOC_IRAM_BASE_ADDR=0x60000000
export EVALSOC_IRAM_DATA_WIDTH=64
export EVALSOC_IRAM_WMSK_WIDTH=8
export EVALSOC_IRAM_AW_LSB=3
export EVALSOC_IRAM_ADDR_WIDTH=16
export EVALSOC_DRAM_BASE_ADDR=0x68000000
export EVALSOC_DRAM_DATA_WIDTH=64
export EVALSOC_DRAM_WMSK_WIDTH=8
export EVALSOC_DRAM_AW_LSB=3
export EVALSOC_DRAM_ADDR_WIDTH=16
export EVALSOC_IRAM_RAM_DP=8192
export EVALSOC_IRAM_RAM_AW=13
export EVALSOC_DRAM_RAM_DP=8192
export EVALSOC_DRAM_RAM_AW=13
