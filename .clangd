CompileFlags:
  Add:
    # 基本的C标准和特性
    - "-std=c11"
    - "-fno-ms-extensions"
    - "-fno-ms-compatibility"
    
    # 启用GNU扩展以支持内联汇编
    - "-fgnu89-inline"
    - "-Wno-gnu"
    - "-Wno-error"
    
    # RISC-V架构相关定义
    - "-D__riscv"
    - "-D__riscv_xlen=32"
    - "-D__riscv32"
    - "-D__LITTLE_ENDIAN__"
    
    # Nuclei SDK相关定义
    - "-DNUCLEI_SDK"
    - "-DCPU_SERIES=900"
    - "-DBOOT_HARTID=0"
    
    # 包含路径
    - "-I./NMSIS/Core/Include"
    - "-I./SoC/evalsoc/Board/nuclei_fpga_eval/Include"
    - "-I./SoC/evalsoc/Common/Include"
    - "-I./application/operators_library/intrinsic"
    
    # 为了兼容性，添加一些通用定义
    - "-D__STATIC_FORCEINLINE=static inline"
    - "-D__ASM_VOLATILE(x)="
    - "-DDEBUG_ASSERT"
  Remove:
    # 移除会导致clangd问题的标志
    - "-fno-shrink-wrap-separate"
    - "-mstringop-strategy=scalar"
    - "-fsanitize=*"
    - "-march=rv32imafd_zve32f_zicond_zba_zbb_zbs_zicbom_zicboz_zicbop_zca_zcb_zcf_zcmp_zcmt"
    - "-mabi=ilp32d"
    - "-mcmodel=medlow"
    - "-mtune=nuclei-900-series"

Diagnostics:
  # 禁用一些对交叉编译代码不友好的诊断
  Suppress:
    - "unused-function"
    - "unused-variable" 
    - "unknown-pragmas"
    - "inline-asm"
  
  # 关闭一些可能对内联汇编产生误报的检查
  ClangTidy:
    Remove:
      - "readability-*"
      - "google-readability-*"
      - "misc-unused-parameters"

Index:
  Background: Build
  StandardLibrary: No 