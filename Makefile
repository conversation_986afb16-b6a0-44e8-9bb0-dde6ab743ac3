TARGET = operator_work

NUCLEI_SDK_ROOT = ./

APP_DIR = ./application
OPERATOR_DIR = $(APP_DIR)/operators_library


SRCDIRS = 	$(APP_DIR)/src 					\
			$(APP_DIR)/tests				\
			$(OPERATOR_DIR)/primitive/src 	\
			$(OPERATOR_DIR)/high_level/src	

INCDIRS = 	$(APP_DIR)/inc 						\
			$(OPERATOR_DIR)/intrinsic 			\
			$(OPERATOR_DIR)/primitive/include	\
			$(OPERATOR_DIR)/high_level/include 	

BUILD_DIR := build_dir

COMMON_FLAGS := -O0 -DDEBUG_ASSERT
LDLIBS += -lm

include $(NUCLEI_SDK_ROOT)/Build/Makefile.base

update:
	cd $(NUCLEI_SDK_ROOT)/scripts/weight_process && python3 minicpmv2_gen.py --gen-struct


lst: $(TARGET).elf 
	$(OBJDUMP) -D $^ > $(TARGET).lst

