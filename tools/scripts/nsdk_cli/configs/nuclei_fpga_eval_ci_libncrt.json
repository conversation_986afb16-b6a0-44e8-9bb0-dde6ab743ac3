{"run_config": {"target": "qemu", "xlspike": {"timeout": 120}, "qemu": {"timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "STDCLIB": "libncrt_small"}, "build_configs": {"n201-ilm": {"DOWNLOAD": "ilm", "CORE": "n201", "ARCH_EXT": ""}, "n201e-ilm": {"DOWNLOAD": "ilm", "CORE": "n201e", "ARCH_EXT": ""}, "n203e-ilm": {"DOWNLOAD": "ilm", "CORE": "n203e", "ARCH_EXT": ""}, "n300-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": ""}, "n300b-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300bp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n300fd-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": ""}, "n300fdb-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300fdbp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}, "appconfig": {"application/baremetal/demo_dsp": {"build_configs": {}, "checks": {"PASS": ["all test are passed"], "FAIL": ["test error apprears", "MEPC"]}}, "application/rtthread/msh": {"build_config": {}, "checks": {"PASS": ["msh >", "Hello RT-Thread!"]}}, "application/baremetal/benchmark/dhrystone": {"checks": {"PASS": ["1000000/(User_Cycle/Number_Of_Runs)", "Measured time too small"]}}, "application/baremetal/benchmark/coremark": {"build_configs": {"n203-optimized": {"CORE": "n203", "CPU_SERIES": "200"}, "n300-optimized": {"CORE": "n300", "CPU_SERIES": "300"}, "n600f-optimized": {"CORE": "n600f", "CPU_SERIES": "600"}, "n900fd-optimized": {"CORE": "n900fd", "CPU_SERIES": "900"}}}}, "expecteds": {"application/baremetal/demo_dsp": {"n201-ilm": {"build": false, "run": false}, "n201e-ilm": {"build": false, "run": false}, "n203e-ilm": {"build": false, "run": false}, "n300b-ilm": {"build": false, "run": false}, "n300bp-ilm": {"build": false, "run": false}, "n300fdb-ilm": {"build": false, "run": false}, "n300fdbp-ilm": {"build": false, "run": false}}, "application/baremetal/benchmark/whetstone": {"n201-ilm": {"build": true, "run": false}, "n201e-ilm": {"build": true, "run": false}}, "application": {"n201e-ilm": {"build": true, "run": false}}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": true}, "application/rtthread/msh": {"build": true, "run": false}, "application/baremetal/demo_spmp": {"build": true, "run": true}, "application/baremetal/demo_smpu": {"build": true, "run": true}, "application/baremetal/demo_smode_eclic": {"build": true, "run": true}, "application/baremetal/demo_cidu": {"build": true, "run": true}, "application/baremetal/demo_cache": {"build": true, "run": false}, "application/baremetal/demo_pma": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}