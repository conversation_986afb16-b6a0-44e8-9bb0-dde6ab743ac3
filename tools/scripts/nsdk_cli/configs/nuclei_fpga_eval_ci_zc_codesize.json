{"run_config": {"target": "qemu", "hardware": {"baudrate": 115200, "timeout": 240}, "qemu": {"timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "APP_COMMON_FLAGS": "-fomit-frame-pointer -fno-shrink-wrap-separate", "BENCH_FLAGS": "-<PERSON><PERSON>"}, "build_configs": {"n200-ilm": {"DOWNLOAD": "ilm", "CORE": "n200", "ARCH_EXT": ""}, "n201-ilm": {"DOWNLOAD": "ilm", "CORE": "n201", "ARCH_EXT": ""}, "n201e-ilm": {"DOWNLOAD": "ilm", "CORE": "n201e", "ARCH_EXT": ""}, "n203e-ilm": {"DOWNLOAD": "ilm", "CORE": "n203e", "ARCH_EXT": ""}, "n300-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": ""}, "n300f-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": ""}, "n300fd-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": ""}, "nx900-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900", "ARCH_EXT": ""}, "nx900f-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900f", "ARCH_EXT": ""}, "nx900fd-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900f", "ARCH_EXT": ""}, "n200-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "n200", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n201-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "n201", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n201e-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "n201e", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n203e-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "n203e", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n300-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n300f-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt"}, "n300fd-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt"}, "nx900-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "nx900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "nx900f-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "nx900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "nx900fd-ilm-zc": {"DOWNLOAD": "ilm", "CORE": "nx900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}}, "appconfig": {}, "expected": {"application/baremetal/demo_nice": {"run": true, "build": true}, "application/baremetal/demo_dsp": {"run": false, "build": false}}}