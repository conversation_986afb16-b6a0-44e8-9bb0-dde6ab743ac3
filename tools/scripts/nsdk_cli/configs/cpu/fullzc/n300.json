{"build_config": {"CPU_SERIES": "300"}, "build_configs": {"rv32imac": {"CORE": "n300", "ARCH_EXT": ""}, "rv32imafc": {"CORE": "n300f", "ARCH_EXT": ""}, "rv32imafdc": {"CORE": "n300fd", "ARCH_EXT": ""}, "rv32imafdc_zicond": {"CORE": "n300fd", "ARCH_EXT": "_zicond", "NMSIS_LIB_ARCH": "rv32imafdc"}, "rv32imacbp": {"CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn3x"}, "rv32imafcbp": {"CORE": "n300f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn3x"}, "rv32imafdcbp": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn3x"}, "rv32imafdcbp_zicond": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldspn1x", "NMSIS_LIB_ARCH": "rv32imafdc_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imabp_xxlcz": {"CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x_xxlcz", "NMSIS_LIB_ARCH": "rv32imac_zba_zbb_zbc_zbs_xxldspn3x"}, "rv32imafbp_xxlcz": {"CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x_xxlcz", "NMSIS_LIB_ARCH": "rv32imafc_zba_zbb_zbc_zbs_xxldspn3x"}, "rv32imafdbp_xxlcz": {"CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x_xxlcz", "NMSIS_LIB_ARCH": "rv32imafdc_zba_zbb_zbc_zbs_xxldspn3x"}}}