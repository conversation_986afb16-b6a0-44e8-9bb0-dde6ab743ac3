{"build_config": {"CPU_SERIES": "900"}, "build_configs": {"rv32imac": {"CORE": "u900"}, "rv32imacb": {"CORE": "u900", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imacbp": {"CORE": "u900", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafc": {"CORE": "u900f", "ARCH_EXT": ""}, "rv32imafcb": {"CORE": "u900f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafcp": {"CORE": "u900f", "ARCH_EXT": "_xxldspn1x"}, "rv32imafcbp": {"CORE": "u900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdc": {"CORE": "u900fd", "ARCH_EXT": ""}, "rv32imafdcb": {"CORE": "u900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafdcp": {"CORE": "u900fd", "ARCH_EXT": "_xxldspn1x"}, "rv32imafdcbp": {"CORE": "u900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcbp_zicond": {"CORE": "u900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldspn1x", "NMSIS_LIB_ARCH": "rv32imafdc_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafcv": {"CORE": "u900f", "ARCH_EXT": "_zve32f"}, "rv32imafcbv": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafcpv": {"CORE": "u900f", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafcbpv": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f"}, "rv32imafdcbv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafdcpv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafdcbpv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcbpv_zicond": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zicond_xxldspn1x", "NMSIS_LIB_ARCH": "rv32imafdc_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}}}