{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 120}, "qemu": {"qemu32": "qemu-system-riscv32", "qemu64": "qemu-system-riscv64", "timeout": 120}, "ncycm": {"timeout": 1200}}, "copy_objects": true, "global_variables": {"sdk_copy_objects": "elf,map"}, "build_target": "clean all", "build_config": {}, "parallel": "-j", "checks": {"PASS": ["passed", "Passed"], "FAIL": ["MEPC", "failed", "Failed", "FAILURE", "ERROR", "test error apprears"]}, "appdirs": ["."], "appdirs_ignore": []}