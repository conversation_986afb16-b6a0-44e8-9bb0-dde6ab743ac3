{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}, "xlspike": {"timeout": 240}}, "parallel": "-j", "copy_objects": true, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "ARCH_EXT": "", "DOWNLOAD": "ilm"}, "appdirs": ["application/baremetal/benchmark"], "build_configs": {}, "appconfig": {"application/baremetal/benchmark/coremark": {"build_configs": {"n300-newlib-small_default": {"CORE": "n300", "STDCLIB": "newlib_small"}, "n300-libncrt-small_default": {"CORE": "n300", "STDCLIB": "libncrt_small"}, "n300-libncrt-fast_default": {"CORE": "n300", "STDCLIB": "libncrt_fast"}, "n300-libncrt-balanced_default": {"CORE": "n300", "STDCLIB": "libncrt_balanced"}, "n300-newlib-small_case1": {"CORE": "n300", "STDCLIB": "newlib_small", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-small_case1": {"CORE": "n300", "STDCLIB": "libncrt_small", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-fast_case1": {"CORE": "n300", "STDCLIB": "libncrt_fast", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-balanced_case1": {"CORE": "n300", "STDCLIB": "libncrt_balanced", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}}}, "application/baremetal/benchmark/dhrystone": {"build_configs": {"n300-newlib-small_default": {"CORE": "n300", "STDCLIB": "newlib_small"}, "n300-libncrt-small_default": {"CORE": "n300", "STDCLIB": "libncrt_small"}, "n300-libncrt-fast_default": {"CORE": "n300", "STDCLIB": "libncrt_fast"}, "n300-libncrt-balanced_default": {"CORE": "n300", "STDCLIB": "libncrt_balanced"}, "n300-newlib-small_case1": {"CORE": "n300", "STDCLIB": "newlib_small", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-small_case1": {"CORE": "n300", "STDCLIB": "libncrt_small", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-fast_case1": {"CORE": "n300", "STDCLIB": "libncrt_fast", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-balanced_case1": {"CORE": "n300", "STDCLIB": "libncrt_balanced", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}}}, "application/baremetal/benchmark/whetstone": {"build_configs": {"n300-newlib-small_default": {"CORE": "n300", "STDCLIB": "newlib_small"}, "n300-libncrt-small_default": {"CORE": "n300", "STDCLIB": "libncrt_small"}, "n300-libncrt-fast_default": {"CORE": "n300", "STDCLIB": "libncrt_fast"}, "n300-libncrt-balanced_default": {"CORE": "n300", "STDCLIB": "libncrt_balanced"}, "n300-newlib-small_case1": {"CORE": "n300", "STDCLIB": "newlib_small", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-small_case1": {"CORE": "n300", "STDCLIB": "libncrt_small", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-fast_case1": {"CORE": "n300", "STDCLIB": "libncrt_fast", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}, "n300-libncrt-balanced_case1": {"CORE": "n300", "STDCLIB": "libncrt_balanced", "BENCH_FLAGS": "-O2 -funroll-all-loops -finline-limit=600 "}}}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": true}, "application/rtthread/msh": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}