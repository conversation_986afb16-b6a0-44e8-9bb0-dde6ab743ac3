{"run_config": {"target": "xlspike", "xlspike": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "ARCH_EXT": "", "SIMU": "xlspike"}, "build_configs": {"n201-ilm": {"DOWNLOAD": "ilm", "CORE": "n201"}, "n201e-ilm": {"DOWNLOAD": "ilm", "CORE": "n201e"}, "n203-ilm": {"DOWNLOAD": "ilm", "CORE": "n203"}, "n300f-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f"}, "n300fd-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd"}, "ux600-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600"}, "ux600f-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600f"}, "ux600f-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600f"}, "ux600fd-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600fd"}}, "appconfig": {"application/baremetal/demo_dsp": {"build_configs": {"n300-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_xxldspn1x"}, "n300f-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_xxldspn1x"}, "n300fd-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_xxldspn1x"}, "ux600-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "ux600", "ARCH_EXT": "_xxldsp"}, "ux600f-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "ux600f", "ARCH_EXT": "_xxldsp"}, "ux600fd-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "ux600fd", "ARCH_EXT": "_xxldsp"}}, "checks": {"PASS": ["all test are passed"], "FAIL": ["test error apprears", "MEPC"]}}}, "expecteds": {"application/baremetal/demo_dsp": {"n201-ilm": {"build": false, "run": false}, "n201-flash": {"build": false, "run": false}, "n201-flashxip": {"build": false, "run": false}, "n203e-ilm": {"build": false, "run": false}, "n203e-flash": {"build": false, "run": false}, "n203e-flashxip": {"build": false, "run": false}, "n201e-ilm": {"build": false, "run": false}, "n201e-flash": {"build": false, "run": false}, "n201e-flashxip": {"build": false, "run": false}}, "application/baremetal/benchmark/whetstone": {"n201-ilm": {"build": true, "run": false}, "n201e-ilm": {"build": true, "run": false}}, "application": {"n201e-ilm": {"build": true, "run": false}}, "test": {"n201e-ilm": {"build": true, "run": false}}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "application/rtthread/msh": {"build": true, "run": false}, "test/core": {"build": true, "run": false}}}