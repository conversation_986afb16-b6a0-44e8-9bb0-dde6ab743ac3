{"run_config": {"target": "qemu", "hardware": {"baudrate": 115200, "timeout": 60}, "xlspike": {"timeout": 120}, "qemu": {"timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "STDCLIB": "newlib_full", "ARCH_EXT": ""}, "build_configs": {"n201-ilm": {"DOWNLOAD": "ilm", "CORE": "n201"}, "n201-flash": {"DOWNLOAD": "flash", "CORE": "n201"}, "n201-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201"}, "n201e-ilm": {"DOWNLOAD": "ilm", "CORE": "n201e"}, "n201e-flash": {"DOWNLOAD": "flash", "CORE": "n201e"}, "n201e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201e"}, "n203-ilm": {"DOWNLOAD": "ilm", "CORE": "n203"}, "n203-flash": {"DOWNLOAD": "flash", "CORE": "n203"}, "n203-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n203"}, "n203e-ilm": {"DOWNLOAD": "ilm", "CORE": "n203e"}, "n203e-flash": {"DOWNLOAD": "flash", "CORE": "n203e"}, "n203e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n203e"}, "n300f-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f"}, "n300f-flash": {"DOWNLOAD": "flash", "CORE": "n300f"}, "n300f-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300f"}, "n300fd-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd"}, "n300fd-flash": {"DOWNLOAD": "flash", "CORE": "n300fd"}, "n300fd-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300fd"}, "ux600-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600"}, "ux600-flash": {"DOWNLOAD": "flash", "CORE": "ux600"}, "ux600-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600"}, "ux600-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600"}, "ux600f-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600f"}, "ux600f-flash": {"DOWNLOAD": "flash", "CORE": "ux600f"}, "ux600f-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600f"}, "ux600f-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600f"}, "ux600fd-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600fd"}, "ux600fd-flash": {"DOWNLOAD": "flash", "CORE": "ux600fd"}, "ux600fd-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600fd"}, "ux600fd-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600fd"}}, "appconfig": {"application/baremetal/benchmark/dhrystone": {"build_configs": {"n203-best": {"CORE": "n203", "DHRY_MODE": "best"}, "n300-best": {"CORE": "n300", "DHRY_MODE": "best"}, "n600f-best": {"CORE": "n600f", "DHRY_MODE": "best"}, "n900fd-best": {"CORE": "n900fd", "DHRY_MODE": "best"}, "nx600f-best": {"CORE": "nx600f", "DHRY_MODE": "best"}, "nx900fd-best": {"CORE": "nx900fd", "DHRY_MODE": "best"}}, "checks": {"PASS": ["1000000/(User_Cycle/Number_Of_Runs)", "Please increase number"]}}, "application/baremetal/benchmark/coremark": {"build_configs": {"n203-optimized": {"CORE": "n203", "CPU_SERIES": "200"}, "n300-optimized": {"CORE": "n300", "CPU_SERIES": "300"}, "n600f-optimized": {"CORE": "n600f", "CPU_SERIES": "600"}, "n900fd-optimized": {"CORE": "n900fd", "CPU_SERIES": "900"}, "nx600f-optimized": {"CORE": "nx600f", "CPU_SERIES": "600"}, "nx900fd-optimized": {"CORE": "nx900fd", "CPU_SERIES": "900"}}}, "application/rtthread/msh": {"build_config": {}, "checks": {"PASS": ["msh >", "Hello RT-Thread!"]}}, "application/baremetal/demo_dsp": {"build_configs": {"n300p-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_xxldspn1x"}, "n300fp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_xxldspn1x"}, "n300fdp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_xxldspn1x"}, "ux600p-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600", "ARCH_EXT": "_xxldsp"}, "ux600fp-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600f", "ARCH_EXT": "_xxldsp"}, "ux600fv-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600f", "ARCH_EXT": "_zve64f"}, "ux600fpv-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600f", "ARCH_EXT": "_zve64f_xxldsp"}, "ux600fdp-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600fd", "ARCH_EXT": "_xxldsp"}, "ux600fdv-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600fd", "ARCH_EXT": "v"}, "ux600fdpv-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600fd", "ARCH_EXT": "v_xxldsp"}}, "checks": {"PASS": ["all test are passed"], "FAIL": ["test error apprears", "MEPC"]}}}, "expected": {"test/core": {"build": true, "run": true}, "application/baremetal/demo_spmp": {"build": true, "run": true}, "application/baremetal/demo_smpu": {"build": true, "run": true}, "application/baremetal/demo_smode_eclic": {"build": true, "run": true}, "application/baremetal/demo_nice": {"build": true, "run": true}, "application/baremetal/demo_cidu": {"build": true, "run": false}, "application/baremetal/demo_cache": {"build": true, "run": false}, "application/baremetal/demo_pma": {"build": true, "run": false}}, "expecteds": {"application/baremetal/demo_dsp": {"n201-ilm": {"build": false, "run": false}, "n201-flash": {"build": false, "run": false}, "n201-flashxip": {"build": false, "run": false}, "n203e-ilm": {"build": false, "run": false}, "n203e-flash": {"build": false, "run": false}, "n203e-flashxip": {"build": false, "run": false}, "n201e-ilm": {"build": false, "run": false}, "n201e-flash": {"build": false, "run": false}, "n201e-flashxip": {"build": false, "run": false}}}}