{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "CORE": "ux600", "DOWNLOAD": "ilm", "ARCH_EXT": ""}, "build_configs": {"ilm": {"DOWNLOAD": "ilm"}, "flash": {"DOWNLOAD": "flash"}, "flashxip": {"DOWNLOAD": "flashxip"}, "ddr": {"DOWNLOAD": "ddr"}}, "expected": {"application/baremetal/demo_nice": {"run": false, "build": true}}, "expecteds": {"application/baremetal/benchmark": {"flashxip": {"run": false}}, "application/baremetal/demo_dsp": {"flashxip": {"run": false}}}}