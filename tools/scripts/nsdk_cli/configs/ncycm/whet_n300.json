{"run_config": {"target": "ncycm", "hardware": {"baudrate": 115200, "timeout": 60}, "xlspike": {"timeout": 120}, "qemu": {"timeout": 60}, "ncycm": {"ncycm": "n300_best_config_cymodel_latest", "timeout": 10000}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "SIMU": "xlspike", "SIMULATION": "1", "ARCH_EXT": "", "DOWNLOAD": "ilm", "CORE": "n300fd", "CPU_SERIES": "300"}, "appdirs": ["application/baremetal/benchmark/whetstone"], "build_configs": {"n300-newlib_small": {"STDCLIB": "newlib_small"}, "n300-libncrt_small": {"STDCLIB": "libncrt_small"}, "n300-libncrt_balanced": {"STDCLIB": "libncrt_balanced"}, "n300-libncrt_fast": {"STDCLIB": "libncrt_fast"}}}