{"run_config": {"target": "ncycm", "hardware": {"baudrate": 115200, "timeout": 60}, "xlspike": {"timeout": 120}, "qemu": {"timeout": 60}, "ncycm": {"ncycm": "n900_best_config_cymodel_latest", "timeout": 6000}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "SIMU": "xlspike", "SIMULATION": "1", "ARCH_EXT": "", "DOWNLOAD": "ilm", "CORE": "n900", "CPU_SERIES": "900"}, "appdirs": ["application/baremetal/benchmark/coremark", "application/baremetal/benchmark/dhrystone"], "build_configs": {"n900-newlib_small": {"STDCLIB": "newlib_small"}, "n900b-newlib_small": {"STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n900p-newlib_small": {"STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "n900bp-newlib_small": {"STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n900-libncrt_small": {"STDCLIB": "libncrt_small"}, "n900-libncrt_balanced": {"STDCLIB": "libncrt_balanced"}, "n900-libncrt_fast": {"STDCLIB": "libncrt_fast"}, "n900b-libncrt_small": {"STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n900b-libncrt_balanced": {"STDCLIB": "libncrt_balanced", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n900b-libncrt_fast": {"STDCLIB": "libncrt_fast", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n900p-libncrt_small": {"STDCLIB": "libncrt_small", "ARCH_EXT": "_xxldspn1x"}, "n900p-libncrt_balanced": {"STDCLIB": "libncrt_balanced", "ARCH_EXT": "_xxldspn1x"}, "n900p-libncrt_fast": {"STDCLIB": "libncrt_fast", "ARCH_EXT": "_xxldspn1x"}, "n900bp-libncrt_small": {"STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n900bp-libncrt_balanced": {"STDCLIB": "libncrt_balanced", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n900bp-libncrt_fast": {"STDCLIB": "libncrt_fast", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}}