{"run_config": {"target": "qemu", "xlspike": {"timeout": 240}, "qemu": {"timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {}, "build_configs": {"n203": {"CORE": "n203", "ARCH_EXT": ""}, "n300bp": {"CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n300fd": {"CORE": "n300fd", "ARCH_EXT": ""}, "n300fdbp": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}, "appconfig": {"application/baremetal/benchmark/dhrystone": {"build_config": {}, "checks": {"PASS": ["1000000/(User_Cycle/Number_Of_Runs)", "Please increase number"]}}}, "expecteds": {"application/baremetal/demo_dsp": {"n201e": {"build": false, "run": false}}, "application/baremetal/benchmark/whetstone": {"n201": {"build": true, "run": false}, "n201e": {"build": true, "run": false}}, "application": {"n201e": {"build": true, "run": false}}, "test": {"n201e": {"build": true, "run": false}}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": true}, "application/rtthread/msh": {"build": true, "run": false}, "application/baremetal/demo_spmp": {"build": true, "run": true}, "application/baremetal/demo_smpu": {"build": true, "run": true}, "application/baremetal/demo_smode_eclic": {"build": true, "run": true}, "application/baremetal/demo_cidu": {"build": true, "run": false}, "application/baremetal/demo_cache": {"build": true, "run": false}, "application/baremetal/demo_pma": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}