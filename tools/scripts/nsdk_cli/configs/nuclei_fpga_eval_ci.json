{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "ARCH_EXT": ""}, "build_configs": {"n201-ilm": {"DOWNLOAD": "ilm", "CORE": "n201"}, "n201-flash": {"DOWNLOAD": "flash", "CORE": "n201"}, "n201-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201"}, "n201e-ilm": {"DOWNLOAD": "ilm", "CORE": "n201e"}, "n201e-flash": {"DOWNLOAD": "flash", "CORE": "n201e"}, "n201e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201e"}, "n203-ilm": {"DOWNLOAD": "ilm", "CORE": "n203"}, "n203-flash": {"DOWNLOAD": "flash", "CORE": "n203"}, "n203-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n203"}, "n203e-ilm": {"DOWNLOAD": "ilm", "CORE": "n203e"}, "n203e-flash": {"DOWNLOAD": "flash", "CORE": "n203e"}, "n203e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n203e"}, "n300f-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f"}, "n300f-flash": {"DOWNLOAD": "flash", "CORE": "n300f"}, "n300f-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300f"}, "n300fd-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd"}, "n300fd-flash": {"DOWNLOAD": "flash", "CORE": "n300fd"}, "n300fd-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300fd"}, "ux600-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600"}, "ux600-flash": {"DOWNLOAD": "flash", "CORE": "ux600"}, "ux600-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600"}, "ux600-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600"}, "ux600f-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600f"}, "ux600f-flash": {"DOWNLOAD": "flash", "CORE": "ux600f"}, "ux600f-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600f"}, "ux600f-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600f"}, "ux600fd-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600fd"}, "ux600fd-flash": {"DOWNLOAD": "flash", "CORE": "ux600fd"}, "ux600fd-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600fd"}, "ux600fd-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600fd"}}, "appconfig": {"application/baremetal/demo_dsp": {"build_configs": {"n300-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_xxldspn1x"}, "n300f-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_xxldspn1x"}, "n300fd-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_xxldspn1x"}, "ux600-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "ux600", "ARCH_EXT": "_xxldsp"}, "ux600f-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "ux600f", "ARCH_EXT": "_xxldsp"}, "ux600fd-ilm-dsp": {"DOWNLOAD": "ilm", "CORE": "ux600fd", "ARCH_EXT": "_xxldsp"}}, "checks": {"PASS": ["all test are passed"], "FAIL": ["test error apprears", "MEPC"]}}}, "expecteds": {"application/baremetal/demo_dsp": {"n201-ilm": {"build": false, "run": false}, "n201-flash": {"build": false, "run": false}, "n201-flashxip": {"build": false, "run": false}, "n203e-ilm": {"build": false, "run": false}, "n203e-flash": {"build": false, "run": false}, "n203e-flashxip": {"build": false, "run": false}, "n201e-ilm": {"build": false, "run": false}, "n201e-flash": {"build": false, "run": false}, "n201e-flashxip": {"build": false, "run": false}}}}