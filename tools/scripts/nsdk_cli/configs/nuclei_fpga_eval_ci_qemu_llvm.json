{"run_config": {"target": "qemu", "hardware": {"baudrate": 115200, "timeout": 60}, "xlspike": {"timeout": 120}, "qemu": {"timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "ARCH_EXT": ""}, "build_configs": {"n201-ilm": {"DOWNLOAD": "ilm", "CORE": "n201"}, "n201-flash": {"DOWNLOAD": "flash", "CORE": "n201"}, "n201-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201"}, "n201e-ilm": {"DOWNLOAD": "ilm", "CORE": "n201e"}, "n201e-flash": {"DOWNLOAD": "flash", "CORE": "n201e"}, "n201e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201e"}, "n203-ilm": {"DOWNLOAD": "ilm", "CORE": "n203"}, "n203-flash": {"DOWNLOAD": "flash", "CORE": "n203"}, "n203-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n203"}, "n203e-ilm": {"DOWNLOAD": "ilm", "CORE": "n203e"}, "n203e-flash": {"DOWNLOAD": "flash", "CORE": "n203e"}, "n203e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n203e"}, "n300f-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f"}, "n300f-flash": {"DOWNLOAD": "flash", "CORE": "n300f"}, "n300f-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300f"}, "n300fd-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd"}, "n300fd-flash": {"DOWNLOAD": "flash", "CORE": "n300fd"}, "n300fd-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300fd"}, "nx900-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900"}, "nx900-flash": {"DOWNLOAD": "flash", "CORE": "nx900"}, "nx900-flashxip": {"DOWNLOAD": "flashxip", "CORE": "nx900"}, "nx900-ddr": {"DOWNLOAD": "ddr", "CORE": "nx900"}, "nx900f-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900f"}, "nx900f-flash": {"DOWNLOAD": "flash", "CORE": "nx900f"}, "nx900f-flashxip": {"DOWNLOAD": "flashxip", "CORE": "nx900f"}, "nx900f-ddr": {"DOWNLOAD": "ddr", "CORE": "nx900f"}, "nx900fd-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900fd"}, "nx900fd-flash": {"DOWNLOAD": "flash", "CORE": "nx900fd"}, "nx900fd-flashxip": {"DOWNLOAD": "flashxip", "CORE": "nx900fd"}, "nx900fd-ddr": {"DOWNLOAD": "ddr", "CORE": "nx900fd"}}, "appconfig": {"application/baremetal/benchmark/dhrystone": {"build_configs": {"n203-best": {"CORE": "n203", "DHRY_MODE": "best"}, "n300-best": {"CORE": "n300", "DHRY_MODE": "best"}, "n900f-best": {"CORE": "n900f", "DHRY_MODE": "best"}, "n900fd-best": {"CORE": "n900fd", "DHRY_MODE": "best"}, "nx900f-best": {"CORE": "nx900f", "DHRY_MODE": "best"}, "nx900fd-best": {"CORE": "nx900fd", "DHRY_MODE": "best"}}, "checks": {"PASS": ["1000000/(User_Cycle/Number_Of_Runs)", "Please increase number"]}}, "application/baremetal/benchmark/coremark": {"build_configs": {"n203-optimized": {"CORE": "n203", "CPU_SERIES": "200"}, "n300-optimized": {"CORE": "n300", "CPU_SERIES": "300"}, "n900f-optimized": {"CORE": "n900f", "CPU_SERIES": "900"}, "n900fd-optimized": {"CORE": "n900fd", "CPU_SERIES": "900"}, "nx900f-optimized": {"CORE": "nx900f", "CPU_SERIES": "900"}, "nx900fd-optimized": {"CORE": "nx900fd", "CPU_SERIES": "900"}}}, "application/rtthread/msh": {"build_config": {}, "checks": {"PASS": ["msh >", "Hello RT-Thread!"]}}, "application/baremetal/demo_dsp": {"build_configs": {"n300p-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_xxldspn1x"}, "n300fp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_xxldspn1x"}, "n300fdp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_xxldspn1x"}, "n300fdbp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n900fdbpv-ilm": {"DOWNLOAD": "ilm", "CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "nx900p-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900", "ARCH_EXT": "_xxldsp"}, "nx900fp-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900f", "ARCH_EXT": "_xxldsp"}, "nx900fv-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900f", "ARCH_EXT": "_zve64f"}, "nx900fpv-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900f", "ARCH_EXT": "_zve64f_xxldsp"}, "nx900fdp-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900fd", "ARCH_EXT": "_xxldsp"}, "nx900fdv-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900fd", "ARCH_EXT": "v"}, "nx900fdpv-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900fd", "ARCH_EXT": "v_xxldsp"}, "nx900fdbpv-ilm": {"DOWNLOAD": "ilm", "CORE": "nx900fd", "ARCH_EXT": "v_zba_zbb_zbc_zbs_xxldsp"}}, "checks": {"PASS": ["all test are passed"], "FAIL": ["test error apprears", "MEPC"]}}}, "expected": {"test/core": {"build": true, "run": true}, "application/baremetal/demo_spmp": {"build": true, "run": true}, "application/baremetal/demo_smpu": {"build": true, "run": true}, "application/baremetal/demo_smode_eclic": {"build": true, "run": true}, "application/baremetal/demo_nice": {"build": true, "run": true}, "application/baremetal/demo_cidu": {"build": true, "run": false}, "application/baremetal/demo_cache": {"build": true, "run": false}, "application/baremetal/demo_pma": {"build": true, "run": false}}, "expecteds": {"application/baremetal/demo_dsp": {"n201-ilm": {"build": false, "run": false}, "n201-flash": {"build": false, "run": false}, "n201-flashxip": {"build": false, "run": false}, "n203e-ilm": {"build": false, "run": false}, "n203e-flash": {"build": false, "run": false}, "n203e-flashxip": {"build": false, "run": false}, "n201e-ilm": {"build": false, "run": false}, "n201e-flash": {"build": false, "run": false}, "n201e-flashxip": {"build": false, "run": false}}}}