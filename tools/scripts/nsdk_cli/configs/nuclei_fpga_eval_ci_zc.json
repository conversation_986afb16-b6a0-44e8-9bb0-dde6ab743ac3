{"run_config": {"target": "qemu", "hardware": {"baudrate": 115200, "timeout": 240}, "qemu": {"timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "ARCH_EXT": ""}, "build_configs": {"n200-ilm": {"DOWNLOAD": "ilm", "CORE": "n200", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n201-flash": {"DOWNLOAD": "flash", "CORE": "n201", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n201e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201e", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zicond"}, "n203e-ilm": {"DOWNLOAD": "ilm", "CORE": "n203e", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zicond"}, "n300-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zicond"}, "n300b-flash": {"DOWNLOAD": "flash", "CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs"}, "n300bp-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x"}, "n300fbp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn1x"}, "n300f-flash": {"DOWNLOAD": "flash", "CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt"}, "n300fbpv-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn2x"}, "n300fdp_zc-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxldsp"}, "n300fdbp_zc-flash": {"DOWNLOAD": "flash", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x"}, "n300fdbpv_zc-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x"}, "n300fdp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcd_xxldsp"}, "n300fdbp-flash": {"DOWNLOAD": "flash", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcd_zba_zbb_zbc_zbs_xxldspn3x"}, "n300fdbpv-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcd_zba_zbb_zbc_zbs_xxldspn3x"}, "ux600b-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs"}, "ux600-flash": {"DOWNLOAD": "flash", "CORE": "ux600", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "ux600bp-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp"}, "ux600bp-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp"}, "ux600fbp-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp"}, "ux600fbp-flash": {"DOWNLOAD": "flash", "CORE": "ux600f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp"}, "ux600fbp-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux600f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp"}, "ux600fbp-ddr": {"DOWNLOAD": "ddr", "CORE": "ux600f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp"}, "ux600fdbpv-ilm": {"DOWNLOAD": "ilm", "CORE": "ux600fd", "ARCH_EXT": "v_zca_zcb_zcd_zba_zbb_zbc_zbs_xxldsp"}, "ux600fdbp-flash": {"DOWNLOAD": "flash", "CORE": "ux600fd", "ARCH_EXT": "_zca_zcb_zcd_zba_zbb_zbc_zbs_xxldsp"}}, "appconfig": {}, "expected": {"application/baremetal/demo_nice": {"run": true, "build": true}, "application/baremetal/demo_dsp": {"run": false, "build": false}}}