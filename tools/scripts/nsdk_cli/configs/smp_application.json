{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 60}, "qemu": {"qemu32": "qemu-system-riscv32", "qemu64": "qemu-system-riscv64", "timeout": 60}}, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval"}, "checks": {"PASS": [], "FAIL": ["MEPC"]}, "appdirs": ["application/baremetal/demo_cidu", "application/baremetal/smphello", "application/freertos/smpdemo"], "appdirs_ignore": [], "appconfig": {"application/baremetal/demo_cidu": {"build_config": {}, "checks": {"PASS": ["[ERROR]__CIDU_PRESENT must be defined as 1 in <Device>.h!", "Core 1 has received interrupt from core 0", "[WARN] SMP & CIDU not present"]}}, "application/freertos/smpdemo": {"build_config": {}, "checks": {"PASS": ["timers Callback 3 on hart"]}}, "application/baremetal/smphello": {"build_config": {}, "checks": {"PASS": ["All harts boot successfully!"]}}}}