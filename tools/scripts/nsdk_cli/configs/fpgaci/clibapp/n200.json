{"matrix": {"newlib_small": {"STDCLIB": "newlib_small"}, "libncrt_small": {"STDCLIB": "libncrt_small"}, "libncrt_balanced": {"STDCLIB": "libncrt_balanced"}, "libncrt_fast": {"STDCLIB": "libncrt_fast"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "application/baremetal/demo_spmp": {"build": true, "run": false}, "application/baremetal/demo_smpu": {"build": true, "run": false}, "application/baremetal/demo_pmp": {"build": true, "run": false}, "application/baremetal/demo_smode_eclic": {"build": true, "run": false}, "application/baremetal/demo_cidu": {"build": true, "run": false}, "application/baremetal/demo_stack_check": {"build": true, "run": false}, "application/baremetal/demo_cache": {"build": true, "run": false}, "application/baremetal/demo_pma": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}, "expecteds": {"application/baremetal/demo_dsp": {"rv32emc-newlib_small": {"build": false, "run": false}, "rv32emc-libncrt_small": {"build": false, "run": false}, "rv32emc-libncrt_balanced": {"build": false, "run": false}, "rv32emc-libncrt_fast": {"build": false, "run": false}}}}