{"matrix": {"terapines": {"TOOLCHAIN": "terapines", "STDCLIB": "newlib_small"}, "nuclei_gnu": {"TOOLCHAIN": "nuclei_gnu", "STDCLIB": "newlib_small"}, "terapines_Os_flto": {"TOOLCHAIN": "terapines", "STDCLIB": "newlib_small", "BENCH_FLAGS": "-Os -flto"}, "terapines_Ofast_flto": {"TOOLCHAIN": "terapines", "STDCLIB": "newlib_small", "BENCH_FLAGS": "-Ofast -flto"}, "nuclei_gnu_Os_flto": {"TOOLCHAIN": "nuclei_gnu", "STDCLIB": "newlib_small", "BENCH_FLAGS": "-Os -flto"}, "nuclei_gnu_Ofast_flto": {"TOOLCHAIN": "nuclei_gnu", "STDCLIB": "newlib_small", "BENCH_FLAGS": "-Ofast -flto"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "application/baremetal/demo_cidu": {"build": true, "run": false}, "application/baremetal/demo_cache": {"build": true, "run": false}, "application/baremetal/demo_pma": {"build": true, "run": false}, "application/baremetal/demo_dsp": {"build": false, "run": false}, "application/baremetal/demo_stack_check": {"build": true, "run": false}, "test/core": {"build": true, "run": true}}}