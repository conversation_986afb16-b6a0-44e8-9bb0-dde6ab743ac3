{"build_config": {"CPU_SERIES": "900"}, "build_configs": {"rv32imac": {"CORE": "n900"}, "rv32imacb": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imacbp": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafc": {"CORE": "n900f", "ARCH_EXT": ""}, "rv32imafcb": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafcp": {"CORE": "n900f", "ARCH_EXT": "_xxldspn1x"}, "rv32imafcbp": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdc": {"CORE": "n900fd", "ARCH_EXT": ""}, "rv32imafdcb": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafdcp": {"CORE": "n900fd", "ARCH_EXT": "_xxldspn1x"}, "rv32imafdcbp": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcbp_zicond": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldspn1x"}, "rv32imafcv": {"CORE": "n900f", "ARCH_EXT": "_zve32f"}, "rv32imafcbv": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafcpv": {"CORE": "n900f", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafcbpv": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f"}, "rv32imafdcbv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafdcpv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafdcbpv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcbpv_zicond": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zicond_xxldspn1x"}, "rv32ima_zc": {"CORE": "n900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "rv32imab_zc": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt"}, "rv32imabp_zc": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxldspn1x"}, "rv32imaf_zc": {"CORE": "n900f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafb_zc": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafp_zc": {"CORE": "n900f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafbp_zc": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafd_zc": {"CORE": "n900fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafdb_zc": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafdp_zc": {"CORE": "n900fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafdbp_zc": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafdbp_zc_zicond": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_zicond_xxldspn1x"}, "rv32imafv_zc": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafbv_zc": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafpv_zc": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafbpv_zc": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafdv_zc": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafdbv_zc": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafdpv_zc": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafdbpv_zc": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafdbpv_zc_zicond": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_zicond_xxldspn1x"}}}