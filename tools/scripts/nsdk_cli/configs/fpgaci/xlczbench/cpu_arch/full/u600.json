{"build_config": {"CPU_SERIES": "600"}, "build_configs": {"rv32imac": {"CORE": "u600"}, "rv32imacb": {"CORE": "u600", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imacbp": {"CORE": "u600", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafc": {"CORE": "u600f", "ARCH_EXT": ""}, "rv32imafcb": {"CORE": "u600f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafcp": {"CORE": "u600f", "ARCH_EXT": "_xxldspn1x"}, "rv32imafcbp": {"CORE": "u600f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdc": {"CORE": "u600fd", "ARCH_EXT": ""}, "rv32imafdcb": {"CORE": "u600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafdcp": {"CORE": "u600fd", "ARCH_EXT": "_xxldspn1x"}, "rv32imafdcbp": {"CORE": "u600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32ima_xlcz": {"CORE": "u600", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz"}, "rv32imab_xlcz": {"CORE": "u600", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz"}, "rv32imabp_xlcz": {"CORE": "u600", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imaf_xlcz": {"CORE": "u600f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafb_xlcz": {"CORE": "u600f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafp_xlcz": {"CORE": "u600f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafbp_xlcz": {"CORE": "u600f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafd_xlcz": {"CORE": "u600fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdb_xlcz": {"CORE": "u600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdp_xlcz": {"CORE": "u600fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafdbp_xlcz": {"CORE": "u600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}}}