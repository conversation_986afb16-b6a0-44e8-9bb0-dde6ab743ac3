{"build_config": {"CPU_SERIES": "300"}, "build_configs": {"rv32imac": {"CORE": "n300"}, "rv32imacb": {"CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imacbp": {"CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafc": {"CORE": "n300f", "ARCH_EXT": ""}, "rv32imafcb": {"CORE": "n300f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafcp": {"CORE": "n300f", "ARCH_EXT": "_xxldspn1x"}, "rv32imafcbp": {"CORE": "n300f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdc": {"CORE": "n300fd", "ARCH_EXT": ""}, "rv32imafdcb": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafdcp": {"CORE": "n300fd", "ARCH_EXT": "_xxldspn1x"}, "rv32imafdcbp": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcbp_zicond": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldspn1x"}, "rv32ima_zc": {"CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "rv32imab_zc": {"CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt"}, "rv32imabp_zc": {"CORE": "n300", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxldspn1x"}, "rv32imaf_zc": {"CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafb_zc": {"CORE": "n300f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafp_zc": {"CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafbp_zc": {"CORE": "n300f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafd_zc": {"CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafdb_zc": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt"}, "rv32imafdp_zc": {"CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafdbp_zc": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxldspn1x"}, "rv32imafdbp_zc_zicond": {"CORE": "n300fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_zicond_xxldspn1x"}}}