{"build_config": {"CPU_SERIES": "900"}, "build_configs": {"rv32imac": {"CORE": "u900"}, "rv32imacb": {"CORE": "u900", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imacbp": {"CORE": "u900", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafc": {"CORE": "u900f", "ARCH_EXT": ""}, "rv32imafcb": {"CORE": "u900f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafcp": {"CORE": "u900f", "ARCH_EXT": "_xxldspn1x"}, "rv32imafcbp": {"CORE": "u900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdc": {"CORE": "u900fd", "ARCH_EXT": ""}, "rv32imafdcb": {"CORE": "u900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafdcp": {"CORE": "u900fd", "ARCH_EXT": "_xxldspn1x"}, "rv32imafdcbp": {"CORE": "u900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafcv": {"CORE": "u900f", "ARCH_EXT": "_zve32f"}, "rv32imafcbv": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafcpv": {"CORE": "u900f", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafcbpv": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f"}, "rv32imafdcbv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafdcpv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafdcbpv": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32ima_xlcz": {"CORE": "u900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz"}, "rv32imab_xlcz": {"CORE": "u900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz"}, "rv32imabp_xlcz": {"CORE": "u900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imaf_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafb_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafp_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafbp_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafd_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdb_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdp_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafdbp_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafv_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafbv_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafpv_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafbpv_xlcz": {"CORE": "u900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafdv_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdbv_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdpv_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafdbpv_xlcz": {"CORE": "u900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}}}