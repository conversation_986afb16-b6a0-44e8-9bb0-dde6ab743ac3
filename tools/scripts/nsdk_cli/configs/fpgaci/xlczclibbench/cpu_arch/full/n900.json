{"build_config": {"CPU_SERIES": "900"}, "build_configs": {"rv32imac": {"CORE": "n900"}, "rv32imacb": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imacbp": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafc": {"CORE": "n900f", "ARCH_EXT": ""}, "rv32imafcb": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafcp": {"CORE": "n900f", "ARCH_EXT": "_xxldspn1x"}, "rv32imafcbp": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdc": {"CORE": "n900fd", "ARCH_EXT": ""}, "rv32imafdcb": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafdcp": {"CORE": "n900fd", "ARCH_EXT": "_xxldspn1x"}, "rv32imafdcbp": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafcv": {"CORE": "n900f", "ARCH_EXT": "_zve32f"}, "rv32imafcbv": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafcpv": {"CORE": "n900f", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafcbpv": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdcv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f"}, "rv32imafdcbv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs"}, "rv32imafdcpv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_xxldspn1x"}, "rv32imafdcbpv": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32ima_xlcz": {"CORE": "n900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz"}, "rv32imab_xlcz": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz"}, "rv32imabp_xlcz": {"CORE": "n900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imaf_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafb_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafp_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafbp_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafd_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdb_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdp_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafdbp_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafv_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafbv_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafpv_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafbpv_xlcz": {"CORE": "n900f", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafdv_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdbv_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "rv32imafdpv_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}, "rv32imafdbpv_xlcz": {"CORE": "n900fd", "ARCH_EXT": "_zve32f_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_xxlcz_xxldspn1x"}}}