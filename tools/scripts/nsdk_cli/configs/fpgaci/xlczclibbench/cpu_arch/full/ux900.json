{"build_config": {"CPU_SERIES": "900"}, "build_configs": {"rv64imac": {"CORE": "nx900"}, "rv64imacb": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imacp": {"CORE": "nx900", "ARCH_EXT": "_xxldsp"}, "rv64imacbp": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldsp"}, "rv64imafc": {"CORE": "nx900f", "ARCH_EXT": ""}, "rv64imafcb": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imafcp": {"CORE": "nx900f", "ARCH_EXT": "_xxldsp"}, "rv64imafcbp": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldsp"}, "rv64imafdc": {"CORE": "nx900fd", "ARCH_EXT": ""}, "rv64imafdcb": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imafdcp": {"CORE": "nx900fd", "ARCH_EXT": "_xxldsp"}, "rv64imafdcbp": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldsp"}, "rv64imafcv": {"CORE": "nx900f", "ARCH_EXT": "_zve64f"}, "rv64imafdcv": {"CORE": "nx900fd", "ARCH_EXT": "v"}, "rv64imafcbv": {"CORE": "nx900f", "ARCH_EXT": "_zve64f_zba_zbb_zbc_zbs"}, "rv64imafdcbv": {"CORE": "nx900fd", "ARCH_EXT": "v_zba_zbb_zbc_zbs"}, "rv64imafcpv": {"CORE": "nx900f", "ARCH_EXT": "_zve64f_xxldsp"}, "rv64imafdcpv": {"CORE": "nx900fd", "ARCH_EXT": "v_xxldsp"}, "rv64imafcbpv": {"CORE": "nx900f", "ARCH_EXT": "_zve64f_zba_zbb_zbc_zbs_xxldsp"}, "rv64imafdcbpv": {"CORE": "nx900fd", "ARCH_EXT": "v_zba_zbb_zbc_zbs_xxldsp"}, "rv64ima_xlcz": {"CORE": "nx900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz"}, "rv64imab_xlcz": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz"}, "rv64imap_xlcz": {"CORE": "nx900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz_xxldsp"}, "rv64imabp_xlcz": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz_xxldsp"}, "rv64imaf_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz"}, "rv64imafb_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz"}, "rv64imafp_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz_xxldsp"}, "rv64imafbp_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz_xxldsp"}, "rv64imafd_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "_zca_zcb_zcd_xxlcz"}, "rv64imafdb_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcd_xxlcz"}, "rv64imafdp_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "_zca_zcb_zcd_xxlcz_xxldsp"}, "rv64imafdbp_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcd_xxlcz_xxldsp"}, "rv64imafv_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zve64f_zca_zcb_zcmp_zcmt_xxlcz"}, "rv64imafdv_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "v_zca_zcb_zcd_xxlcz"}, "rv64imafbv_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zve64f_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz"}, "rv64imafdbv_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "v_zba_zbb_zbc_zbs_zca_zcb_zcd_xxlcz"}, "rv64imafpv_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zve64f_zca_zcb_zcmp_zcmt_xxlcz_xxldsp"}, "rv64imafdpv_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "v_zca_zcb_zcd_xxlcz_xxldsp"}, "rv64imafbpv_xlcz": {"CORE": "nx900f", "ARCH_EXT": "_zve64f_zba_zbb_zbc_zbs_zca_zcb_zcmp_zcmt_xxlcz_xxldsp"}, "rv64imafdbpv_xlcz": {"CORE": "nx900fd", "ARCH_EXT": "v_zba_zbb_zbc_zbs_zca_zcb_zcd_xxlcz_xxldsp"}}}