{"run_config": {"target": "qemu", "hardware": {"baudrate": 115200, "timeout": 240}, "qemu": {"timeout": 240}}, "parallel": "-j", "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "TOOLCHAIN": "terapines", "ARCH_EXT": ""}, "build_configs": {"n200-ilm": {"DOWNLOAD": "ilm", "CORE": "n200", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n201-flash": {"DOWNLOAD": "flash", "CORE": "n201", "ARCH_EXT": "_zca_zcb_zcmp_zcmt"}, "n201e-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n201e", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zicond"}, "n203e-ilm": {"DOWNLOAD": "ilm", "CORE": "n203e", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zicond"}, "n300-ilm": {"DOWNLOAD": "ilm", "CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz"}, "n300b-flash": {"DOWNLOAD": "flash", "CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxlcz"}, "n300bp-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x_xxlcz"}, "n300fbp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn1x_xxlcz"}, "n300f-flash": {"DOWNLOAD": "flash", "CORE": "n300f", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxlcz"}, "n300fbpv-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300f", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn2x_xxlcz"}, "n300fdp_zc-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_xxldsp_xxlcz"}, "n300fdbp_zc-flash": {"DOWNLOAD": "flash", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x_xxlcz"}, "n300fdbpv_zc-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcmp_zcmt_zba_zbb_zbc_zbs_xxldspn3x_xxlcz"}, "n300fdp-ilm": {"DOWNLOAD": "ilm", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcd_xxldsp_xxlcz"}, "n300fdbp-flash": {"DOWNLOAD": "flash", "CORE": "n300fd", "ARCH_EXT": "_zca_zcb_zcf_zcd_zba_zbb_zbc_zbs_xxldspn3x_xxlcz"}, "n300fdbpv-flashxip": {"DOWNLOAD": "flashxip", "CORE": "n300fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcd_zba_zbb_zbc_zbs_xxldspn3x_xxlcz"}, "n900fdbpv_zc": {"DOWNLOAD": "ilm", "CORE": "n900fd", "ARCH_EXT": "_zve32f_zca_zcb_zcf_zcd_zba_zbb_zbc_zbs_xxldspn1x"}, "ux900b-ilm": {"DOWNLOAD": "ilm", "CORE": "ux900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxlcz"}, "ux900-flash": {"DOWNLOAD": "flash", "CORE": "ux900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_xxlcz"}, "ux900bp-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp_xxlcz"}, "ux900bp-ddr": {"DOWNLOAD": "ddr", "CORE": "ux900", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp_xxlcz"}, "ux900fbp-ilm": {"DOWNLOAD": "ilm", "CORE": "ux900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp_xxlcz"}, "ux900fbp-flash": {"DOWNLOAD": "flash", "CORE": "ux900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp_xxlcz"}, "ux900fbp-flashxip": {"DOWNLOAD": "flashxip", "CORE": "ux900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp_xxlcz"}, "ux900fbp-ddr": {"DOWNLOAD": "ddr", "CORE": "ux900f", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_xxldsp_xxlcz"}, "ux900fdbpv-ilm": {"DOWNLOAD": "ilm", "CORE": "ux900fd", "ARCH_EXT": "v_zca_zcb_zcd_zba_zbb_zbc_zbs_xxldsp_xxlcz"}, "ux900fdbp-flash": {"DOWNLOAD": "flash", "CORE": "ux900fd", "ARCH_EXT": "_zca_zcb_zcd_zba_zbb_zbc_zbs_xxldsp_xxlcz"}}, "appconfig": {}, "expected": {"application/baremetal/demo_nice": {"run": true, "build": true}, "application/baremetal/demo_dsp": {"run": false, "build": false}}}