{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "copy_objects": true, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "BOARD": "nuclei_fpga_eval", "ARCH_EXT": "", "DOWNLOAD": "ilm"}, "build_configs": {"n300fd-default": {"CORE": "n300fd"}, "n300fd-newlib-full": {"CORE": "n300fd", "STDCLIB": "newlib_full"}, "n300fd-newlib-fast": {"CORE": "n300fd", "STDCLIB": "newlib_fast"}, "n300fd-newlib-small": {"CORE": "n300fd", "STDCLIB": "newlib_small"}, "n300fd-newlib-nano": {"CORE": "n300fd", "STDCLIB": "newlib_nano"}, "n300fd-libncrt-pico": {"CORE": "n300fd", "STDCLIB": "libncrt_pico"}, "n300fd-libncrt-nano": {"CORE": "n300fd", "STDCLIB": "libncrt_nano"}, "n300fd-libncrt-small": {"CORE": "n300fd", "STDCLIB": "libncrt_small"}, "n300fd-libncrt-fast": {"CORE": "n300fd", "STDCLIB": "libncrt_fast"}, "n300fd-libncrt-balanced": {"CORE": "n300fd", "STDCLIB": "libncrt_balanced"}, "n300fdb-newlib-small": {"CORE": "n300fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300fdb-libncrt-small": {"CORE": "n300fd", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300f-default": {"CORE": "n300f"}, "n300f-newlib-full": {"CORE": "n300f", "STDCLIB": "newlib_full"}, "n300f-newlib-fast": {"CORE": "n300f", "STDCLIB": "newlib_fast"}, "n300f-newlib-small": {"CORE": "n300f", "STDCLIB": "newlib_small"}, "n300f-newlib-nano": {"CORE": "n300f", "STDCLIB": "newlib_nano"}, "n300f-libncrt-pico": {"CORE": "n300f", "STDCLIB": "libncrt_pico"}, "n300f-libncrt-nano": {"CORE": "n300f", "STDCLIB": "libncrt_nano"}, "n300f-libncrt-small": {"CORE": "n300f", "STDCLIB": "libncrt_small"}, "n300f-libncrt-fast": {"CORE": "n300f", "STDCLIB": "libncrt_fast"}, "n300f-libncrt-balanced": {"CORE": "n300f", "STDCLIB": "libncrt_balanced"}, "n300fb-newlib-small": {"CORE": "n300f", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300fb-libncrt-small": {"CORE": "n300f", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300-default": {"CORE": "n300"}, "n300-newlib-full": {"CORE": "n300", "STDCLIB": "newlib_full"}, "n300-newlib-fast": {"CORE": "n300", "STDCLIB": "newlib_fast"}, "n300-newlib-small": {"CORE": "n300", "STDCLIB": "newlib_small"}, "n300-newlib-nano": {"CORE": "n300", "STDCLIB": "newlib_nano"}, "n300-libncrt-pico": {"CORE": "n300", "STDCLIB": "libncrt_pico"}, "n300-libncrt-nano": {"CORE": "n300", "STDCLIB": "libncrt_nano"}, "n300-libncrt-small": {"CORE": "n300", "STDCLIB": "libncrt_small"}, "n300-libncrt-fast": {"CORE": "n300", "STDCLIB": "libncrt_fast"}, "n300-libncrt-balanced": {"CORE": "n300", "STDCLIB": "libncrt_balanced"}, "n300b-newlib-small": {"CORE": "n300", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300b-libncrt-small": {"CORE": "n300", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": true}, "application/rtthread/msh": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}