runcfg: include('rcfg')
environment: include('env')
fpga_runners:
  map(include('fpgarunner'), key=str())
ncycm_runners:
  map(include('ncycmrunner'), key=str())
configs:
  map(include('config'), key=str())
---

rcfg:
  runner: enum('fpga', 'ncycm', 'qemu', 'xlspike')

env:
  fpgaloc: str()
  ncycmloc: str()
  cfgloc: str()

fpgarunner:
  board_type: enum('ku060', 'ddr200t', 'mcu200t', 'vcu118', 'vu19p')
  fpga_serial: str()
  ftdi_serial: str()
  serial_port: any(str(),null())

ncycmrunner:
  model: str()

config:
  bitstream: str()
  fpga: enum('ku060', 'ddr200t', 'mcu200t', 'vcu118', 'vu19p')
  ncycm: str()
  openocd_cfg: str()
  appcfg: str()
  hwcfg: str()
