{"build_config": {"SOC": "evalsoc", "CPU_SERIES": "600"}, "build_configs": {"rv32imac": {"CORE": "n600"}, "rv32imac_zicond": {"CORE": "n600", "ARCH_EXT": "_zicond"}, "rv32imacb": {"CORE": "n600", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imacb_zicond": {"CORE": "n600", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond"}, "rv32imab_zc_zicond": {"CORE": "n600", "ARCH_EXT": "_zca_zcb_zcmp_zcmt_zba_zbb_zbc_zbs_zicond"}, "rv32imacbp": {"CORE": "n600", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafc": {"CORE": "n600f", "ARCH_EXT": ""}, "rv32imafcb": {"CORE": "n600f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafcp": {"CORE": "n600f", "ARCH_EXT": "_xxldspn1x"}, "rv32imafcbp": {"CORE": "n600f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafcbp_zicond": {"CORE": "n600f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldspn1x"}, "rv32imafdc": {"CORE": "n600fd", "ARCH_EXT": ""}, "rv32imafdcb": {"CORE": "n600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv32imafdcp": {"CORE": "n600fd", "ARCH_EXT": "_xxldspn1x"}, "rv32imafdcbp": {"CORE": "n600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv32imafdbp_zc_zicond": {"CORE": "n600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zca_zcb_zcf_zcmp_zcmt_zicond_xxldspn1x"}, "rv32imafdcbp_zicond": {"CORE": "n600fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldspn1x"}}}