{"build_config": {"SOC": "evalsoc", "CPU_SERIES": "900"}, "build_configs": {"rv64imac": {"CORE": "nx900"}, "rv64imac_zicond": {"CORE": "nx900", "ARCH_EXT": "_zicond"}, "rv64imacb": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imacb_zicond": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond"}, "rv64imacbp": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldsp"}, "rv64imacbp_zicond": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldsp"}, "rv64imafc": {"CORE": "nx900f", "ARCH_EXT": ""}, "rv64imafcb": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imafcp": {"CORE": "nx900f", "ARCH_EXT": "_xxldsp"}, "rv64imafcbp": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldsp"}, "rv64imafcbp_zicond": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldsp"}, "rv64imafdc": {"CORE": "nx900fd", "ARCH_EXT": ""}, "rv64imafdcb": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imafdcp": {"CORE": "nx900fd", "ARCH_EXT": "_xxldsp"}, "rv64imafdcbp": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldsp"}, "rv64imafdcbp_zicond": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_zicond_xxldsp"}}}