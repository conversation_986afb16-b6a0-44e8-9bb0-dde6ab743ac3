{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 60}, "qemu": {"qemu32": "qemu-system-riscv32", "qemu64": "qemu-system-riscv64", "timeout": 60}}, "build_target": "clean dasm", "build_config": {"SOC": "evalsoc"}, "copy_objects": true, "checks": {"PASS": [], "FAIL": ["MEPC"]}, "appdirs": ["application/baremetal/benchmark"], "appdirs_ignore": ["application/baremetal/dsp_examples", "application/baremetal/Internal"], "appconfig": {"application/baremetal/benchmark/dhrystone": {"build_config": {}, "checks": {"PASS": ["1000000/(User_Cycle/Number_Of_Runs)"]}}, "application/baremetal/benchmark/whetstone": {"build_config": {}, "checks": {"PASS": ["MWIPS/MHz"]}}, "application/baremetal/benchmark/coremark": {"build_config": {}, "checks": {"PASS": ["Iterations*1000000/total_ticks"]}}, "application/baremetal/demo_timer": {"build_config": {}, "checks": {"PASS": ["MTimer msip and mtip interrupt test finish and pass"]}}, "application/baremetal/helloworld": {"build_config": {}, "checks": {"PASS": ["19: Hello World From Nuclei RISC-V Processor!"]}}, "application/baremetal/demo_eclic": {"build_config": {}, "checks": {"PASS": ["software interrupt hit 10 times"]}}, "application/baremetal/demo_nice": {"build_config": {}, "checks": {"PASS": ["PASS"], "FAIL": ["FAIL", "MEPC"]}}, "application/baremetal/demo_dsp": {"build_config": {}, "checks": {"PASS": ["all test are passed"], "FAIL": ["test error apprears", "MEPC"]}}, "application/freertos/demo": {"build_config": {}, "checks": {"PASS": ["timers Call<PERSON> 11"]}}, "application/rtthread/demo": {"build_config": {}, "checks": {"PASS": ["Main thread count: 6"]}}, "application/rtthread/msh": {"build_config": {}, "checks": {"PASS": ["msh >"]}}, "application/ucosii/demo": {"build_config": {}, "checks": {"PASS": ["task3 is running... 10"]}}, "test/core": {"build_config": {}, "checks": {"PASS": [", 0 failed"], "FAIL": ["[FAIL]", "MEPC"]}}}}