{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "copy_objects": true, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "ARCH_EXT": "", "DOWNLOAD": "ilm"}, "build_configs": {"ux900bp-newlib-small": {"CORE": "ux900", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "ux900fdbp-newlib-small": {"CORE": "ux900fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}