{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "copy_objects": true, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "ARCH_EXT": "", "DOWNLOAD": "ilm"}, "build_configs": {"n600-newlib-small": {"CORE": "n600", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "n600-libncrt-small": {"CORE": "n600", "STDCLIB": "libncrt_small", "ARCH_EXT": ""}, "n600b-newlib-small": {"CORE": "n600", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n600b-libncrt-small": {"CORE": "n600", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n600p-newlib-small": {"CORE": "n600", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "n600p-libncrt-small": {"CORE": "n600", "STDCLIB": "libncrt_small", "ARCH_EXT": "_xxldspn1x"}, "n600bp-newlib-small": {"CORE": "n600", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n600bp-libncrt-small": {"CORE": "n600", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n600f-newlib-small": {"CORE": "n600f", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "n600f-libncrt-small": {"CORE": "n600f", "STDCLIB": "libncrt_small", "ARCH_EXT": ""}, "n600fb-newlib-small": {"CORE": "n600f", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n600fb-libncrt-small": {"CORE": "n600f", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n600fp-newlib-small": {"CORE": "n600f", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "n600fp-libncrt-small": {"CORE": "n600f", "STDCLIB": "libncrt_small", "ARCH_EXT": "_xxldspn1x"}, "n600fbp-newlib-small": {"CORE": "n600f", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n600fbp-libncrt-small": {"CORE": "n600f", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n600fd-newlib-small": {"CORE": "n600fd", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "n600fd-libncrt-small": {"CORE": "n600fd", "STDCLIB": "libncrt_small", "ARCH_EXT": ""}, "n600fdb-newlib-small": {"CORE": "n600fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n600fdb-libncrt-small": {"CORE": "n600fd", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n600fdp-newlib-small": {"CORE": "n600fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "n600fdp-libncrt-small": {"CORE": "n600fd", "STDCLIB": "libncrt_small", "ARCH_EXT": "_xxldspn1x"}, "n600fdbp-newlib-small": {"CORE": "n600fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n600fdbp-libncrt-small": {"CORE": "n600fd", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}