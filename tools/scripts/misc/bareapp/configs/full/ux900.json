{"build_config": {"SOC": "evalsoc", "DOWNLOAD": "ilm", "CPU_SERIES": "900"}, "build_configs": {"rv64imac": {"CORE": "nx900"}, "rv64imacb": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imacbp": {"CORE": "nx900", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv64imafc": {"CORE": "nx900f", "ARCH_EXT": ""}, "rv64imafcb": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imafcp": {"CORE": "nx900f", "ARCH_EXT": "_xxldspn1x"}, "rv64imafcbp": {"CORE": "nx900f", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "rv64imafdc": {"CORE": "nx900fd", "ARCH_EXT": ""}, "rv64imafdcb": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "rv64imafdcp": {"CORE": "nx900fd", "ARCH_EXT": "_xxldspn1x"}, "rv64imafdcbp": {"CORE": "nx900fd", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}