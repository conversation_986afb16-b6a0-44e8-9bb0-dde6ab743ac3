{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "copy_objects": true, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "ARCH_EXT": "", "DOWNLOAD": "ilm"}, "build_configs": {"n203-newlib-small": {"CORE": "n203", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "n203-libncrt-small": {"CORE": "n203", "STDCLIB": "libncrt_small", "ARCH_EXT": ""}, "n203b-newlib-small": {"CORE": "n203", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n203b-libncrt-small": {"CORE": "n203", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "application/baremetal/demo_spmp": {"build": true, "run": false}, "application/baremetal/demo_pmp": {"build": true, "run": false}, "application/baremetal/demo_smode_eclic": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}