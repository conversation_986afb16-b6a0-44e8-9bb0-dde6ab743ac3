{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "copy_objects": true, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "ARCH_EXT": "", "DOWNLOAD": "ilm"}, "build_configs": {"ux600-newlib-small": {"CORE": "ux600", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "ux600b-newlib-small": {"CORE": "ux600", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "ux600p-newlib-small": {"CORE": "ux600", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "ux600bp-newlib-small": {"CORE": "ux600", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "ux600f-newlib-small": {"CORE": "ux600f", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "ux600fb-newlib-small": {"CORE": "ux600f", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "ux600fp-newlib-small": {"CORE": "ux600f", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "ux600fbp-newlib-small": {"CORE": "ux600f", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "ux600fd-newlib-small": {"CORE": "ux600fd", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "ux600fdb-newlib-small": {"CORE": "ux600fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "ux600fdp-newlib-small": {"CORE": "ux600fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "ux600fdbp-newlib-small": {"CORE": "ux600fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "application/rtthread/msh": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}