{"run_config": {"target": "hardware", "hardware": {"baudrate": 115200, "timeout": 240}}, "parallel": "-j", "copy_objects": true, "build_target": "clean all", "build_config": {"SOC": "evalsoc", "ARCH_EXT": "", "DOWNLOAD": "ilm"}, "build_configs": {"n300-newlib-small": {"CORE": "n300", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "n300-libncrt-small": {"CORE": "n300", "STDCLIB": "libncrt_small", "ARCH_EXT": ""}, "n300b-newlib-small": {"CORE": "n300", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300b-libncrt-small": {"CORE": "n300", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300p-newlib-small": {"CORE": "n300", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "n300p-libncrt-small": {"CORE": "n300", "STDCLIB": "libncrt_small", "ARCH_EXT": "_xxldspn1x"}, "n300bp-newlib-small": {"CORE": "n300", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n300bp-libncrt-small": {"CORE": "n300", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n300f-newlib-small": {"CORE": "n300f", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "n300f-libncrt-small": {"CORE": "n300f", "STDCLIB": "libncrt_small", "ARCH_EXT": ""}, "n300fb-newlib-small": {"CORE": "n300f", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300fb-libncrt-small": {"CORE": "n300f", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300fp-newlib-small": {"CORE": "n300f", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "n300fp-libncrt-small": {"CORE": "n300f", "STDCLIB": "libncrt_small", "ARCH_EXT": "_xxldspn1x"}, "n300fbp-newlib-small": {"CORE": "n300f", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n300fbp-libncrt-small": {"CORE": "n300f", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n300fd-newlib-small": {"CORE": "n300fd", "STDCLIB": "newlib_small", "ARCH_EXT": ""}, "n300fd-libncrt-small": {"CORE": "n300fd", "STDCLIB": "libncrt_small", "ARCH_EXT": ""}, "n300fdb-newlib-small": {"CORE": "n300fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300fdb-libncrt-small": {"CORE": "n300fd", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs"}, "n300fdp-newlib-small": {"CORE": "n300fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_xxldspn1x"}, "n300fdp-libncrt-small": {"CORE": "n300fd", "STDCLIB": "libncrt_small", "ARCH_EXT": "_xxldspn1x"}, "n300fdbp-newlib-small": {"CORE": "n300fd", "STDCLIB": "newlib_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}, "n300fdbp-libncrt-small": {"CORE": "n300fd", "STDCLIB": "libncrt_small", "ARCH_EXT": "_zba_zbb_zbc_zbs_xxldspn1x"}}, "expected": {"application/baremetal/demo_nice": {"build": true, "run": false}, "application/rtthread/msh": {"build": true, "run": false}, "test/core": {"build": false, "run": false}}}