NUCLEI_SDK_BUILD = $(NUCLEI_SDK_ROOT)/Build
# Include your local and global makefile variables
# SOC, DOWNLOAD, STDCLIB etc.
# Makefile.global should be placed in $(NUCLEI_SDK_ROOT)/Build
# Makefile.local should be placed together with application Makefile in your own application folder
# Sample content for this Makefile.local or Makefile.global
# SOC ?= evalsoc
# DOWNLOAD ?= ilm
EXTRA_MKS := $(wildcard Makefile.local $(NUCLEI_SDK_BUILD)/Makefile.global)
ifneq ("$(strip $(EXTRA_MKS))", "")
$(info Obtaining addtional make variables from $(realpath $(EXTRA_MKS)))
include $(EXTRA_MKS)
endif

# Variables could be passed in make command
# NOTE: CORE and BOARD are defined in $(NUCLEI_SDK_ROOT)/SoC/$(SOC)/Makefile.build
# BOARD and SOC name should always be lower-case
## Available choices:
## The name of sub directories in $(NUCLEI_SDK_ROOT)/SoC/
SOC ?= evalsoc
## Available choices:
## ilm: Program will be downloaded into ilm/ram and run directly in ilm/ram, program lost when poweroff
## flash: Program will be downloaded into flash, when running, program will be copied to ilm/ram and run in ilm/ram
## flashxip: Program will be downloaded into flash and run directly in Flash
DOWNLOAD ?= ilm
## If SIMULATION=1, it means the program is optimized for hardware simulation environment
SIMULATION ?= 0
## If V=1, it will display compiling message in verbose including compiling options
V ?=
## If SILENT=1, it will not display any compiling messsage
SILENT ?=

# Variables should be defined in Application Makefile
## Available choices:
## The name of sub directories in $(NUCLEI_SDK_ROOT)/OS/
RTOS ?=
## Available choices:
##### Using newlib
### newlib_full: Normal newlib library with full newlib feature
### newlib_fast: Newlib-nano library with printf and scanf float feature
### newlib_small: Newlib-nano library with printf float feature
### newlib_nano: Newlib-nano library without float printf/scanf feature
##### Using Nuclei C Runtime library
### libncrt_fast: Nuclei C Runtime library, favor speed at the expense of size
### libncrt_balanced: balanced, full feature
### libncrt_small: favor size at the expense of speed, but full feature
### libncrt_nano: favor size at the expense of speed, no float support
### libncrt_pico: favor size at the expense of speed, no long/long long support
##### Using without system library or include
### nostd: Don't search the standard system directories for header files
### nospec: Don't pass any --specs options
STDCLIB ?= newlib_nano
## Available choices:
### Use to choose heapops type for libncrt, only available for libncrt
### basic: A low-overhead best-fit heap where allocation and deallocation have very little internal fragmentation
### realtime: A real-time heap where allocation and deallocation have O(1) performance
### minimal: An allocate-only heap where deallocation and reallocation are not implemented
NCRTHEAP ?= basic
## Available choices:
### Use to choose fileops type for libncrt, only available for libncrt
### uart: lower level input/output via uart, developer need to implement metal_tty_putc/getc
### semi: input/output via semihosting
### rtt: input/output via jlink rtt, require to use JLink tool
NCRTIO ?= uart
## If NOGC=1, it will not gc any sections during compiling to save code size
NOGC ?=
## If BANNER=0, it will not display sdk banner when program run
BANNER ?=
## If AUTOVEC=0, it will try to disable compiler auto vectorization as much as possible
AUTOVEC ?=

# Directory variables for NMSIS, SoC/RTOS chosen, Middleware Components
# NUCLEI_SDK_SOC and NUCLEI_SDK_RTOS variables need to be set deferred
NUCLEI_SDK_NMSIS ?= $(NUCLEI_SDK_ROOT)/NMSIS
NUCLEI_SDK_SOC = $(NUCLEI_SDK_ROOT)/SoC/$(SOC)
NUCLEI_SDK_RTOS = $(NUCLEI_SDK_ROOT)/OS/$(RTOS)
NUCLEI_SDK_MIDDLEWARE := $(NUCLEI_SDK_ROOT)/Components

## Include GNU Make Standard Library
## Website: http://gmsl.sourceforge.net/
include $(NUCLEI_SDK_BUILD)/gmsl/gmsl

include $(NUCLEI_SDK_BUILD)/Makefile.misc
include $(NUCLEI_SDK_BUILD)/Makefile.conf
include $(NUCLEI_SDK_BUILD)/Makefile.rules
