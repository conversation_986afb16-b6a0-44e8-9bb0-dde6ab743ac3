NUCLEI_SDK_TOOLCHAIN_MK=$(NUCLEI_SDK_BUILD)/toolchain/$(TOOLCHAIN).mk
ifeq ($(wildcard $(NUCLEI_SDK_TOOLCHAIN_MK)),)
$(warning Cannot find toolchain related configuration makefile $(NUCLEI_SDK_TOOLCHAIN_MK), use default TOOLCHAIN=nuclei_gnu)
override TOOLCHAIN := nuclei_gnu
override NUCLEI_SDK_TOOLCHAIN_MK=$(NUCLEI_SDK_BUILD)/toolchain/$(TOOLCHAIN).mk
endif
include $(NUCLEI_SDK_TOOLCHAIN_MK)
MAKEFILE_PREREQS += $(NUCLEI_SDK_TOOLCHAIN_MK)
