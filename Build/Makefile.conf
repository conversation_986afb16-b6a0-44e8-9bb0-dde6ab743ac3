# Toolchain related settings
TOOLCHAIN ?= nuclei_gnu

# Overwrite NEWLIB when STDCLIB is defined
ifdef STDCLIB
override NEWLIB := $(STDCLIB)
endif

include $(NUCLEI_SDK_BUILD)/Makefile.files
include $(NUCLEI_SDK_NMSIS)/build.mk
include $(NUCLEI_SDK_BUILD)/Makefile.soc

# Set your remote bitbang port
RBB_PORT ?= 9824
# Set your GDB port using variable GDB_PORT
GDB_PORT ?= 3333
## Makefile Variable GDBREMOTE
# You can change GDBREMOTE to other gdb remotes
## eg. if you have started openocd server with (bindto 0.0.0.0 defined in openocd.cfg)
## make sure your machine can connect to remote machine
## in remote machine(ipaddr **************, port 3333) which connect the hardware board,
## then you can change the GDBREMOTE to **************:3333
## GDBREMOTE ?= **************:3333
GDBREMOTE ?= | $(OPENOCD) $(OPENOCD_OPT) -c \"$(OPENOCD_CMD_ARGS); gdb_port pipe; log_output openocd.log\" -f $(OPENOCD_CFG)

GDB_UPLOAD_ARGS ?= --batch
GDB_UPLOAD_CMDS += -ex 'thread apply all monitor reset halt'
GDB_UPLOAD_CMDS += -ex 'thread apply all info reg pc'
GDB_UPLOAD_CMDS += -ex 'thread 1'
GDB_UPLOAD_CMDS += -ex 'load $<'
GDB_UPLOAD_CMDS += -ex 'file $<'
# $$pc is for $pc in gdb, but need to do this in makefile
# please make sure the _start label is provided in your startup code
# and is the entry of the startup of cpu
# make sure pc is set to _start after load
# when load program to flash, the cpu will do reset init
# before flash erase and do reset halt after flash write end
# so we need to manually set pc to _start
# see https://openocd.org/doc/html/CPU-Configuration.html#Target-Events
GDB_UPLOAD_CMDS += -ex 'thread apply all set $$pc=_start'
GDB_UPLOAD_CMDS += -ex 'thread apply all info reg pc'
GDB_UPLOAD_CMDS += -ex 'thread 1'
GDB_UPLOAD_CMDS += -ex 'monitor resume'
GDB_UPLOAD_CMDS += -ex 'quit'

OPENOCD_PORT_ARGS = -c "$(OPENOCD_CMD_ARGS); gdb_port $(GDB_PORT)"

OPENOCD_ARGS += $(OPENOCD_OPT) -f $(OPENOCD_CFG)
GDB_CMDS += -ex "set remotetimeout 240"
GDB_CMDS += -ex "target extended-remote localhost:$(GDB_PORT)"

DOWNLOAD_UPPER = $(call uc, $(DOWNLOAD))

DEFAULT_RISCV_ARCH ?= rv32imac
DEFAULT_RISCV_ABI ?= ilp32

ifeq ($(RISCV_ARCH),)
$(warning RISCV_ARCH is not defined, use $(DEFAULT_RISCV_ARCH) as default)
RISCV_ARCH := $(DEFAULT_RISCV_ARCH)
endif
ifeq ($(RISCV_ABI),)
$(warning RISCV_ABI is not defined, use $(DEFAULT_RISCV_ABI) as default)
RISCV_ABI := $(DEFAULT_RISCV_ABI)
endif

# Adjust CPU_SERIES according to passed CORE
ifneq ($(findstring 20,$(CORE)),)
CPU_SERIES ?= 200
else ifneq ($(findstring 30,$(CORE)),)
CPU_SERIES ?= 300
else ifneq ($(findstring 60,$(CORE)),)
CPU_SERIES ?= 600
else ifneq ($(findstring 90,$(CORE)),)
CPU_SERIES ?= 900
else ifneq ($(findstring 100,$(CORE)),)
CPU_SERIES ?= 1000
LDSPEC_EN := 1
else
CPU_SERIES ?= 0
endif

# Set default RISC_CMODEL=medlow for rv32, otherwise medany
ifeq ($(RISCV_CMODEL),)
ifneq ($(findstring 32,$(RISCV_ABI)),)
override RISCV_CMODEL := medlow
else
override RISCV_CMODEL := medany
endif
endif

XLSPIKE := xl_spike
XLMODEL := xl_cpumodel
# QEMU settings
ifneq ($(findstring 32,$(RISCV_ABI)),)
QEMU := qemu-system-riscv32
else
QEMU := qemu-system-riscv64
endif

## QEMU/XLSPIKE options
QEMU_OPT += -icount shift=0

# Include RTOS and Components Makefiles
include $(NUCLEI_SDK_BUILD)/Makefile.rtos
include $(NUCLEI_SDK_BUILD)/Makefile.components

XLSPIKE_ARCH ?= $(RISCV_ARCH)

MKDEP_OPT = -MMD -MT $@ -MF $@.d
C_INCLUDE_OPT = $(foreach dir,$(sort $(INCDIRS) $(C_INCDIRS)),-I$(dir))
CXX_INCLUDE_OPT = $(foreach dir,$(sort $(INCDIRS) $(CXX_INCDIRS)),-I$(dir))
ASM_INCLUDE_OPT = $(foreach dir,$(sort $(INCDIRS) $(ASM_INCDIRS)),-I$(dir))

ifeq ($(NOGC),1)
GC_CFLAGS =
GC_LDFLAGS =
else
GC_CFLAGS = -ffunction-sections -fdata-sections
GC_LDFLAGS = -Wl,--gc-sections -Wl,--check-sections
endif

ifeq ($(SIMULATION),1)
COMMON_FLAGS += -DCFG_SIMULATION
endif

ifeq ($(BANNER),0)
COMMON_FLAGS += -DNUCLEI_BANNER=0
endif

include $(NUCLEI_SDK_BUILD)/Makefile.toolchain

COMMON_FLAGS += -g -fno-common

# For Nuclei N200/NS200 series CPU, unaligned access feature is configurable, if this feature is not enabled
# then unaligned access is not supported, so we must pass -mstrict-align, see https://gcc.gnu.org/onlinedocs/gcc/RISC-V-Options.html
#COMMON_FLAGS += -mstrict-align

# Recommend to disable strict aliasing to avoid unexpected optimization, linux enabled it https://www.mail-archive.com/<EMAIL>/msg01647.html
#COMMON_FLAGS += -fno-strict-aliasing

COMMON_FLAGS += $(APP_COMMON_FLAGS) $(GC_CFLAGS) \
	-DDOWNLOAD_MODE=DOWNLOAD_MODE_$(DOWNLOAD_UPPER) \
	-DDOWNLOAD_MODE_STRING=\"$(DOWNLOAD_UPPER)\" \
	-DCPU_SERIES=$(CPU_SERIES)

CFLAGS += $(COMMON_FLAGS) $(APP_CFLAGS) $(C_INCLUDE_OPT)
CXXFLAGS += $(COMMON_FLAGS) $(APP_CXXFLAGS) $(CXX_INCLUDE_OPT)
ASMFLAGS += -x assembler-with-cpp $(COMMON_FLAGS) $(APP_ASMFLAGS) $(ASM_INCLUDE_OPT)

LIB_OPT = $(addprefix -L, $(sort $(LIBDIRS)))

# filter out -lm library when using libncrt library
# since libncrt also provided math library feature
# it is used to avoid issue like undefined reference to `__errno'`
ifneq ($(findstring libncrt,$(STDCLIB)),)
LDLIBS := $(filter-out -lm,$(LDLIBS))
endif

LDFLAGS += -T $(LINKER_SCRIPT) -nostartfiles -Wl,-Map=$(BUILD_DIR)/$(TARGET).map \
		$(APP_LDFLAGS) $(GC_LDFLAGS) $(STDCLIB_LDFLAGS) \
		$(LIB_OPT) -Wl,--start-group $(LDLIBS) -Wl,--end-group

# Prerequesties
MAKEFILE_PREREQS += $(wildcard $(NUCLEI_SDK_BUILD)/Makefile.*)
MAKEFILE_PREREQS += $(EXTRA_MKS)
MAKEFILE_PREREQS += $(wildcard makefile Makefile *.mk)

LINK_PREREQS += $(LINKER_SCRIPT)

COMMON_PREREQS = $(MAKEFILE_PREREQS) $(LINK_PREREQS)
