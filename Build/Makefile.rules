TARGET_ELF := $(BUILD_DIR)/$(TARGET).elf

ALL_CSRCS = $(filter-out $(wildcard $(EXCLUDE_SRCS)), $(sort $(wildcard $(C_SRCS)) $(call get_csrcs, $(SRCDIRS) $(C_SRCDIRS))))
ALL_CXXSRCS = $(filter-out $(wildcard $(EXCLUDE_SRCS)), $(sort $(wildcard $(CXX_SRCS)) $(call get_cxxsrcs, $(SRCDIRS) $(CXX_SRCDIRS))))
ALL_ASMSRCS = $(filter-out $(wildcard $(EXCLUDE_SRCS)), $(sort $(wildcard $(ASM_SRCS)) $(call get_asmsrcs, $(SRCDIRS) $(ASM_SRCDIRS))))

ALL_C_OBJS   := $(patsubst %.c,$(BUILD_DIR)/%.o,$(filter %.c,$(ALL_CSRCS)))
ALL_CXX_OBJS := $(patsubst %.cpp,$(BUILD_DIR)/%.o,$(filter %.cpp,$(ALL_CXXSRCS)))
ALL_ASM_OBJS := $(patsubst %.S,$(BUILD_DIR)/%.o,$(filter %.S,$(ALL_ASMSRCS)))
ALL_ASM_OBJS += $(patsubst %.s,$(BUILD_DIR)/%.o,$(filter %.s,$(ALL_ASMSRCS)))

ALL_OBJS += $(ALL_ASM_OBJS) $(ALL_C_OBJS) $(ALL_CXX_OBJS)


ALL_DEPS := $(patsubst %.o,%.d,$(ALL_OBJS))
ALL_GCNOS := $(patsubst %.o,%.gcno,$(ALL_OBJS))


all: info $(TARGET_ELF)

# include dependency files of application
ifneq ($(MAKECMDGOALS),clean)
-include $(ALL_DEPS)
endif


.PHONY: all info showflags showtoolver help bin size dasm upload run_openocd run_gdb run_qemu run_xlspike run_xlmodel run_qemu_debug run_xlspike_rbb run_xlspike_openocd clean debug

info:
	@$(ECHO) Current Configuration: TOOLCHAIN=$(TOOLCHAIN) RISCV_ARCH=$(RISCV_ARCH) RISCV_ABI=$(RISCV_ABI) RISCV_TUNE=$(RISCV_TUNE) RISCV_CMODEL=$(RISCV_CMODEL) SOC=$(SOC) BOARD=$(BOARD) CORE=$(CORE) ARCH_EXT=$(ARCH_EXT) DOWNLOAD=$(DOWNLOAD) STDCLIB=$(STDCLIB) SMP=$(SMP) BOOT_HARTID=$(BOOT_HARTID)

showflags:
	@$(ECHO) TARGET: $(TARGET)
	@$(ECHO) NUCLEI_SDK_ROOT: $(NUCLEI_SDK_ROOT)
	@$(ECHO) NUCLEI_SDK_NMSIS: $(NUCLEI_SDK_NMSIS)
	@$(ECHO) OPENOCD_CFG: $(OPENOCD_CFG)
	@$(ECHO) LINKER_SCRIPT: $(LINKER_SCRIPT)
	@$(ECHO) ASMFLAGS: $(ASMFLAGS)
	@$(ECHO) CFLAGS: $(CFLAGS)
	@$(ECHO) CXXFLAGS: $(CXXFLAGS)
	@$(ECHO) LDFLAGS: $(LDFLAGS)
	@$(ECHO) CC: $(CC)
	@$(ECHO) CXX: $(CXX)
	@$(ECHO) GDB: $(GDB)

showtoolver:
	@$(ECHO) Show $(CC) version
	@$(CC) -v
	@$(ECHO) Show $(OPENOCD) version
	@$(OPENOCD) -v
	@$(ECHO) Show $(QEMU) version
	-@$(QEMU) --version

help:
	@$(ECHO) "Nuclei N/NX-series RISC-V Embedded Processor Software Development Kit "
	@$(ECHO) "== For detailed user guide, please check https://doc.nucleisys.com/nuclei_sdk/"
	@$(ECHO) "== Make variables used in Nuclei SDK =="
	@$(ECHO) "SOC:         Select SoC built in Nuclei SDK, will select evalsoc by default"
	@$(ECHO) "BOARD:       Select SoC's Board built in Nuclei SDK, will select nuclei_fpga_eval by default"
	@$(ECHO) "CORE:        Not required for all SoCs, currently only evalsoc require it, n307fd by default"
	@$(ECHO) "ARCH_EXT:    Not required for all SoCs, currently only evalsoc require it, such as ARCH_EXT=_zba_zbb_zbc_zbs_xxldspn3x, means B and Nuclei DSP(p v0.5.3) with N1/N2/N3 extension, please use >= gcc 13"
	@$(ECHO) "DOWNLOAD:    Not required for all SoCs, use ilm by default, optional flashxip/ilm/flash/ddr"
	@$(ECHO) "V:           V=1 verbose make, will print more information, by default V=0"
	@$(ECHO) "== How to Use with Make =="
	@$(ECHO) "1. Build Application:"
	@$(ECHO) "all [PROGRAM=flash/flashxip/ilm/ddr]"
	@$(ECHO) "   Build a software program to load with the debugger."
	@$(ECHO) "2. Upload Application to Board using OpenOCD and GDB:"
	@$(ECHO) "upload [PROGRAM=flash/flashxip/ilm/ddr]"
	@$(ECHO) "   Launch OpenOCD to flash your program to the on-board Flash."
	@$(ECHO) "3:(Option 1) Debug Application using OpenOCD and GDB"
	@$(ECHO) "  3.1: run_openocd"
	@$(ECHO) "  3.2: run_gdb [PROGRAM=flash/flashxip/ilm/ddr]"
	@$(ECHO) "   Step 1: Launch OpenOCD for Debugger connection: make run_openocd"
	@$(ECHO) "   Step 2: Launch GDB to connect openocd server, you can set breakpoints using gdb and debug it."
	@$(ECHO) "           If you want to load your application, you need to run load in gdb command terminal"
	@$(ECHO) "           to load your program, then use gdb to debug it."
	@$(ECHO) "3:(Option 2) Debug Application using OpenOCD and GDB"
	@$(ECHO) "debug [PROGRAM=flash/flashxip/ilm/ddr]"
	@$(ECHO) "   Launch GDB and OpenOCD to debug your application on-board, you can set breakpoints using gdb and debug it."
	@$(ECHO) "           If you want to load your application, you need to run load in gdb command terminal"
	@$(ECHO) "           to load your program, then use gdb to debug it."
	@$(ECHO) ""
	@$(ECHO) "== Example Usage =="
	@$(ECHO) "1. cd <NUCLEI_SDK_ROOT>/application/helloworld"
	@$(ECHO) "2. Build for ILM download mode: make DOWNLOAD=ilm all"
	@$(ECHO) "3. Download application to board: make DOWNLOAD=ilm upload"
	@$(ECHO) "4.(Option 1) Debug application:"
	@$(ECHO) "   4.1 Terminal One: make DOWNLOAD=ilm run_openocd"
	@$(ECHO) "   4.2 Terminal Two: make DOWNLOAD=ilm run_gdb"
	@$(ECHO) "4.(Option 2) Debug application: make DOWNLOAD=ilm debug"
	@$(ECHO) ""


$(TARGET_ELF): $(ALL_OBJS)
	$(TRACE_LINK)
	$(Q)$(CC) $(CFLAGS) $(ALL_OBJS) -o $@ $(LDFLAGS)
	$(Q)$(SIZE) $@

$(BUILD_DIR)/%.o: %.c $(COMMON_PREREQS)
	@mkdir -p $(dir $@)
	$(TRACE_COMPILE)
	$(Q)$(CC) $(CFLAGS) $(MKDEP_OPT) -c -o $@ $<

$(BUILD_DIR)/%.o: %.cpp $(COMMON_PREREQS)
	@mkdir -p $(dir $@)
	$(TRACE_COMPILE)
	$(Q)$(CXX) $(CXXFLAGS) $(MKDEP_OPT) -c -o $@ $<

$(BUILD_DIR)/%.o: %.S $(COMMON_PREREQS)
	@mkdir -p $(dir $@)
	$(TRACE_ASSEMBLE)
	$(Q)$(CC) $(ASMFLAGS) $(MKDEP_OPT) -c -o $@ $<

$(BUILD_DIR)/%.o: %.s $(COMMON_PREREQS)
	@mkdir -p $(dir $@)
	$(TRACE_ASSEMBLE)
	$(Q)$(CC) $(ASMFLAGS) $(MKDEP_OPT) -c -o $@ $<


dasm: $(TARGET_ELF)
	-$(OBJDUMP) -S -d -M no-aliases $< > $(BUILD_DIR)/$(TARGET).dump
	-$(OBJDUMP) -d -M no-aliases $< > $(BUILD_DIR)/$(TARGET).dasm
	-$(OBJCOPY) $< -O ihex $(BUILD_DIR)/$(TARGET).hex
ifeq ($(TOOLCHAIN),nuclei_gnu)
	-$(OBJCOPY) $< -O srec $(BUILD_DIR)/$(TARGET).srec
	-$(OBJCOPY) $< -O verilog $(BUILD_DIR)/$(TARGET).verilog
endif

bin: $(TARGET_ELF)
	$(OBJCOPY) $< -O binary $(BUILD_DIR)/$(TARGET).bin

size: $(TARGET_ELF)
	$(Q)$(SIZE) $<

upload: $(TARGET_ELF)
	@$(ECHO) "Download and run $<"
	$(GDB) -ex "set remotetimeout 240" -ex "target remote $(GDBREMOTE)" $(GDB_UPLOAD_ARGS) $(GDB_UPLOAD_CMDS)

run_openocd:
	@$(ECHO) "Start openocd server"
	$(OPENOCD) $(OPENOCD_PORT_ARGS) $(OPENOCD_ARGS)

run_gdb: $(TARGET_ELF)
	@$(ECHO) "Run gdb to connect openocd server and debug"
	$(GDB) $< $(GDB_ARGS) $(GDB_CMDS)

debug: $(TARGET_ELF)
	@$(ECHO) "Download and debug $<"
	$(GDB) $< -ex "set remotetimeout 240" -ex "target remote $(GDBREMOTE)"

# just for evalsoc purpose
# https://doc.nucleisys.com/nuclei_tools/qemu/intro.html
run_qemu: $(TARGET_ELF)
	@$(ECHO) "Run program $< on $(QEMU)"
	$(QEMU) -M $(QEMU_MACHINE) -cpu $(QEMU_CPU) $(QEMU_OPT) -nodefaults -nographic -serial stdio $(QEMU_ELF_OPT) $<

# just for evalsoc purpose
run_qemu_debug: $(TARGET_ELF)
	@$(ECHO) "Run program $< on $(QEMU), and waiting for gdb connection on port $(GDB_PORT)"
	@$(ECHO) "Then you can open a new terminal in the same directory and run make target: run_gdb to connect to this gdb connection"
	$(QEMU) -M $(QEMU_MACHINE) -cpu $(QEMU_CPU) $(QEMU_OPT) -nodefaults -nographic -serial stdio $(QEMU_ELF_OPT) $< -S -gdb tcp::$(GDB_PORT)

# just for evalsoc purpose using xlmodel
# https://doc.nucleisys.com/nuclei_tools/xlmodel/intro.html
run_xlmodel: $(TARGET_ELF)
	@$(ECHO) "Run program $< on $(XLMODEL)"
	$(XLMODEL) $(XLMODEL_OPT) $(XLMODEL_EXTOPT) $<

# just works on evalsoc purpose, experimental support
run_xlspike: $(TARGET_ELF)
	@$(ECHO) "Run program $< on $(XLSPIKE)"
	$(XLSPIKE) $(XLSPIKE_OPT) --isa $(XLSPIKE_ARCH) $<

run_xlspike_rbb: $(TARGET_ELF)
	@$(ECHO) "Run program $< on $(XLSPIKE), and waiting for remote bitbang connection on port $(RBB_PORT)"
	@$(ECHO) "Then you need open a new terminal in the same directory and run make target: run_xlspike_openocd to connect to this remote bitbang connection"
	$(XLSPIKE) $(XLSPIKE_OPT) --rbb-port=$(RBB_PORT) -H --isa $(XLSPIKE_ARCH) $<

run_xlspike_openocd:
	@$(ECHO) "Run openocd to connect to remote bitbang connection"
	@$(ECHO) "Then you can open a new terminal in the same directory and run make target: run_gdb to connect to this gdb connection"
	$(OPENOCD) $(OPENOCD_PORT_ARGS) -f $(OPENOCD_XLSPIKE_CFG)


clean:
	@$(ECHO) "Clean all build objects"
	$(Q)$(RM)  openocd.log
	$(Q)$(RM) -r $(BUILD_DIR)
