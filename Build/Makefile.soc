ifeq ($(SOC),demosoc)
$(warning SOC demosoc is deprecated since Nuclei SDK 0.5.0, please use SOC=evalsoc now)
override SOC := evalsoc
endif

# Check if the SOC is valid, if there is only one SoC in this folder, force it to be the only SoC
ifeq ($(wildcard $(NUCLEI_SDK_SOC)),)
NUCLEI_SOC_VALID_BUILD_MKS := $(wildcard $(NUCLEI_SDK_ROOT)/SoC/*/build.mk)
NUCLEI_SOC_COUNT := $(words $(NUCLEI_SOC_VALID_BUILD_MKS))
NUCLEI_SOC_VALID := $(notdir $(patsubst %/,%,$(dir $(NUCLEI_SOC_VALID_BUILD_MKS))))
ifeq ($(NUCLEI_SOC_COUNT),1)
$(warning No SoC named $(SOC) located $(realpath $(NUCLEI_SDK_ROOT)/SoC), force SOC=$(NUCLEI_SOC_VALID) now)
override SOC := $(NUCLEI_SOC_VALID)
else
$(error There is no SoC named $(SOC) located in $(realpath $(NUCLEI_SDK_ROOT)/SoC), please pass correct SOC variable)
endif
endif

SOC_BUILD_MAKEFILE := $(NUCLEI_SDK_ROOT)/SoC/$(SOC)/build.mk

ifneq ($(wildcard $(SOC_BUILD_MAKEFILE)),)
MAKEFILE_PREREQS += $(SOC_BUILD_MAKEFILE)
include $(SOC_BUILD_MAKEFILE)
else
$(error build.mk might not exist in $(realpath $(NUCLEI_SDK_ROOT)/SoC/$(SOC)))
endif
