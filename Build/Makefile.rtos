ifneq ($(RTOS),)
ifneq ($(wildcard $(NUCLEI_SDK_RTOS)),)
MAKEFILE_PREREQS += $(NUCLEI_SDK_BUILD)/Makefile.rtos

RTOS_BUILD_MAKEFILE := $(NUCLEI_SDK_ROOT)/OS/$(RTOS)/build.mk

# build.mk is necessary for RTOS
ifneq ($(wildcard $(RTOS_BUILD_MAKEFILE)),)
MAKEFILE_PREREQS += $(RTOS_BUILD_MAKEFILE)
include $(RTOS_BUILD_MAKEFILE)
else
$(error build.mk might not exist in $(realpath $(NUCLEI_SDK_ROOT)/OS/$(RTOS)))
endif

# Define RTOS_$(RTOS) to show usage of RTOS, such as RTOS_FREERTOS
RTOS_UPPER := $(call uc, $(RTOS))
COMMON_FLAGS += -DRTOS_$(RTOS_UPPER)
endif
endif
