#!/usr/bin/env python3
import torch
import numpy as np
from safetensors.torch import save_file
import re

def parse_weight_line(line):
    """Parse a line from weight_name.log to extract name, shape, and dtype."""
    # Pattern: model.layers.0.input_layernorm.weight : (2304,) : torch.bfloat16
    match = re.match(r'^(.+?)\s*:\s*\((.*?)\)\s*:\s*(.+)$', line)
    if match:
        name = match.group(1).strip()
        shape_str = match.group(2).strip()
        dtype_str = match.group(3).strip()
        
        # Parse shape
        if shape_str:
            shape = tuple(int(x.strip()) for x in shape_str.split(',') if x.strip())
        else:
            shape = ()
            
        return name, shape, dtype_str
    return None, None, None

def get_torch_dtype(dtype_str):
    """Convert string dtype to torch dtype."""
    dtype_map = {
        'torch.bfloat16': torch.bfloat16,
        'torch.float16': torch.float16,
        'torch.float32': torch.float32,
        'torch.int32': torch.int32,
        'torch.int64': torch.int64,
        'torch.int8': torch.int8,
        'torch.uint8': torch.uint8,
    }
    return dtype_map.get(dtype_str, torch.float32)

def generate_random_tensor(shape, dtype):
    """Generate a random tensor with given shape and dtype."""
    if dtype in [torch.int32, torch.int64, torch.int8, torch.uint8]:
        # For integer types, generate random integers
        if dtype == torch.int32:
            # For qweight/qzeros, use appropriate ranges
            tensor = torch.randint(-2147483648, 2147483647, shape, dtype=dtype)
        elif dtype == torch.int8:
            tensor = torch.randint(-128, 127, shape, dtype=dtype)
        elif dtype == torch.uint8:
            tensor = torch.randint(0, 255, shape, dtype=dtype)
        else:  # int64
            tensor = torch.randint(-1000, 1000, shape, dtype=dtype)
    else:
        # For float types, generate random floats
        tensor = torch.randn(shape, dtype=torch.float32)
        if dtype == torch.bfloat16:
            tensor = tensor.to(torch.bfloat16)
        elif dtype == torch.float16:
            tensor = tensor.to(torch.float16)
    
    return tensor

def main():
    # Read the weight_name.log file
    weights = {}
    
    print("Reading weight_name.log...")
    with open('weight_names.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            name, shape, dtype_str = parse_weight_line(line)
            if name and shape is not None and dtype_str:
                dtype = get_torch_dtype(dtype_str)
                print(f"Generating random tensor for {name} with shape {shape} and dtype {dtype}")
                
                # Generate random tensor
                tensor = generate_random_tensor(shape, dtype)
                weights[name] = tensor
    
    print(f"\nTotal weights generated: {len(weights)}")
    
    # Save to safetensors
    print("\nSaving to random.safetensors...")
    save_file(weights, "random.safetensors")
    print("Done! Saved to random.safetensors")
    
    # Print summary
    total_params = sum(np.prod(tensor.shape) for tensor in weights.values())
    print(f"\nTotal parameters: {total_params:,}")

if __name__ == "__main__":
    main()