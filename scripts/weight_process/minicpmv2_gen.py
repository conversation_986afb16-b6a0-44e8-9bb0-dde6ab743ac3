from enum import Enum
from typing import NamedTuple
from typing import List, Any, Dict, Callable, Tuple
from dataclasses import dataclass
import torch
import json

# hardware info
NUM_NODES = 16
WORD_BITS = 256 # bit, 256bit-word
WORD_LEN = WORD_BITS // 8 # byte, 256bit-word
TOTAL_WORDS = 128 * 1024 * 1024 / WORD_LEN

# algorithm
DRAM_BASE = 0x80_0000 # 0x1000_0000 byte
EMBEDDING_DIM = 36 * 64 # = 2304
PROMPT_LEN = 720
DECODE_LEN = 150
CONTEXT_LEN = PROMPT_LEN + DECODE_LEN
NUM_HEAD = 36
HEAD_DIM = EMBEDDING_DIM // NUM_HEAD
NUM_LAYERS = 40

WEIGHT_WIDTH = 4

VOCAB_SIZE = 122753  
FFN_INTERMEDIATE_SIZE = 5760  

def get_word_num(elem_cnt, width=4): # how many 256bit-word
    return (elem_cnt * width + 256 - 1) // 256

#  mapping strategy (https://nanocorechip.feishu.cn/wiki/MdcHwR6XOiiIkHkVg23chU1bnWh?from=from_copylink)
## attn
def attn_head_alloc(core_group, layer_id) -> int:
    """
    head allocation for each core
    """
    layer_group = layer_id // 10
    if layer_group == core_group:
        return 3
    else:
        return 2

def attn_head_start(core_id, layer_id) -> int:
    # integration of attn head alloc in one layer
    core_group = core_id // 4
    layer_group = layer_id // 10
    if core_group < layer_group:
        return 2 * core_id
    elif core_group == layer_group:
        return 2 * (core_group * 4) + (core_id % 4) * 3
    else:
        return 2 * (core_group * 4) + 4 + (core_id % 4) * 2

def attn_oproj_quant_startidx(core_id, layer_id) -> int:
    # first line of quant params in this core
    head_start = attn_head_start(core_id, layer_id)
    return head_start * (EMBEDDING_DIM // NUM_HEAD) // QUANT_GROUP

    
## ffn
def ffn_col_alloc(core_group, layer_id) -> int:
    # All cores allocate 384 columns for consistency
    # Even though last 3 cores only use 256 columns
    return 384

## lm_head
def lm_head_alloc(core_group) -> int:
    return (120*64) # zero fill to 122880 = 120*16*64



# quant stratgy

## group quant
QUANT_GROUP = 128

def quant_weight_size_4b(dim1, dim0):
    # Number of groups (ceiling division)
    num_groups = (dim1 + QUANT_GROUP - 1) // QUANT_GROUP
    
    # Quantized weights: dim1 * dim0 * 0.5 bytes (INT4)
    quantized_weights_size = dim1 * get_word_num(dim0, 4)
    
    # Scale weights: num_groups * dim0 * 2 bytes (BF16)
    scale_weights_size = num_groups * get_word_num(dim0, 16)
    
    # Zero point weights: num_groups * dim0 * 0.5 bytes (INT4)
    zero_point_weights_size = num_groups * get_word_num(dim0, 4)
    
    return int(scale_weights_size), int(zero_point_weights_size)



###########################################################################
def min_strides(shape, width):
    (dim2, dim1, dim0) = shape
    dim0a = WORD_BITS // width
    dim0b =(dim0 + dim0a - 1) // dim0a
    stride0b = 1
    stride1 = dim0b
    stride2 = stride1 * dim1
    return (stride2, stride1, stride0b)

def dtype_convert(dtype="INT4"):
    _map = {
        "INT4"      :   (4, torch.int8      ), # use int8 for calculation
        "INT8"      :   (8, torch.int8      ),
        "INT16"     :   (16,torch.int16     ),
        "INT32"     :   (32,torch.int32     ),
        "FP16"      :   (16,torch.float16   ),
        "FP32"      :   (32,torch.float32   ),
        "BF16"      :   (16,torch.bfloat16  )
    }

    if dtype not in _map:
        raise ValueError(f"Unsupported dtype: {dtype}")
    return _map[dtype]

def create_min_tensor(name: str, group_id: int, core_id: int,  address: int, shape: tuple, dtype: str) -> Dict[str, Any]:
    width, _ = dtype_convert(dtype)
    (dim2, dim1, dim0) = shape
    stride2, stride1, stride0b = min_strides(shape, width)
    template =  {
        "name": f"{name}_{group_id}_{core_id}",
        "address": f"0x{address:08x}",
        "datatype": dtype,
        "dimension": {
            "dim0": dim0,
            "dim1": dim1,
            "dim2": dim2
        },
        "stride": {
            "stride0b": stride0b,
            "stride1": stride1,
            "stride2": stride2
        }
    }
    return template


###########################################################################

# Tensor metadata structure
@dataclass
class TensorMeta:
    """Metadata for a tensor slice"""
    name_template: str  # Template for tensor name, e.g. "layer{}.input.norm"
    dtype: str
    shape_func: Callable  # Function that takes (core_id, layer_id) and returns shape tuple
    size_func: Callable  # Function that calculates size in words
    
    def __dict__(self) -> Dict[str, Any]:
        """Convert TensorMeta to a JSON-serializable dictionary"""
        return {
            "name_template": self.name_template,
            "dtype": self.dtype,
            "shape_func": self.shape_func.__name__ if self.shape_func else None,
            "size_func": self.size_func.__name__ if self.size_func else None,
        }


# Define centralized tensor metadata table
def create_tensor_metadata_table():
    """Create centralized table of all tensor metadata"""
    
    # Shape functions
    def norm_shape(core_id, layer_id):
        return (1, 1, EMBEDDING_DIM)
    
    def kvcache_shape(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        return (CONTEXT_LEN, head_count, EMBEDDING_DIM // NUM_HEAD)
    
    def qkv_proj_shape(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        return (1, head_count * EMBEDDING_DIM, EMBEDDING_DIM // NUM_HEAD)
    
    def o_proj_shape(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        return (1, head_count * (EMBEDDING_DIM // NUM_HEAD), EMBEDDING_DIM)
    
    def qkv_scale_shape(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        q_num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        return (1, head_count * q_num_groups, EMBEDDING_DIM // NUM_HEAD)
    
    def o_scale_shape(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        o_num_groups = (head_count * (EMBEDDING_DIM // NUM_HEAD) + QUANT_GROUP - 1) // QUANT_GROUP
        return (1, o_num_groups, EMBEDDING_DIM)
    
    def gu_proj_shape(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        return (1, EMBEDDING_DIM, num_cols * 2)
    
    def d_proj_shape(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        return (1, num_cols, EMBEDDING_DIM)
    
    def gu_scale_shape(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        gu_num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        return (1, gu_num_groups, num_cols * 2)
    
    def d_scale_shape(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        d_num_groups = (num_cols + QUANT_GROUP - 1) // QUANT_GROUP
        return (1, d_num_groups, EMBEDDING_DIM)
    
    def lmhead_shape(core_id, layer_id=None):
        lm_vocab_size = lm_head_alloc(core_id // 4)
        return (1, EMBEDDING_DIM, lm_vocab_size)
    
    def lmhead_scale_shape(core_id, layer_id=None):
        lm_vocab_size = lm_head_alloc(core_id // 4)
        lm_num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        return (1, lm_num_groups, lm_vocab_size)
    
    def rope_shape(core_id, layer_id=None):
        return (CONTEXT_LEN, 1, EMBEDDING_DIM // NUM_HEAD)
    
    # Size calculation functions
    def norm_size(core_id, layer_id):
        return get_word_num(EMBEDDING_DIM, 16)
    
    def kvcache_size(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        return CONTEXT_LEN * get_word_num(EMBEDDING_DIM // NUM_HEAD, 16) * head_count
    
    def attn_proj_size(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        return EMBEDDING_DIM * get_word_num(EMBEDDING_DIM // NUM_HEAD, WEIGHT_WIDTH) * head_count
    
    def attn_o_proj_size(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        return (EMBEDDING_DIM // NUM_HEAD) * get_word_num(EMBEDDING_DIM, WEIGHT_WIDTH) * head_count
    
    def attn_scale_size(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        scale_size, _ = quant_weight_size_4b(EMBEDDING_DIM, EMBEDDING_DIM // NUM_HEAD)
        return scale_size * head_count
    
    def attn_zero_size(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        _, zero_size = quant_weight_size_4b(EMBEDDING_DIM, EMBEDDING_DIM // NUM_HEAD)
        return zero_size * head_count
    
    def attn_o_scale_size(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        scale_size, _ = quant_weight_size_4b(head_count * (EMBEDDING_DIM // NUM_HEAD), EMBEDDING_DIM)
        return scale_size
    
    def attn_o_zero_size(core_id, layer_id):
        head_count = attn_head_alloc(core_id // 4, layer_id)
        _, zero_size = quant_weight_size_4b(head_count * (EMBEDDING_DIM // NUM_HEAD), EMBEDDING_DIM)
        return zero_size
    
    def gu_proj_size(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        return EMBEDDING_DIM * get_word_num(num_cols * 2, WEIGHT_WIDTH)
    
    def d_proj_size(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        return num_cols * get_word_num(EMBEDDING_DIM, WEIGHT_WIDTH)
    
    def gu_scale_size(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        scale_size, _ = quant_weight_size_4b(EMBEDDING_DIM, num_cols * 2)
        return scale_size
    
    def gu_zero_size(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        _, zero_size = quant_weight_size_4b(EMBEDDING_DIM, num_cols * 2)
        return zero_size
    
    def d_scale_size(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        scale_size, _ = quant_weight_size_4b(num_cols, EMBEDDING_DIM)
        return scale_size
    
    def d_zero_size(core_id, layer_id):
        num_cols = ffn_col_alloc(core_id // 4, layer_id)
        _, zero_size = quant_weight_size_4b(num_cols, EMBEDDING_DIM)
        return zero_size
    
    def lmhead_size(core_id, layer_id=None):
        lm_vocab_size = lm_head_alloc(core_id // 4)
        return EMBEDDING_DIM * get_word_num(lm_vocab_size, WEIGHT_WIDTH)
    
    def lmhead_scale_size(core_id, layer_id=None):
        lm_vocab_size = lm_head_alloc(core_id // 4)
        scale_size, _ = quant_weight_size_4b(EMBEDDING_DIM, lm_vocab_size)
        return scale_size
    
    def lmhead_zero_size(core_id, layer_id=None):
        lm_vocab_size = lm_head_alloc(core_id // 4)
        _, zero_size = quant_weight_size_4b(EMBEDDING_DIM, lm_vocab_size)
        return zero_size
    
    def rope_size(core_id, layer_id=None):
        return CONTEXT_LEN * get_word_num(EMBEDDING_DIM // NUM_HEAD, 16)
    
    metadata_table = {
        # Layer-dependent tensors
        "input_norm"         : TensorMeta("layer{}.inputnorm"          , "BF16", norm_shape         , norm_size         ),
        "post_attn_norm"     : TensorMeta("layer{}.postattnnorm"       , "BF16", norm_shape         , norm_size         ),
        
        # KV Cache
        "kcache"             : TensorMeta("layer{}.kcache"             , "BF16", kvcache_shape      , kvcache_size      ),
        "vcache"             : TensorMeta("layer{}.vcache"             , "BF16", kvcache_shape      , kvcache_size      ),
        
        # Attention weights
        "attn_q_proj"        : TensorMeta("layer{}.attn.qproj.weight"  , "INT4", qkv_proj_shape     , attn_proj_size    ),
        "attn_k_proj"        : TensorMeta("layer{}.attn.kproj.weight"  , "INT4", qkv_proj_shape     , attn_proj_size    ),
        "attn_v_proj"        : TensorMeta("layer{}.attn.vproj.weight"  , "INT4", qkv_proj_shape     , attn_proj_size    ),
        "attn_o_proj"        : TensorMeta("layer{}.attn.oproj.weight"  , "INT4", o_proj_shape       , attn_o_proj_size  ),
        
        # Attention quantization
        "attn_q_proj_qscale" : TensorMeta("layer{}.attn.qproj.scale"   , "BF16", qkv_scale_shape    , attn_scale_size   ),
        "attn_q_proj_qzero"  : TensorMeta("layer{}.attn.qproj.zero"    , "INT4", qkv_scale_shape    , attn_zero_size    ),
        "attn_k_proj_qscale" : TensorMeta("layer{}.attn.kproj.scale"   , "BF16", qkv_scale_shape    , attn_scale_size   ),
        "attn_k_proj_qzero"  : TensorMeta("layer{}.attn.kproj.zero"    , "INT4", qkv_scale_shape    , attn_zero_size    ),
        "attn_v_proj_qscale" : TensorMeta("layer{}.attn.vproj.scale"   , "BF16", qkv_scale_shape    , attn_scale_size   ),
        "attn_v_proj_qzero"  : TensorMeta("layer{}.attn.vproj.zero"    , "INT4", qkv_scale_shape    , attn_zero_size    ),
        "attn_o_proj_qscale" : TensorMeta("layer{}.attn.oproj.scale"   , "BF16", o_scale_shape      , attn_o_scale_size ),
        "attn_o_proj_qzero"  : TensorMeta("layer{}.attn.oproj.zero"    , "INT4", o_scale_shape      , attn_o_zero_size  ),
        
        # MLP weights
        "gu_proj"            : TensorMeta("layer{}.mlp.guproj.weight"  , "INT4", gu_proj_shape      , gu_proj_size      ),  # Combined gate+up
        "d_proj"             : TensorMeta("layer{}.mlp.dproj.weight"   , "INT4", d_proj_shape       , d_proj_size       ),
        
        # MLP quantization
        "gu_proj_qscale"     : TensorMeta("layer{}.mlp.guproj.scale"   , "BF16", gu_scale_shape     , gu_scale_size     ),
        "gu_proj_qzero"      : TensorMeta("layer{}.mlp.guproj.zero"    , "INT4", gu_scale_shape     , gu_zero_size      ),
        "d_proj_qscale"      : TensorMeta("layer{}.mlp.dproj.scale"    , "BF16", d_scale_shape      , d_scale_size      ),
        "d_proj_qzero"       : TensorMeta("layer{}.mlp.dproj.zero"     , "INT4", d_scale_shape      , d_zero_size       ),
        
        # Global tensors
        "lm_head_norm"       : TensorMeta("lmhead.norm"                , "BF16", norm_shape         , norm_size         ),
        "lm_head"            : TensorMeta("lmhead.weight"              , "INT4", lmhead_shape       , lmhead_size       ),
        "lm_head_scale"      : TensorMeta("lmhead.scale"               , "BF16", lmhead_scale_shape , lmhead_scale_size ),
        "lm_head_zero"       : TensorMeta("lmhead.zero"                , "INT4", lmhead_scale_shape , lmhead_zero_size  ),
        "rope_sin"           : TensorMeta("rope.sin"                   , "BF16", rope_shape         , rope_size         ),
        "rope_cos"           : TensorMeta("rope.cos"                   , "BF16", rope_shape         , rope_size         ),
    }
    
    return metadata_table

class WeightPacker:
    def __init__(self, weight_file: str):
        ''' Open safetensor files'''
        self.metadata_table = create_tensor_metadata_table()
        from safetensors.torch import load_file
        self.weight_data = load_file(weight_file)

    def _get_weight_elem(self, weight_name: str) -> torch.Tensor:
        if weight_name not in self.weight_data:
            raise ValueError(f"Weight not found: {weight_name}")
        return self.weight_data[weight_name]
    
    def _unpack_int4_weights(self, packed_weight: torch.Tensor) -> torch.Tensor:
        """Unpack INT4 weights from INT32 format - Optimized byte-view version
        
        Args:
            packed_weight: Tensor of shape (rows, cols) with dtype int32
                          Each int32 contains 8 int4 values
        
        Returns:
            Tensor of shape (rows, cols*8) with dtype int8
        """
        rows, packed_cols = packed_weight.shape
        device = packed_weight.device
        
        # View the int32 data as uint8 bytes for efficient nibble extraction
        # Each int32 becomes 4 uint8 bytes
        packed_bytes = packed_weight.view(torch.uint8).reshape(rows, packed_cols, 4)
        
        # Pre-allocate output tensor
        unpacked = torch.empty((rows, packed_cols, 8), dtype=torch.int8, device=device)
        
        # Extract low and high nibbles from each byte
        # Low nibbles (bits 0-3) go to even indices
        unpacked[:, :, 0::2] = (packed_bytes & 0x0F).to(torch.int8)
        # High nibbles (bits 4-7) go to odd indices
        unpacked[:, :, 1::2] = ((packed_bytes >> 4) & 0x0F).to(torch.int8)
        
        # Convert unsigned 4-bit values to signed (-8 to 7)
        # Values > 7 represent negative numbers in two's complement
        mask = unpacked > 7
        unpacked[mask] -= 16
        
        # Reshape to final shape (rows, packed_cols * 8)
        return unpacked.reshape(rows, packed_cols * 8)

    # Layer normalization weights
    def input_norm(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get input normalization weights for all cores at once"""
        weight_name = f"model.layers.{layer_id}.input_layernorm.weight"
        weight = self._get_weight_elem(weight_name)
        
        assert weight.shape == (EMBEDDING_DIM,)
        assert weight.dtype == torch.bfloat16
        
        # Reshape to 3D: (1, 1, EMBEDDING_DIM)
        weight_3d = weight.reshape(1, 1, EMBEDDING_DIM)
        
        # Same weight for all cores
        return {core_id: weight_3d for core_id in range(NUM_NODES)}
    
    def post_attn_norm(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get post-attention normalization weights for all cores at once"""
        weight_name = f"model.layers.{layer_id}.post_attention_layernorm.weight"
        weight = self._get_weight_elem(weight_name)
        
        assert weight.shape == (EMBEDDING_DIM,)
        assert weight.dtype == torch.bfloat16
        
        # Reshape to 3D: (1, 1, EMBEDDING_DIM)
        weight_3d = weight.reshape(1, 1, EMBEDDING_DIM)
        
        # Same weight for all cores
        return {core_id: weight_3d for core_id in range(NUM_NODES)}

    # Attention projection weights
    def attn_q_proj(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get Q projection weights for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.q_proj.qweight"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        assert weight.shape == (EMBEDDING_DIM, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 weights once
        unpacked_weight = self._unpack_int4_weights(weight)
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        weight_heads = unpacked_weight.reshape(EMBEDDING_DIM, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_weights = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract heads for this core
            core_weight = weight_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_weight = core_weight.permute(1, 0, 2).reshape(num_heads * EMBEDDING_DIM, head_dim)
            
            # Add dim2 dimension
            core_weights[core_id] = core_weight.unsqueeze(0)
        
        return core_weights
    
    def attn_k_proj(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get K projection weights for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.k_proj.qweight"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        assert weight.shape == (EMBEDDING_DIM, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 weights once
        unpacked_weight = self._unpack_int4_weights(weight)
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        weight_heads = unpacked_weight.reshape(EMBEDDING_DIM, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_weights = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract heads for this core
            core_weight = weight_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_weight = core_weight.permute(1, 0, 2).reshape(num_heads * EMBEDDING_DIM, head_dim)
            
            # Add dim2 dimension
            core_weights[core_id] = core_weight.unsqueeze(0)
        
        return core_weights
    
    def attn_v_proj(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get V projection weights for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.v_proj.qweight"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        assert weight.shape == (EMBEDDING_DIM, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 weights once
        unpacked_weight = self._unpack_int4_weights(weight)
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        weight_heads = unpacked_weight.reshape(EMBEDDING_DIM, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_weights = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract heads for this core
            core_weight = weight_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_weight = core_weight.permute(1, 0, 2).reshape(num_heads * EMBEDDING_DIM, head_dim)
            
            # Add dim2 dimension
            core_weights[core_id] = core_weight.unsqueeze(0)
        
        return core_weights
    
    def attn_o_proj(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get O projection weights for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.o_proj.qweight"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        assert weight.shape == (EMBEDDING_DIM, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 weights once
        unpacked_weight = self._unpack_int4_weights(weight)
        
        # Process all cores at once
        core_weights = {}
        head_dim = EMBEDDING_DIM // NUM_HEAD
        
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # For o_proj, we use row splitting
            row_start = core_head_start * head_dim
            row_end = row_start + num_heads * head_dim
            
            # Extract rows for this core
            core_weight = unpacked_weight[row_start:row_end, :].clone()
            
            # Add dim2 dimension
            core_weights[core_id] = core_weight.unsqueeze(0)
        
        return core_weights
    
    # Attention quantization parameters
    def attn_q_proj_qscale(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get Q projection quantization scales for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.q_proj.scales"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM)
        assert weight.dtype == torch.bfloat16
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        scale_heads = weight.reshape(num_groups, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_scales = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract scales for this core's heads
            core_scale = scale_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_scale = core_scale.permute(1, 0, 2).reshape(num_heads * num_groups, head_dim)
            
            # Add dim2 dimension
            core_scales[core_id] = core_scale.unsqueeze(0)
        
        return core_scales
    
    def attn_q_proj_qzero(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get Q projection quantization zeros for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.q_proj.qzeros"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 zeros once
        unpacked_zeros = self._unpack_int4_weights(weight)
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        zero_heads = unpacked_zeros.reshape(num_groups, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_zeros = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract zeros for this core's heads
            core_zero = zero_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_zero = core_zero.permute(1, 0, 2).reshape(num_heads * num_groups, head_dim)
            
            # Add dim2 dimension
            core_zeros[core_id] = core_zero.unsqueeze(0)
        
        return core_zeros
    
    def attn_k_proj_qscale(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get K projection quantization scales for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.k_proj.scales"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM)
        assert weight.dtype == torch.bfloat16
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        scale_heads = weight.reshape(num_groups, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_scales = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract scales for this core's heads
            core_scale = scale_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_scale = core_scale.permute(1, 0, 2).reshape(num_heads * num_groups, head_dim)
            
            # Add dim2 dimension
            core_scales[core_id] = core_scale.unsqueeze(0)
        
        return core_scales
    
    def attn_k_proj_qzero(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get K projection quantization zeros for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.k_proj.qzeros"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 zeros once
        unpacked_zeros = self._unpack_int4_weights(weight)
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        zero_heads = unpacked_zeros.reshape(num_groups, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_zeros = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract zeros for this core's heads
            core_zero = zero_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_zero = core_zero.permute(1, 0, 2).reshape(num_heads * num_groups, head_dim)
            
            # Add dim2 dimension
            core_zeros[core_id] = core_zero.unsqueeze(0)
        
        return core_zeros
    
    def attn_v_proj_qscale(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get V projection quantization scales for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.v_proj.scales"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM)
        assert weight.dtype == torch.bfloat16
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        scale_heads = weight.reshape(num_groups, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_scales = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract scales for this core's heads
            core_scale = scale_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_scale = core_scale.permute(1, 0, 2).reshape(num_heads * num_groups, head_dim)
            
            # Add dim2 dimension
            core_scales[core_id] = core_scale.unsqueeze(0)
        
        return core_scales
    
    def attn_v_proj_qzero(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get V projection quantization zeros for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.v_proj.qzeros"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 zeros once
        unpacked_zeros = self._unpack_int4_weights(weight)
        
        # Reshape to separate heads
        head_dim = EMBEDDING_DIM // NUM_HEAD
        zero_heads = unpacked_zeros.reshape(num_groups, NUM_HEAD, head_dim)
        
        # Process all cores at once
        core_zeros = {}
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            core_head_start = attn_head_start(core_id, layer_id)
            
            # Extract zeros for this core's heads
            core_zero = zero_heads[:, core_head_start:core_head_start + num_heads, :].clone()
            
            # Permute and reshape
            core_zero = core_zero.permute(1, 0, 2).reshape(num_heads * num_groups, head_dim)
            
            # Add dim2 dimension
            core_zeros[core_id] = core_zero.unsqueeze(0)
        
        return core_zeros
    
    def attn_o_proj_qscale(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get O projection quantization scales for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.o_proj.scales"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM)
        assert weight.dtype == torch.bfloat16
        
        # Process all cores at once
        core_scales = {}
        head_dim = EMBEDDING_DIM // NUM_HEAD
        
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            
            # Calculate which quantization groups this core needs
            quant_start_idx = attn_oproj_quant_startidx(core_id, layer_id)
            num_core_groups = (num_heads * head_dim + QUANT_GROUP - 1) // QUANT_GROUP
            
            # Extract the quantization groups for this core
            core_scale = weight[quant_start_idx:quant_start_idx + num_core_groups, :].clone()
            
            # Add dim2 dimension
            core_scales[core_id] = core_scale.unsqueeze(0)
        
        return core_scales
    
    def attn_o_proj_qzero(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get O projection quantization zeros for all cores at once"""
        weight_name = f"model.layers.{layer_id}.self_attn.o_proj.qzeros"
        weight = self._get_weight_elem(weight_name)
        
        # Validation
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 zeros once
        unpacked_zeros = self._unpack_int4_weights(weight)
        
        # Process all cores at once
        core_zeros = {}
        head_dim = EMBEDDING_DIM // NUM_HEAD
        
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            
            # Calculate which quantization groups this core needs
            quant_start_idx = attn_oproj_quant_startidx(core_id, layer_id)
            num_core_groups = (num_heads * head_dim + QUANT_GROUP - 1) // QUANT_GROUP
            
            # Extract the quantization groups for this core
            core_zero = unpacked_zeros[quant_start_idx:quant_start_idx + num_core_groups, :].clone()
            
            # Add dim2 dimension
            core_zeros[core_id] = core_zero.unsqueeze(0)
        
        return core_zeros
    
    # MLP weights
    def gu_proj(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get GU projection weights for all cores at once"""
        # Gate and Up projections are concatenated in column direction
        gate_weight_name = f"model.layers.{layer_id}.mlp.gate_proj.qweight"
        up_weight_name = f"model.layers.{layer_id}.mlp.up_proj.qweight"
        
        gate_weight = self._get_weight_elem(gate_weight_name)
        up_weight = self._get_weight_elem(up_weight_name)
        
        # INT4 weights are packed as INT32
        # Original shape (2304, 5760) INT4 -> packed shape (2304, 720) INT32
        assert gate_weight.shape == (EMBEDDING_DIM, FFN_INTERMEDIATE_SIZE // 8)
        assert up_weight.shape == (EMBEDDING_DIM, FFN_INTERMEDIATE_SIZE // 8)
        assert gate_weight.dtype == torch.int32
        assert up_weight.dtype == torch.int32
        
        # Unpack INT4 weights once
        unpacked_gate = self._unpack_int4_weights(gate_weight)
        unpacked_up = self._unpack_int4_weights(up_weight)
        
        # Process all cores at once
        core_weights = {}
        for core_id in range(NUM_NODES):
            # Column distribution:
            # - First 13 cores: 384 columns each (13 * 384 = 4992)
            # - Last 3 cores: 256 columns each (3 * 256 = 768)
            # - Total: 4992 + 768 = 5760 (FFN_INTERMEDIATE_SIZE)
            if core_id < 13:
                col_start = core_id * 384
                col_end = col_start + 384
            else:
                # Last 3 cores
                col_start = 13 * 384 + (core_id - 13) * 256
                col_end = col_start + 256
            
            # Extract columns for this core
            core_gate = unpacked_gate[:, col_start:col_end].clone()
            core_up = unpacked_up[:, col_start:col_end].clone()
            
            # Pad to 384 columns if necessary (for last 3 cores)
            actual_cols = col_end - col_start
            if actual_cols < 384:
                pad_width = 384 - actual_cols
                core_gate = torch.nn.functional.pad(core_gate, (0, pad_width), value=0)
                core_up = torch.nn.functional.pad(core_up, (0, pad_width), value=0)
            
            # Concatenate gate and up in column direction
            core_weight = torch.cat([core_gate, core_up], dim=1)
            
            # Add dim2 dimension: (1, EMBEDDING_DIM, 384 * 2)
            core_weights[core_id] = core_weight.unsqueeze(0)
        
        return core_weights
    
    def d_proj(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get D projection weights for all cores at once"""
        weight_name = f"model.layers.{layer_id}.mlp.down_proj.qweight"
        weight = self._get_weight_elem(weight_name)

        # INT4 weights are packed as INT32
        # Original shape (5760, 2304) INT4 -> packed shape (5760, 288) INT32
        assert weight.shape == (FFN_INTERMEDIATE_SIZE, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32

        # Unpack INT4 weights once
        unpacked_weight = self._unpack_int4_weights(weight)

        # Process all cores at once
        core_weights = {}
        for core_id in range(NUM_NODES):
            # Row distribution (same pattern as column distribution in gu_proj):
            # - First 13 cores: 384 rows each
            # - Last 3 cores: 256 rows each (need padding to 384)
            if core_id < 13:
                row_start = core_id * 384
                row_end = row_start + 384
            else:
                # Last 3 cores
                row_start = 13 * 384 + (core_id - 13) * 256
                row_end = row_start + 256

            # Extract rows for this core
            core_weight = unpacked_weight[row_start:row_end, :].clone()

            # Pad to 384 rows if necessary (for last 3 cores)
            actual_rows = row_end - row_start
            if actual_rows < 384:
                pad_height = 384 - actual_rows
                core_weight = torch.nn.functional.pad(core_weight, (0, 0, 0, pad_height), value=0)

            # Shape: (1, 384, EMBEDDING_DIM)
            core_weights[core_id] = core_weight.unsqueeze(0)

        return core_weights

    
    # MLP quantization parameters
    def gu_proj_qscale(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get GU projection quantization scales for all cores at once"""
        gate_scale_name = f"model.layers.{layer_id}.mlp.gate_proj.scales"
        up_scale_name = f"model.layers.{layer_id}.mlp.up_proj.scales"
        
        gate_scale = self._get_weight_elem(gate_scale_name)
        up_scale = self._get_weight_elem(up_scale_name)
        
        # Scales shape: (18, 5760) - 18 groups for EMBEDDING_DIM=2304 with QUANT_GROUP=128
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert gate_scale.shape == (num_groups, FFN_INTERMEDIATE_SIZE)
        assert up_scale.shape == (num_groups, FFN_INTERMEDIATE_SIZE)
        assert gate_scale.dtype == torch.bfloat16
        assert up_scale.dtype == torch.bfloat16
        
        # Process all cores at once
        core_scales = {}
        for core_id in range(NUM_NODES):
            # Same column distribution as weights
            if core_id < 13:
                col_start = core_id * 384
                col_end = col_start + 384
            else:
                # Last 3 cores
                col_start = 13 * 384 + (core_id - 13) * 256
                col_end = col_start + 256
            
            # Extract columns for this core
            core_gate_scale = gate_scale[:, col_start:col_end].clone()
            core_up_scale = up_scale[:, col_start:col_end].clone()
            
            # Pad to 384 columns if necessary
            actual_cols = col_end - col_start
            if actual_cols < 384:
                pad_width = 384 - actual_cols
                core_gate_scale = torch.nn.functional.pad(core_gate_scale, (0, pad_width), value=0)
                core_up_scale = torch.nn.functional.pad(core_up_scale, (0, pad_width), value=0)
            
            # Concatenate gate and up scales
            core_scale = torch.cat([core_gate_scale, core_up_scale], dim=1)
            
            # Add dim2 dimension: (1, num_groups, 384 * 2)
            core_scales[core_id] = core_scale.unsqueeze(0)
        
        return core_scales
    
    def gu_proj_qzero(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get GU projection quantization zeros for all cores at once"""
        gate_zero_name = f"model.layers.{layer_id}.mlp.gate_proj.qzeros"
        up_zero_name = f"model.layers.{layer_id}.mlp.up_proj.qzeros"
        
        gate_zero = self._get_weight_elem(gate_zero_name)
        up_zero = self._get_weight_elem(up_zero_name)
        
        # Zeros are packed as INT32: (18, 720)
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert gate_zero.shape == (num_groups, FFN_INTERMEDIATE_SIZE // 8)
        assert up_zero.shape == (num_groups, FFN_INTERMEDIATE_SIZE // 8)
        assert gate_zero.dtype == torch.int32
        assert up_zero.dtype == torch.int32
        
        # Unpack INT4 zeros once
        unpacked_gate_zero = self._unpack_int4_weights(gate_zero)
        unpacked_up_zero = self._unpack_int4_weights(up_zero)
        
        # Process all cores at once
        core_zeros = {}
        for core_id in range(NUM_NODES):
            # Same column distribution as weights
            if core_id < 13:
                col_start = core_id * 384
                col_end = col_start + 384
            else:
                # Last 3 cores
                col_start = 13 * 384 + (core_id - 13) * 256
                col_end = col_start + 256
            
            # Extract columns for this core
            core_gate_zero = unpacked_gate_zero[:, col_start:col_end].clone()
            core_up_zero = unpacked_up_zero[:, col_start:col_end].clone()
            
            # Pad to 384 columns if necessary
            actual_cols = col_end - col_start
            if actual_cols < 384:
                pad_width = 384 - actual_cols
                core_gate_zero = torch.nn.functional.pad(core_gate_zero, (0, pad_width), value=0)
                core_up_zero = torch.nn.functional.pad(core_up_zero, (0, pad_width), value=0)
            
            # Concatenate gate and up zeros
            core_zero = torch.cat([core_gate_zero, core_up_zero], dim=1)
            
            # Add dim2 dimension: (1, num_groups, 384 * 2)
            core_zeros[core_id] = core_zero.unsqueeze(0)
        
        return core_zeros

    def d_proj_qscale(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get D projection quantization scales for all cores at once"""
        weight_name = f"model.layers.{layer_id}.mlp.down_proj.scales"
        weight = self._get_weight_elem(weight_name)

        # For d_proj, quantization is done row-wise
        # Total rows: 5760, groups: 5760/128 = 45
        total_groups = (FFN_INTERMEDIATE_SIZE + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (total_groups, EMBEDDING_DIM)
        assert weight.dtype == torch.bfloat16

        # Process all cores at once
        core_scales = {}
        for core_id in range(NUM_NODES):
            # Calculate which quantization groups this core needs
            if core_id < 13:
                # First 13 cores: 384 rows = 3 groups (384/128 = 3)
                row_start = core_id * 384
                group_start = row_start // QUANT_GROUP
                num_groups = 3
            else:
                # Last 3 cores: 256 rows = 2 groups (256/128 = 2)
                # Need to pad to 3 groups for consistency
                row_start = 13 * 384 + (core_id - 13) * 256
                group_start = row_start // QUANT_GROUP
                num_groups = 2

            # Extract the quantization groups for this core
            core_scale = weight[group_start:group_start + num_groups, :].clone()

            # Pad to 3 groups if necessary (for last 3 cores)
            if num_groups < 3:
                pad_groups = 3 - num_groups
                # Create zero padding with same shape as one group
                padding = torch.zeros((pad_groups, EMBEDDING_DIM), dtype=torch.bfloat16)
                core_scale = torch.cat([core_scale, padding], dim=0)

            # Add dim2 dimension: (1, 3, EMBEDDING_DIM)
            core_scales[core_id] = core_scale.unsqueeze(0)

        return core_scales

    def d_proj_qzero(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Get D projection quantization zeros for all cores at once"""
        weight_name = f"model.layers.{layer_id}.mlp.down_proj.qzeros"
        weight = self._get_weight_elem(weight_name)

        # Zeros are packed as INT32
        total_groups = (FFN_INTERMEDIATE_SIZE + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (total_groups, EMBEDDING_DIM // 8)
        assert weight.dtype == torch.int32

        # Unpack INT4 zeros once
        unpacked_zeros = self._unpack_int4_weights(weight)

        # Process all cores at once
        core_zeros = {}
        for core_id in range(NUM_NODES):
            # Calculate which quantization groups this core needs
            if core_id < 13:
                # First 13 cores: 384 rows = 3 groups
                row_start = core_id * 384
                group_start = row_start // QUANT_GROUP
                num_groups = 3
            else:
                # Last 3 cores: 256 rows = 2 groups
                # Need to pad to 3 groups for consistency
                row_start = 13 * 384 + (core_id - 13) * 256
                group_start = row_start // QUANT_GROUP
                num_groups = 2

            # Extract the quantization groups for this core
            core_zero = unpacked_zeros[group_start:group_start + num_groups, :].clone()

            # Pad to 3 groups if necessary (for last 3 cores)
            if num_groups < 3:
                pad_groups = 3 - num_groups
                # Create zero padding with same shape as one group
                padding = torch.zeros((pad_groups, EMBEDDING_DIM), dtype=torch.int8)
                core_zero = torch.cat([core_zero, padding], dim=0)

            # Add dim2 dimension: (1, 3, EMBEDDING_DIM)
            core_zeros[core_id] = core_zero.unsqueeze(0)

        return core_zeros
 
    
    # Global tensors (no layer_id)
    def lm_head_norm(self) -> Dict[int, torch.Tensor]:
        """Get LM head normalization weights for all cores at once"""
        weight_name = "model.norm.weight"
        weight = self._get_weight_elem(weight_name)
        
        assert weight.shape == (EMBEDDING_DIM,)
        assert weight.dtype == torch.bfloat16
        
        # Reshape to 3D: (1, 1, EMBEDDING_DIM)
        weight_3d = weight.reshape(1, 1, EMBEDDING_DIM)
        
        # Same weight for all cores
        return {core_id: weight_3d.clone() for core_id in range(NUM_NODES)}
    
    def lm_head(self) -> Dict[int, torch.Tensor]:
        """Get LM head weights for all cores at once"""
        weight_name = "lm_head.qweight"
        weight = self._get_weight_elem(weight_name)
        
        # INT4 weights are packed as INT32
        # Original shape (2304, 122880) INT4 -> packed shape (2304, 15360) INT32
        assert weight.shape == (EMBEDDING_DIM, 15360)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 weights once
        unpacked_weight = self._unpack_int4_weights(weight)
        # After unpacking: (2304, 122880)
        
        # Process all cores at once
        core_weights = {}
        cols_per_core = 7680  # 122880 / 16 = 7680
        
        for core_id in range(NUM_NODES):
            col_start = core_id * cols_per_core
            col_end = col_start + cols_per_core
            
            # Extract columns for this core
            core_weight = unpacked_weight[:, col_start:col_end].clone()
            
            # Add dim2 dimension: (1, EMBEDDING_DIM, cols_per_core)
            core_weights[core_id] = core_weight.unsqueeze(0)
        
        return core_weights
    
    def lm_head_scale(self) -> Dict[int, torch.Tensor]:
        """Get LM head quantization scales for all cores at once"""
        weight_name = "lm_head.scales"
        weight = self._get_weight_elem(weight_name)
        
        # Scales shape: (18, 122880) - 18 groups for EMBEDDING_DIM=2304 with QUANT_GROUP=128
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, 122880)
        assert weight.dtype == torch.bfloat16
        
        # Process all cores at once
        core_scales = {}
        cols_per_core = 7680  # 122880 / 16 = 7680
        
        for core_id in range(NUM_NODES):
            col_start = core_id * cols_per_core
            col_end = col_start + cols_per_core
            
            # Extract columns for this core
            core_scale = weight[:, col_start:col_end].clone()
            
            # Add dim2 dimension: (1, num_groups, cols_per_core)
            core_scales[core_id] = core_scale.unsqueeze(0)
        
        return core_scales
    
    def lm_head_zero(self) -> Dict[int, torch.Tensor]:
        """Get LM head quantization zeros for all cores at once"""
        weight_name = "lm_head.qzeros"
        weight = self._get_weight_elem(weight_name)
        
        # Zeros are packed as INT32: (18, 15360)
        num_groups = (EMBEDDING_DIM + QUANT_GROUP - 1) // QUANT_GROUP
        assert weight.shape == (num_groups, 15360)
        assert weight.dtype == torch.int32
        
        # Unpack INT4 zeros once
        unpacked_zeros = self._unpack_int4_weights(weight)
        # After unpacking: (18, 122880)
        
        # Process all cores at once
        core_zeros = {}
        cols_per_core = 7680  # 122880 / 16 = 7680
        
        for core_id in range(NUM_NODES):
            col_start = core_id * cols_per_core
            col_end = col_start + cols_per_core
            
            # Extract columns for this core
            core_zero = unpacked_zeros[:, col_start:col_end].clone()
            
            # Add dim2 dimension: (1, num_groups, cols_per_core)
            core_zeros[core_id] = core_zero.unsqueeze(0)
        
        return core_zeros
    
    def rope_sin(self) -> Dict[int, torch.Tensor]:
        """Generate RoPE sin embeddings for all cores at once"""
        time_line = torch.arange(0, CONTEXT_LEN, dtype=torch.float32)
        inv_freq = 1.0 / (10000 ** (torch.arange(0, HEAD_DIM, 2).float() / HEAD_DIM))
        freqs = torch.outer(time_line, inv_freq) # [max_seq_len, head_dim // 2]
        emb = torch.cat((freqs, freqs), dim=-1)
        sin = emb.sin().unsqueeze(0).to(torch.bfloat16)  # [1, CONTEXT_LEN, HEAD_DIM]
        
        # Same embeddings for all cores
        return {core_id: sin.clone() for core_id in range(NUM_NODES)}
    
    def rope_cos(self) -> Dict[int, torch.Tensor]:
        """Generate RoPE cos embeddings for all cores at once"""
        time_line = torch.arange(0, CONTEXT_LEN, dtype=torch.float32)
        inv_freq = 1.0 / (10000 ** (torch.arange(0, HEAD_DIM, 2).float() / HEAD_DIM))
        freqs = torch.outer(time_line, inv_freq) # [max_seq_len, head_dim // 2]
        emb = torch.cat((freqs, freqs), dim=-1)
        cos = emb.cos().unsqueeze(0).to(torch.bfloat16)  # [1, CONTEXT_LEN, HEAD_DIM]
        
        # Same embeddings for all cores
        return {core_id: cos.clone() for core_id in range(NUM_NODES)}
    
    # KVCache allocation methods
    def kcache(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Allocate and initialize K cache for all cores at once
        
        K cache stores key vectors for attention mechanism during inference.
        Initialized to zeros with shape based on head allocation per core.
        """
        core_caches = {}
        
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            head_dim = EMBEDDING_DIM // NUM_HEAD
            
            # Shape: (CONTEXT_LEN, num_heads, head_dim)
            cache_shape = (CONTEXT_LEN, num_heads, head_dim)
            
            # Initialize with zeros in bfloat16
            cache_tensor = torch.zeros(cache_shape, dtype=torch.bfloat16)
            
            core_caches[core_id] = cache_tensor
        
        return core_caches
    
    def vcache(self, layer_id: int) -> Dict[int, torch.Tensor]:
        """Allocate and initialize V cache for all cores at once
        
        V cache stores value vectors for attention mechanism during inference.
        Initialized to zeros with shape based on head allocation per core.
        """
        core_caches = {}
        
        for core_id in range(NUM_NODES):
            core_group = core_id // 4
            num_heads = attn_head_alloc(core_group, layer_id)
            head_dim = EMBEDDING_DIM // NUM_HEAD
            
            # Shape: (CONTEXT_LEN, num_heads, head_dim)
            cache_shape = (CONTEXT_LEN, num_heads, head_dim)
            
            # Initialize with zeros in bfloat16
            cache_tensor = torch.zeros(cache_shape, dtype=torch.bfloat16)
            
            core_caches[core_id] = cache_tensor
        
        return core_caches
    
    def pack_weights_for_all_cores(self, output_dir: str = "./weight_slices", include_kvcache: bool = True):
        """Pack all weights for all cores and save as individual .pt files
        
        Uses batch processing to load each weight only once and distribute to all cores.
        Implements multi-threaded async saving for improved performance.
        
        Args:
            output_dir: Directory to save weight slice files
            include_kvcache: Whether to include KVCache initialization (default: True)
        """
        import os
        from concurrent.futures import ThreadPoolExecutor
        import time
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create subdirectories for all cores
        core_dirs = {}
        for core_id in range(NUM_NODES):
            # core_dir = os.path.join(output_dir, f"core_{core_id}")
            core_dir = os.path.join(output_dir, f"./")
            os.makedirs(core_dir, exist_ok=True)
            core_dirs[core_id] = core_dir
        
        print(f"Packing weights for {NUM_NODES} cores using batch processing...")
        start_time = time.time()
        
        # Counter for total files created
        total_files = 0
        
        # Use ThreadPoolExecutor for async saving
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = []
            
            # Process layer-dependent weights
            print("\nProcessing layer-dependent weights...")
            for layer_id in range(NUM_LAYERS):
                # Define weight operations with their metadata keys
                layer_operations = [
                    ("input_norm", self.input_norm),
                    ("post_attn_norm", self.post_attn_norm),
                    ("attn_q_proj", self.attn_q_proj),
                    ("attn_k_proj", self.attn_k_proj),
                    ("attn_v_proj", self.attn_v_proj),
                    ("attn_o_proj", self.attn_o_proj),
                    ("attn_q_proj_qscale", self.attn_q_proj_qscale),
                    ("attn_q_proj_qzero", self.attn_q_proj_qzero),
                    ("attn_k_proj_qscale", self.attn_k_proj_qscale),
                    ("attn_k_proj_qzero", self.attn_k_proj_qzero),
                    ("attn_v_proj_qscale", self.attn_v_proj_qscale),
                    ("attn_v_proj_qzero", self.attn_v_proj_qzero),
                    ("attn_o_proj_qscale", self.attn_o_proj_qscale),
                    ("attn_o_proj_qzero", self.attn_o_proj_qzero),
                    ("gu_proj", self.gu_proj),
                    ("d_proj", self.d_proj),
                    ("gu_proj_qscale", self.gu_proj_qscale),
                    ("gu_proj_qzero", self.gu_proj_qzero),
                    ("d_proj_qscale", self.d_proj_qscale),
                    ("d_proj_qzero", self.d_proj_qzero),
                ]
                
                # Optionally add KVCache operations
                if include_kvcache:
                    layer_operations.extend([
                        ("kcache", self.kcache),
                        ("vcache", self.vcache),
                    ])
                
                for meta_key, method in layer_operations:
                    # Get metadata
                    meta = self.metadata_table[meta_key]
                    
                    # Call method once to get all core weights
                    all_core_weights = method(layer_id)
                    
                    # Save each core's weight asynchronously
                    for core_id, weight_tensor in all_core_weights.items():
                        group_id = core_id // 4
                        core_in_group = core_id % 4
                        
                        # Generate filename from template
                        filename = meta.name_template.format(layer_id) + f"_{group_id}_{core_in_group}.pt"
                        filepath = os.path.join(core_dirs[core_id], filename)
                        
                        # Submit save task to executor
                        future = executor.submit(torch.save, weight_tensor, filepath)
                        futures.append(future)
                        total_files += 1
                
                if (layer_id + 1) % 10 == 0:
                    print(f"  Processed {layer_id + 1}/{NUM_LAYERS} layers...")
            
            # Process global weights (no layer_id)
            print("\nProcessing global weights...")
            global_operations = [
                ("lm_head_norm", self.lm_head_norm),
                ("lm_head", self.lm_head),
                ("lm_head_scale", self.lm_head_scale),
                ("lm_head_zero", self.lm_head_zero),
                ("rope_sin", self.rope_sin),
                ("rope_cos", self.rope_cos),
            ]
            
            for meta_key, method in global_operations:
                # Get metadata
                meta = self.metadata_table[meta_key]
                
                # Call method once to get all core weights
                all_core_weights = method()
                
                # Save each core's weight asynchronously
                for core_id, weight_tensor in all_core_weights.items():
                    group_id = core_id // 4
                    core_in_group = core_id % 4

                    # Generate filename from template (no layer_id formatting)
                    filename = meta.name_template + f"_{group_id}_{core_in_group}.pt"
                    filepath = os.path.join(core_dirs[core_id], filename)
                    
                    # Submit save task to executor
                    future = executor.submit(torch.save, weight_tensor, filepath)
                    futures.append(future)
                    total_files += 1
            
            # Wait for all saves to complete
            print("\nWaiting for all save operations to complete...")
            for i, future in enumerate(futures):
                future.result()  # This will raise any exceptions that occurred
                if (i + 1) % 100 == 0:
                    print(f"  Completed {i + 1}/{len(futures)} save operations...")
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\nWeight packing complete!")
        print(f"Total files created: {total_files}")
        print(f"Weight slices saved to {output_dir}/")
        print(f"Total time: {elapsed_time:.2f} seconds")
        print(f"Average time per file: {elapsed_time / total_files * 1000:.2f} ms")


# result for specific core i
class DRAMAllocator:
    def __init__(self, core_id):
        self.core_id = core_id
        self.base_pointer = DRAM_BASE
        self.top_pointer = self.base_pointer
        self.tensors = []  # Store tensor information
        
        # Get tensor metadata table
        self.metadata_table = create_tensor_metadata_table()
        
        self.input_norm         = [0] * NUM_LAYERS
        self.attn_q_proj        = [0] * NUM_LAYERS
        self.attn_k_proj        = [0] * NUM_LAYERS
        self.attn_v_proj        = [0] * NUM_LAYERS
        self.attn_o_proj        = [0] * NUM_LAYERS
        self.attn_q_proj_qscale = [0] * NUM_LAYERS
        self.attn_k_proj_qscale = [0] * NUM_LAYERS
        self.attn_v_proj_qscale = [0] * NUM_LAYERS
        self.attn_o_proj_qscale = [0] * NUM_LAYERS
        self.attn_q_proj_qzero  = [0] * NUM_LAYERS
        self.attn_k_proj_qzero  = [0] * NUM_LAYERS
        self.attn_v_proj_qzero  = [0] * NUM_LAYERS
        self.attn_o_proj_qzero  = [0] * NUM_LAYERS
        
        self.post_attn_norm     = [0] * NUM_LAYERS
        
        self.gu_proj            = [0] * NUM_LAYERS
        self.d_proj             = [0] * NUM_LAYERS
        self.gu_proj_qscale     = [0] * NUM_LAYERS
        self.d_proj_qscale      = [0] * NUM_LAYERS
        self.gu_proj_qzero      = [0] * NUM_LAYERS
        self.d_proj_qzero       = [0] * NUM_LAYERS
        
        self.rope_sin       = 0
        self.rope_cos       = 0
        
        self.lm_head_norm   = 0
        self.lm_head        = 0
        self.lm_head_scale  = 0
        self.lm_head_zero   = 0
        
        self.kcache         = [0] * NUM_LAYERS
        self.vcache         = [0] * NUM_LAYERS

    def get_used_size(self):
        return (self.top_pointer - self.base_pointer)
    
    def _alloc_tensor(self, tensor_name: str, layer_id: int = None):
        """Generic tensor allocation using metadata table"""
        if tensor_name not in self.metadata_table:
            raise ValueError(f"Unknown tensor: {tensor_name}")
        
        meta = self.metadata_table[tensor_name]
        
        # Get shape and size
        shape = meta.shape_func(self.core_id, layer_id)
        size = meta.size_func(self.core_id, layer_id)
        
        # Store address
        attr = getattr(self, tensor_name)
        if isinstance(attr, list):
            attr[layer_id] = self.top_pointer
        else:
            setattr(self, tensor_name, self.top_pointer)
        
        # Create tensor info
        if layer_id is not None:
            name = meta.name_template.format(layer_id)
        else:
            name = meta.name_template
            
        tensor_info = create_min_tensor(
            name,
            self.core_id // 4,
            self.core_id % 4,
            self.top_pointer * WORD_LEN,
            shape,
            meta.dtype
        )
        self.tensors.append(tensor_info)
        
        # Update pointer
        self.top_pointer += size
    
    def alloc_input_norm(self):
        for i in range(NUM_LAYERS):
            self._alloc_tensor("input_norm", i)
            
    def alloc_post_attn_norm(self):
        for i in range(NUM_LAYERS):
            self._alloc_tensor("post_attn_norm", i)

    def alloc_kvcache(self):
        for layer_id in range(NUM_LAYERS):
            self._alloc_tensor("kcache", layer_id)
            self._alloc_tensor("vcache", layer_id)

    def alloc_attn(self):
        for layer_id in range(NUM_LAYERS):
            self._alloc_tensor("attn_q_proj", layer_id)
            self._alloc_tensor("attn_k_proj", layer_id)
            self._alloc_tensor("attn_v_proj", layer_id)
            self._alloc_tensor("attn_o_proj", layer_id)

    def alloc_attn_quant(self):
        for layer_id in range(NUM_LAYERS):
            self._alloc_tensor("attn_q_proj_qscale", layer_id)
            self._alloc_tensor("attn_q_proj_qzero", layer_id)
            self._alloc_tensor("attn_k_proj_qscale", layer_id)
            self._alloc_tensor("attn_k_proj_qzero", layer_id)
            self._alloc_tensor("attn_v_proj_qscale", layer_id)
            self._alloc_tensor("attn_v_proj_qzero", layer_id)
            self._alloc_tensor("attn_o_proj_qscale", layer_id)
            self._alloc_tensor("attn_o_proj_qzero", layer_id)

    def alloc_ffn(self):
        for layer_id in range(NUM_LAYERS):
            self._alloc_tensor("gu_proj", layer_id)
            self._alloc_tensor("d_proj", layer_id)
    
    def alloc_ffn_quant(self):
        for layer_id in range(NUM_LAYERS):
            self._alloc_tensor("gu_proj_qscale", layer_id)
            self._alloc_tensor("gu_proj_qzero", layer_id)
            self._alloc_tensor("d_proj_qscale", layer_id)
            self._alloc_tensor("d_proj_qzero", layer_id)

    def alloc_lm_head_norm(self):
        self._alloc_tensor("lm_head_norm")

    def alloc_lm_head(self):
        self._alloc_tensor("lm_head")
        self._alloc_tensor("lm_head_scale")
        self._alloc_tensor("lm_head_zero")
    
    def alloc_rope(self):
        self._alloc_tensor("rope_sin")
        self._alloc_tensor("rope_cos")
    
    def alloc(self):
        self.alloc_input_norm()         #    184,320 =  1*2304*2*40
        self.alloc_attn()               # 26,542,080 =  2304*192*0.5 * 4 * 10 + 2304*128*0.5 * 4  * 30
        self.alloc_attn_quant()         #  1,065,600 = 10*(3*18*64*2.5*3 + 2*2304*2.5) + 30*(2*18*64*2.5*3 + 1*2304*2.5)
        self.alloc_post_attn_norm()     #    184,320 =  1*2304*2*40
        self.alloc_ffn()                # 49,766,400 =  3*2304*6*64*0.5  * 10 + 3*2304*5.5*64*0.5 * 30
        self.alloc_ffn_quant()          #  1,987,200 = 10*(18*12*64*2.5 + 3*2304*2.5) + 30*(18*11*64*2.5 + 3*2304*2.5)

        self.alloc_lm_head_norm()       #      4,608 = 1*2304*2
        self.alloc_lm_head()            #  8,847,360 = 2304*120*64*0.5

        self.alloc_rope()               #    111,360 = 870*64*2 

        self.alloc_kvcache()            # 20,044,800 = 10*3* 2*(870*64*2) + 30 * 2 * 2*(870*64*2)


###########################################################################


# C code formatting templates
STRUCT_TEMPLATE = """{type_name} {var_name} = {{
{content}
}};"""

INDENT = "    "

@dataclass
class CStructField:
    """Represents a field in a C structure"""
    name: str
    value: Any
    is_struct: bool = False
    
    def format(self, indent_level: int = 0) -> str:
        """Format the field as C code"""
        indent = INDENT * indent_level
        if self.is_struct:
            return f"{indent}.{self.name} = {{\n{self.value}\n{indent}}}"
        else:
            return f"{indent}.{self.name} = {{0x{self.value:08X}}}"


class CStructBuilder:
    """Builder for C structure initialization code"""
    
    def __init__(self, type_name: str, var_name: str):
        self.type_name = type_name
        self.var_name = var_name
        self.fields: List[str] = []
    
    def add_field(self, name: str, value: Any, is_struct: bool = False) -> 'CStructBuilder':
        """Add a field to the structure"""
        field = CStructField(name, value, is_struct)
        self.fields.append(field.format(1))
        return self
    
    def add_raw_line(self, line: str, indent_level: int = 1) -> 'CStructBuilder':
        """Add a raw line of code"""
        self.fields.append(INDENT * indent_level + line)
        return self
    
    def build(self) -> str:
        """Build the final C structure"""
        if self.fields:
            content = ",\n".join(self.fields)
        else:
            content = ""
        return STRUCT_TEMPLATE.format(
            type_name=self.type_name,
            var_name=self.var_name,
            content=content
        )


class WeightAddressAllocator:
    """Encapsulates weight address allocation logic"""
    
    def __init__(self, alloc_instance):
        """Initialize with a pre-created allocator instance"""
        self.allocator = alloc_instance
    
    def get_address(self, attr_name: str, layer_id: int = None) -> int:
        """Get address for a specific attribute"""
        attr = getattr(self.allocator, attr_name)
        if isinstance(attr, list) and layer_id is not None:
            return attr[layer_id] * WORD_LEN
        else:
            return attr * WORD_LEN


class LayerWeightGenerator:
    """Generates weight structure for a single layer"""
    
    def __init__(self, alloc: WeightAddressAllocator, layer_id: int):
        self.alloc = alloc
        self.layer_id = layer_id
    
    def generate_input_norm(self) -> str:
        """Generate input normalization weights"""
        return CStructField(
            "input_norm",
            self._format_struct([
                ("norm", self.alloc.get_address("input_norm", self.layer_id))
            ]),
            is_struct=True
        ).format(4)
    
    def generate_self_attn(self) -> str:
        """Generate self-attention weights"""
        fields = [
            ("q_proj        ", self.alloc.get_address("attn_q_proj", self.layer_id)),
            ("k_proj        ", self.alloc.get_address("attn_k_proj", self.layer_id)),
            ("v_proj        ", self.alloc.get_address("attn_v_proj", self.layer_id)),
            ("o_proj        ", self.alloc.get_address("attn_o_proj", self.layer_id)),
            ("q_proj_scale  ", self.alloc.get_address("attn_q_proj_qscale", self.layer_id)),
            ("k_proj_scale  ", self.alloc.get_address("attn_k_proj_qscale", self.layer_id)),
            ("v_proj_scale  ", self.alloc.get_address("attn_v_proj_qscale", self.layer_id)),
            ("o_proj_scale  ", self.alloc.get_address("attn_o_proj_qscale", self.layer_id)),
            ("q_proj_zero   ", self.alloc.get_address("attn_q_proj_qzero", self.layer_id)),
            ("k_proj_zero   ", self.alloc.get_address("attn_k_proj_qzero", self.layer_id)),
            ("v_proj_zero   ", self.alloc.get_address("attn_v_proj_qzero", self.layer_id)),
            ("o_proj_zero   ", self.alloc.get_address("attn_o_proj_qzero", self.layer_id))
        ]
        return CStructField(
            "self_attn",
            self._format_struct(fields),
            is_struct=True
        ).format(4)
    
    def generate_post_attn_norm(self) -> str:
        """Generate post-attention normalization weights"""
        return CStructField(
            "post_attention_norm",
            self._format_struct([
                ("norm", self.alloc.get_address("post_attn_norm", self.layer_id))
            ]),
            is_struct=True
        ).format(4)
    
    def generate_mlp(self) -> str:
        """Generate MLP weights"""
        fields = [
            ("gu_proj       ", self.alloc.get_address("gu_proj", self.layer_id)),
            ("d_proj        ", self.alloc.get_address("d_proj", self.layer_id)),
            ("gu_proj_scale ", self.alloc.get_address("gu_proj_qscale", self.layer_id)),
            ("d_proj_scale  ", self.alloc.get_address("d_proj_qscale", self.layer_id)),
            ("gu_proj_zero  ", self.alloc.get_address("gu_proj_qzero", self.layer_id)),
            ("d_proj_zero   ", self.alloc.get_address("d_proj_qzero", self.layer_id))
        ]
        return CStructField(
            "mlp",
            self._format_struct(fields),
            is_struct=True
        ).format(4)
    
    
    def _format_struct(self, fields: List[tuple]) -> str:
        """Format a list of fields as a C structure body"""
        lines = []
        for i, (name, value) in enumerate(fields):
            suffix = "," if i < len(fields) - 1 else ""
            lines.append(f"{INDENT * 5}.{name} = {{0x{value:08X}}}{suffix}")
        return "\n".join(lines)
    
    def generate(self) -> str:
        """Generate complete layer structure"""
        parts = [
            f"{INDENT * 4}{{ // Layer {self.layer_id}",
            self.generate_input_norm() + ",",
            self.generate_self_attn() + ",",
            self.generate_post_attn_norm() + ",",
            self.generate_mlp(),
            f"{INDENT * 4}}}"
        ]
        return "\n".join(parts)


class MiniCPMV2Generator:
    """Main generator for MiniCPMV2 structures"""
    
    def __init__(self, allocators):
        """Initialize with pre-created allocators"""
        self.allocators = allocators
    
    def generate_weight_struct(self) -> str:
        """Generate the MINICPMV2_WEIGHT structure"""
        # Build group array
        group_content = []
        for group_id in range(4):
            group_content.append(self._generate_group(group_id))
        
        # Manually build the structure to avoid comma issues
        lines = [
            f"MINICPMV2_WEIGHT minicpmv2_weight = {{",
            f"{INDENT}.group = {{"
        ]
        
        for i, group in enumerate(group_content):
            if i > 0:
                lines[-1] += ","
            lines.extend(group.split('\n'))
        
        lines.append(f"{INDENT}}}")
        lines.append("};")
        
        return "\n".join(lines)
    
    def _generate_group(self, group_id: int) -> str:
        """Generate weight structure for a single group"""
        # Use allocator from the first core of this group
        alloc = WeightAddressAllocator(self.allocators[group_id * 4])
        
        lines = [f"{INDENT * 2}{{ // Group {group_id}"]
        
        # Generate layers
        lines.append(f"{INDENT * 3}.layer = {{")
        layer_parts = []
        for layer_id in range(NUM_LAYERS):
            layer_gen = LayerWeightGenerator(alloc, layer_id)
            layer_str = layer_gen.generate()
            if layer_id < NUM_LAYERS - 1:
                layer_str += ","
            layer_parts.append(layer_str)
        lines.append("\n".join(layer_parts))
        lines.append(f"{INDENT * 3}}},")
        
        
        # Generate lm_head
        lines.append(f"{INDENT * 3}.lm_head = {{")
        lines.append(f"{INDENT * 4}.norm = {{0x{alloc.get_address('lm_head_norm'):08X}}},")
        lines.append(f"{INDENT * 4}.head = {{0x{alloc.get_address('lm_head'):08X}}},")
        lines.append(f"{INDENT * 4}.head_scale = {{0x{alloc.get_address('lm_head_scale'):08X}}},")
        lines.append(f"{INDENT * 4}.head_zero = {{0x{alloc.get_address('lm_head_zero'):08X}}}")
        lines.append(f"{INDENT * 3}}},")
        
        # Generate rope
        lines.append(f"{INDENT * 3}.rope = {{")
        lines.append(f"{INDENT * 4}.cos = {{0x{alloc.get_address('rope_cos'):08X}}},")
        lines.append(f"{INDENT * 4}.sin = {{0x{alloc.get_address('rope_sin'):08X}}}")
        lines.append(f"{INDENT * 3}}}")
        
        lines.append(f"{INDENT * 2}}}")
        
        return "\n".join(lines)
    
    def generate_kvcache_struct(self, cache_name: str, cache_attr: str) -> str:
        """Generate MINICPMV2_KVCACHE structure"""
        # Build group array
        group_content = []
        for group_id in range(4):
            group_content.append(self._generate_kvcache_group(group_id, cache_attr))
        
        # Manually build the structure to avoid comma issues
        lines = [
            f"MINICPMV2_KVCACHE {cache_name} = {{",
            f"{INDENT}.group = {{"
        ]
        
        for i, group in enumerate(group_content):
            if i > 0:
                lines[-1] += ","
            lines.extend(group.split('\n'))
        
        lines.append(f"{INDENT}}}")
        lines.append("};")
        
        return "\n".join(lines)
    
    def generate_free_space_struct(self) -> str:
        """Generate FREE_SPACE structure"""
        lines = [
            "FREE_SPACE free_space = {",
            f"{INDENT}.group = {{"
        ]
        
        for group_id in range(4):
            alloc_instance = self.allocators[group_id * 4]
            addr = alloc_instance.top_pointer * WORD_LEN
            size = int((TOTAL_WORDS - alloc_instance.get_used_size()) * WORD_LEN)
            
            comma = "," if group_id < 3 else ""
            lines.append(f"{INDENT * 2}{{ // Group {group_id}")
            lines.append(f"{INDENT * 3}.addr_dram = 0x{addr:08X},")
            lines.append(f"{INDENT * 3}.size = 0x{size:08X} // {size} bytes")
            lines.append(f"{INDENT * 2}}}{comma}")
        
        lines.append(f"{INDENT}}}")
        lines.append("};")
        
        return "\n".join(lines)
    
    def _generate_kvcache_group(self, group_id: int, cache_attr: str) -> str:
        """Generate cache addresses for a single group"""
        alloc = WeightAddressAllocator(self.allocators[group_id * 4])
        
        lines = [f"{INDENT * 2}{{ // Group {group_id}"]
        
        # Get all addresses
        addresses = []
        for layer_id in range(NUM_LAYERS):
            addr = getattr(alloc.allocator, cache_attr)[layer_id] * WORD_LEN
            addresses.append(f"{{0x{addr:08X}}}")
        
        # Format addresses in groups of 8 for readability
        for i in range(0, NUM_LAYERS, 8):
            group = addresses[i:i+8]
            suffix = "," if i + 8 < NUM_LAYERS else ""
            lines.append(f"{INDENT * 3}{', '.join(group)}{suffix}")
        
        lines.append(f"{INDENT * 2}}}")
        
        return "\n".join(lines)



def gen_structures(allocators):
    """Generate C structures"""
    generator = MiniCPMV2Generator(allocators)

    weight_file = open("./weight_structs.h", "w")

    # Generate weight structure
    weight_file.write(generator.generate_weight_struct())
    weight_file.write("\n\n")
    
    # Generate kcache structure
    weight_file.write(generator.generate_kvcache_struct("minicpmv2_kcache", "kcache"))
    weight_file.write("\n\n")
    
    # Generate vcache structure
    weight_file.write(generator.generate_kvcache_struct("minicpmv2_vcache", "vcache"))
    weight_file.write("\n\n")
    
    # Generate free space structure
    weight_file.write(generator.generate_free_space_struct())
    weight_file.write("\n\n")

    weight_file.close()

    # print used_size
    for alloc in allocators:
        used_size = alloc.get_used_size() * WORD_LEN
        free_size = int((TOTAL_WORDS - alloc.get_used_size()) * WORD_LEN)
        print(f"Core {alloc.core_id:2d} used size: {used_size} bytes, free size: {free_size} bytes")



def create_all_allocators():
    """Create and initialize allocators for all cores"""
    allocators = []
    for core_id in range(NUM_NODES):
        alloc = DRAMAllocator(core_id)
        alloc.alloc()
        allocators.append(alloc)
    return allocators


def gen_tensors(allocators):
    """Generate tensor information JSON file"""
    all_tensors = []
    
    # Collect tensors from all 16 cores
    for alloc in allocators:
        # Add all tensors from this core
        all_tensors.extend(alloc.tensors)
    
    # Save to JSON file
    with open("./tensor_info.json", "w") as f:
        json.dump(all_tensors, f, indent=4)
    
    print(f"Generated tensor information for {len(all_tensors)} tensors")


def gen_combined_tensors(allocators):
    """Generate combined tensor information JSON file"""
    combined_tensors = []
    
    # Create one combined tensor per core
    for alloc in allocators:
        # Calculate total used memory in bytes
        used_bytes = alloc.get_used_size() * WORD_LEN
        
        # Create combined tensor entry
        combined_tensor = create_min_tensor(
            "weightimage",
            alloc.core_id // 4,  # group_id
            alloc.core_id % 4,   # core_id within group
            DRAM_BASE * WORD_LEN,  # Start address
            (1, 1, int(used_bytes)),  # Shape
            "INT8"  # Data type
        )
        
        combined_tensors.append(combined_tensor)
    
    # Save to JSON file
    with open("./tensor_info_combined.json", "w") as f:
        json.dump(combined_tensors, f, indent=4)
    
    print(f"Generated combined tensor information for {len(combined_tensors)} cores")


if __name__ == "__main__":
    import sys
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='MiniCPMv2 Weight Allocator')
    parser.add_argument('--gen-struct', action='store_true',
                        help='Generate C structure files (weight_structs.h)')
    parser.add_argument('--gen-json', action='store_true',
                        help='Generate JSON tensor information files')
    parser.add_argument('--gen-weight', action='store_true',
                        help='Generate weight slice files (.pt files)')
    parser.add_argument('--gen-image', action='store_true',
                        help='Generate DRAM image for simulation backend')
    parser.add_argument('--weight-file', type=str, default='random.safetensors',
                        help='Path to weight file (default: random.safetensors)')
    parser.add_argument('--output-dir', type=str, default='./weight_slices',
                        help='Output directory for weight slices (default: ./weight_slices)')
    
    args = parser.parse_args()
    
    # If no options specified, show help and exit
    if not (args.gen_struct or args.gen_json or args.gen_weight or args.gen_image):
        parser.print_help()
        print("\nError: At least one of --gen-struct, --gen-json, --gen-weight or --gen-image must be specified..")
        sys.exit(1)
    
    # Create allocators (always needed)
    allocators = create_all_allocators()
    
    # Generate C structures if requested
    if args.gen_struct:
        print("Generating C structures...")
        gen_structures(allocators)
        print("C structures saved to weight_structs.h")
    
    # Generate JSON files if requested
    if args.gen_json:
        print("\nGenerating JSON tensor information...")
        gen_tensors(allocators)
        gen_combined_tensors(allocators)
        print("JSON files saved to tensor_info.json and tensor_info_combined.json")
    
    # Generate weight slices if requested
    if args.gen_weight:
        print(f"\nGenerating weight slices from {args.weight_file}...")
        try:
            packer = WeightPacker(args.weight_file)
            packer.pack_weights_for_all_cores(args.output_dir, include_kvcache=True)
        except FileNotFoundError:
            print(f"Error: Weight file '{args.weight_file}' not found!")
            sys.exit(1)
        except Exception as e:
            print(f"Error packing weights: {e}")
            sys.exit(1)

    if args.gen_image:
        print("\nGenerating DRAM image for simulation backend...")
        from sim_backend_torch import pack_dram_image
        pack_dram_image()

