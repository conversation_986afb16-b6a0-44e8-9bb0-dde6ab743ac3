#include "software_port_data.h"
#include "minicpmv_lmhead.h"
#include "hardware_inst_data.h"
#include "minicpmv_def.h"
#include "high_level.h"
#include "primitive.h"

void minicpmv2_lmhead(const Tensor* X_sram, const Tensor* O_sram) {
    // 边界检查
    debug_assert(X_sram != NULL);
    debug_assert(O_sram != NULL);

    // 取输入输出维度
    uint32_t d1 = X_sram->dim0; // 输入维度（等于MINICPMV2_EMBEDDING_DIM）
    uint32_t d2 = O_sram->dim0; // 输出维度（等于MINICPMV2_LMHEAD_COLpNODE）
    uint32_t N  = X_sram->dim1; // decode时N=1

    // SPAD地址定义
    const uint32_t SPAD0_BASE = 0x00000000u;  // Scratchpad0
    const uint32_t SPAD2_BASE = 0x00200000u;  // Scratchpad2

    // 获取每个group的lm_head权重地址
    uint32_t lm_head_addr[4];
    for(int i = 0; i < 4; i++) {
        lm_head_addr[i] = minicpmv2_weight.group[i].lm_head.head.addr_dram;
    }

    // 构造权重张量
    Tensor W_lm_head = {
        .base_addr = lm_head_addr[0], // 默认用第一个group，后续分组处理
        .dim0 = d2, // 输出维度
        .dim1 = d1, // 输入维度
        .dim2 = 1,
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = d2 / 2, // INT4 = 0.5字节/元素
        .byte_stride2_u = d1 * d2 / 2,
    };

    // 分块参数
    const uint32_t TILE_D1 = 128; // 输入分块
    const uint32_t TILE_D2 = 128; // 输出分块
    const uint32_t MAX_BLOCK = 60;

    // CIM 配置参数
    Tensor cim_w = {
        .base_addr = 0x00400000,
        .dim0 = TILE_D2,
        .dim1 = TILE_D1,
        .dim2 = d1 / TILE_D1,
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = TILE_D2 / 2,
    };
    CIM_Option conv_wt = {
        .type = TYPE_INT,
        .width = WIDTH_4,
        .page_index = 0,
        .accumulate = 1,
        .activate = 1,
    };

    // 创建临时张量用于分块操作
    Tensor X_tile_spad0, W_tile, O_tile_spad0, O_tile_out;
    uint32_t actual_n = 1; // decode场景只处理1个token
    uint32_t total_pages = d1 / TILE_D1;
    uint32_t max_pages_per_load = 16;
    int npu_mask_group[4]={0xf,0xf,0xf,0xf};

    // 辅助函数：生成group级别的npu mask
    void make_group_mask(int group_idx, int* mask)
    {
        for (int g = 0; g < 4; ++g) {
            mask[g] = (g == group_idx) ? 0xf : 0x0;
        }
    }

    for (uint32_t block_iter = 0; block_iter < MAX_BLOCK; ++block_iter) {


        for (uint32_t group_id = 0; group_id < 4; group_id++) {
            make_group_mask(group_id, npu_mask_group);

            // 分块矩阵乘法
            for (uint32_t d2_idx = 0; d2_idx < d2; d2_idx += TILE_D2) {
                uint32_t actual_d2 = (d2_idx + TILE_D2 <= d2) ? TILE_D2 : (d2 - d2_idx);
                // 初始化输出分块为0 - SPAD0
                O_tile_spad0.base_addr = SPAD2_BASE;
                O_tile_spad0.dim0 = actual_d2;
                O_tile_spad0.dim1 = actual_n;
                O_tile_spad0.dim2 = 1;
                O_tile_spad0.byte_stride1_u = actual_d2 * 2;
                O_tile_spad0.byte_stride2_u = actual_d2 * actual_n * 2;
                O_tile_spad0.width = O_sram->width;
                O_tile_spad0.type = O_sram->type;

                // 按page批次处理（如果total_pages > 16，需要分多次）
                for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                    uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ?
                        max_pages_per_load : (total_pages - page_batch);

                    // 配置当前批次的权重加载
                    W_tile.base_addr = lm_head_addr[group_id] + d2_idx / 2 + page_batch * W_lm_head.byte_stride1_u;
                    W_tile.dim0 = TILE_D1;
                    W_tile.dim1 = TILE_D2;
                    W_tile.dim2 = current_batch_pages;
                    W_tile.byte_stride1_u = TILE_D1 / 2;
                    W_tile.byte_stride2_u = TILE_D2 * W_lm_head.byte_stride1_u;
                    W_tile.type = TYPE_INT;
                    W_tile.width = WIDTH_4;

                    load(&W_tile, &cim_w, npu_mask_group);

                    for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {
                        uint32_t d1_idx = (page_batch + page_idx) * TILE_D1;
                        uint32_t actual_d1 = TILE_D1;
                        X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                        X_tile_spad0.dim0 = actual_d1;
                        X_tile_spad0.dim1 = actual_n;
                        X_tile_spad0.dim2 = 1;
                        X_tile_spad0.width = X_sram->width;
                        X_tile_spad0.type = X_sram->type;

                        conv_wt.page_index = page_idx;
                        gemm(&X_tile_spad0, &O_tile_spad0, &O_tile_spad0, &conv_wt, npu_mask_group);
                    }
                }
                // 配置输出张量
                O_tile_out.dim0 = actual_d2;
                O_tile_out.dim1 = actual_n;
                O_tile_out.dim2 = 1;
                O_tile_out.byte_stride1_u = O_sram->byte_stride1_u;
                O_tile_out.byte_stride2_u = O_sram->byte_stride2_u;
                O_tile_out.width = O_sram->width;
                O_tile_out.type = O_sram->type;
                O_tile_out.base_addr = O_sram->base_addr + d2_idx * 2;
                // 存储结果
                store(&O_tile_spad0, &O_tile_out, npu_mask_group);
            }
        }
    }
}