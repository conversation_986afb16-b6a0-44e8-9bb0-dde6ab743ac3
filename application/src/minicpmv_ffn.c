#include "minicpmv_ffn.h"
#include "high_level.h"
#include "primitive.h"
#include "minicpmv_basic.h"
#include "hardware_inst_data.h"
#include "minicpmv_def.h"

/**
 * @brief 处理简单切分矩阵乘法
 * @details 尺寸中的d1可以被256整除, d2可以被64整除
 * @param[in] X 输入矩阵，shape=[N, d1], BF16格式, 存储在DRAM
 * @param[in] W 权重矩阵，shape=[d1, d2], INT4格式, 存储在DRAM
 * @param[out] O 输出矩阵，shape=[N, d2], BF16格式, 存储在DRAM
 * @return 无返回值
 */
void tiled_mm(const Tensor* X, const Tensor* W, Tensor* O) {
    // 获取矩阵维度
    uint32_t N = X->dim1;
    uint32_t d1 = X->dim0;
    uint32_t d2 = W->dim0;
    
    /* SPAD 基地址定义 */
    const uint32_t SPAD0_BASE = 0x00000000u;  /* Scratchpad0 */
    const uint32_t SPAD1_BASE = 0x00100000u;  /* Scratchpad1 */
    const uint32_t SPAD2_BASE = 0x00200000u;  /* Scratchpad2 */
    
    // 验证约束条件
    debug_assert(d1 % 256 == 0);
    debug_assert(d2 % 64 == 0);
    debug_assert(X->dim0 == W->dim1);
    debug_assert(O->dim1 == N);
    debug_assert(O->dim0 == d2);
    
    // 分块大小
    const uint32_t TILE_N = 16;     // N 方向的分块大小
    const uint32_t TILE_D1 = 256;   // d1 方向的分块大小
    const uint32_t TILE_D2 = 64;    // d2 方向的分块大小
    const uint32_t HALF_TILE_N = TILE_N/2; 
    
    /* NPU 掩码 - 使用所有可用的 NPU 核心 */
    int npu_mask[4] = {0xf, 0xf, 0xf, 0xf};
    
    /* CIM 配置参数 */
    Tensor cim_w = {
        .base_addr = 0x00400000,
        .dim0 = 64,
        .dim1 = 256,
        .dim2 = d1/TILE_D1,
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = 1*32,
    };

    CIM_Option conv_wt = {
        .type = TYPE_INT,
        .width = WIDTH_4,
        .page_index = 0,
        .accumulate = 0,
        .activate = 1,
    };

    // 创建临时张量用于分块操作
    Tensor X_tile_spad0, X_tile_spad1, W_tile, O_tile_spad0, O_tile_spad1, O_tile_out;
    
    // 按 N 方向分块（对应参考代码的input_load_loop）
    for (uint32_t n = 0; n < N; n += TILE_N) {
        uint32_t actual_n = (n + TILE_N <= N) ? TILE_N : (N - n);
        
        /* 计算两个 SPAD 的行数 */
        uint32_t spad0_rows = (actual_n >= HALF_TILE_N) ? HALF_TILE_N : actual_n;
        uint32_t spad1_rows = (actual_n > HALF_TILE_N) ? (actual_n - HALF_TILE_N) : 0;
        
        /* 加载 X 数据到 SPAD0（参照参考代码：在每个输入块中先load_operator加载输入数据） */
        slice_to_spad(X, n, spad0_rows, SPAD0_BASE, npu_mask, &X_tile_spad0);
        
        // 加载 X 数据到 SPAD1 (如果需要)
        if (spad1_rows > 0) {
            slice_to_spad(X, n + HALF_TILE_N, spad1_rows, SPAD1_BASE, npu_mask, &X_tile_spad1);
        }
        
        /* 按 d2 方向分块 */
        for (uint32_t d2_idx = 0; d2_idx < d2; d2_idx += TILE_D2) {
            uint32_t actual_d2 = (d2_idx + TILE_D2 <= d2) ? TILE_D2 : (d2 - d2_idx);
            
            /* 初始化输出分块为0 - SPAD0 */
            O_tile_spad0.base_addr = SPAD2_BASE;
            O_tile_spad0.dim0 = actual_d2;
            O_tile_spad0.dim1 = spad0_rows;
            O_tile_spad0.dim2 = 1;
            O_tile_spad0.byte_stride1_u = actual_d2*2;
            O_tile_spad0.byte_stride2_u = actual_d2*spad0_rows*2;
            O_tile_spad0.width = O->width;  // BF16
            O_tile_spad0.type = O->type;    // float type
            
            // 初始化输出分块为0 - SPAD1 (如果需要)
            if (spad1_rows > 0) {
                O_tile_spad1.base_addr = SPAD2_BASE + actual_d2 * spad0_rows * 2; // SPAD1 基地址 + 偏移
                O_tile_spad1.dim0 = actual_d2;
                O_tile_spad1.dim1 = spad1_rows;
                O_tile_spad1.dim2 = 1;
                O_tile_spad1.byte_stride1_u = actual_d2*2;
                O_tile_spad1.byte_stride2_u = actual_d2*spad0_rows*2;
                O_tile_spad1.width = O->width;  // BF16
                O_tile_spad1.type = O->type;  // float type
            }
             
             // 计算总的page数和是否需要分批处理
             uint32_t total_pages = d1/TILE_D1;
             uint32_t max_pages_per_load = 16;  // 硬件限制：最多16个page
             
             // 按page批次处理（如果total_pages > 16，需要分多次）
             for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                 uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ? 
                                                max_pages_per_load : (total_pages - page_batch);
                 
                 // 配置当前批次的权重加载
                 W_tile.base_addr = W->base_addr + d2_idx /2 + page_batch * W->byte_stride1_u ;
                 W_tile.dim0 = 64;
                 W_tile.dim1 = 256;
                 W_tile.dim2 = current_batch_pages;  // 当前批次的page数
                 W_tile.byte_stride1_u = 1*32;
                 W_tile.byte_stride2_u = 256*W->byte_stride1_u;
                 W_tile.type = TYPE_INT;
                 W_tile.width = WIDTH_4;
                 
                 // 加载当前批次的权重到CIM
                 load(&W_tile, &cim_w, npu_mask);
                 
                 // 处理当前批次的所有page
                 for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {
                     uint32_t d1_idx = (page_batch + page_idx) * TILE_D1;
                     uint32_t actual_d1 = TILE_D1;  // 每个page都是256
                     
                     // 设置 SPAD0 张量，指向 SPAD 中当前 d1 分块的数据
                     X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                     X_tile_spad0.dim0 = actual_d1;
                     X_tile_spad0.dim1 = spad0_rows;
                     X_tile_spad0.dim2 = 1;
                     X_tile_spad0.width = X->width;  // BF16
                     X_tile_spad0.type = X->type;    // float type
                     
                     // 设置 SPAD1 张量，指向 SPAD 中当前 d1 分块的数据 (如果需要)
                     if (spad1_rows > 0) {
                         X_tile_spad1.base_addr = SPAD1_BASE + d1_idx * 2;
                         X_tile_spad1.dim0 = actual_d1;
                         X_tile_spad1.dim1 = spad1_rows;
                         X_tile_spad1.dim2 = 1;
                         X_tile_spad1.width = X->width;  // BF16
                         X_tile_spad1.type = X->type;    // float type
                     }
                     
                    // 更新权重配置（page_idx对应当前批次内的page索引）
                    conv_wt.page_index = page_idx;
                     
                    // 执行分块矩阵乘法 - SPAD0（对应参考代码的gemm_operator）
                    gemm(&X_tile_spad0, &O_tile_spad0, &O_tile_spad0, &conv_wt, npu_mask);

                    // 执行分块矩阵乘法 - SPAD1 (如果需要)
                    if (spad1_rows > 0) {
                        gemm(&X_tile_spad1, &O_tile_spad1, &O_tile_spad1, &conv_wt, npu_mask);
                    }
                }
            }
            
            // 配置输出张量
            O_tile_out.dim0 = actual_d2;
            O_tile_out.dim1 = spad0_rows;
            O_tile_out.dim2 = 1;
            O_tile_out.byte_stride1_u = O->byte_stride1_u;
            O_tile_out.byte_stride2_u = O->byte_stride2_u;
            O_tile_out.width = O->width;
            O_tile_out.type = O->type;
            O_tile_out.base_addr = O->base_addr + n * O->byte_stride1_u + d2_idx*2;

            // 存储结果
            store(&O_tile_spad0, &O_tile_out, npu_mask);
            if (spad1_rows > 0) {
                O_tile_out.base_addr = O->base_addr + (n + spad0_rows) * O->byte_stride1_u + d2_idx *2;
                O_tile_out.dim1 = spad1_rows;
                store(&O_tile_spad1, &O_tile_out, npu_mask);
            }
        }
    }
}

/**
 * @brief MiniCPM-V2 gate/up projection prefill阶段处理
 * @details 处理gate projection和up projection的矩阵乘法，执行SiLU激活和element-wise乘法
 * @param[in] layer_id 层号，范围0-39
 * @param[in] X_dram 输入张量，存储在DRAM
 * @param[out] gu_out 输出张量，存储在DRAM
 * @return 无返回值
 */
void minicpmv2_gu_proj_prefill(int layer_id, const Tensor* X_dram, const Tensor* gu_out) {
    /* 边界检查 */
    debug_assert(layer_id < MINICPMV2_NUM_LAYERS);
    debug_assert(X_dram != NULL);
    debug_assert(gu_out != NULL);

    const uint32_t TILE_N = 8;        // N 方向的分块大小 (720/8 = 90 次循环)
    const uint32_t TILE_D1 = 128;     // d1 方向的分块大小 (2304/128 = 18 次循环)
    const uint32_t TILE_D2 = 128;      // d2 方向的分块大小 (5760/128 = 45 次循环)
    const uint32_t MAX_BLOCK = 3;

    uint32_t N = X_dram->dim1;
    uint32_t d1 = X_dram->dim0;
    uint32_t d2 = gu_out->dim0;
    uint32_t d2_half = d2/2;
    //分两个spad存储X
    const uint32_t SPAD0_BASE = 0x00000000u;  // Scratchpad0
    const uint32_t SPAD1_BASE = 0x00100000u;  // Scratchpad1
    const uint32_t SPAD2_BASE = 0x00200000u;  // Scratchpad2


    uint32_t gu_proj_addr[4];
    uint32_t scale_addr[4];
    
    /* 初始化权重和scale和gu_proj地址 */
    for (int i = 0; i < 4; i++) {
        gu_proj_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.gu_proj.addr_dram;
        scale_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.gu_proj_scale.addr_dram;
    }


    /* 构造权重张量 - gu_proj是gate和up projection的拼接 */
    /* 输入: [N, 2304], 权重: [2304, 5760], 输出: [N, 5760] */
    Tensor W_gu_proj = {
        .base_addr = gu_proj_addr[0],  // 使用对应组的权重地址
        .dim0 = d2,        // 输出维度
        .dim1 = d1,            // 输入维度
        .dim2 = 1,
        .type = TYPE_INT,
        .width = WIDTH_4,                       // INT4权重
        .byte_stride1_u = d2 / 2,  // INT4 = 0.5字节/元素
        .byte_stride2_u = d1 * d2/2,
    };


    // 验证约束条件
    debug_assert(d1 % TILE_D1 == 0);
    debug_assert(d2 % TILE_D2 == 0);
    debug_assert(X_dram->dim0 == W_gu_proj.dim1);
    debug_assert(gu_out->dim1 == N);
    debug_assert(gu_out->dim0 == d2);
    
    /* NPU 掩码 - 使用所有可用的 NPU 核心 */
    int npu_mask[4] = {0xf, 0xf, 0xf, 0xf};

    /* CIM 配置参数 */
    Tensor cim_w = {
        .base_addr = 0x00400000u,
        .dim0 = TILE_D2,
        .dim1 = TILE_D1,
        .dim2 = d1 / TILE_D1,  /* 18 pages */
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = TILE_D2 / 2,
    };

    CIM_Option conv_wt = {
        .type = TYPE_INT,
        .width = WIDTH_4,
        .page_index = 0,
        .accumulate = 0,
        .activate = 1,
    };

    VP_Option mul_vp_option = {
        .special_case.disable0 = 0,
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation = OPERATION_MUL,
        .scalar_in2 = 0
    };
    
    

    Tensor scale = {
        .base_addr = scale_addr[0],  // 指向scale数据的物理/虚拟地址
        .dim0 = d2,
        .dim1 = d1/TILE_D1,
        .dim2 = 1,
        .type = TYPE_BF,             // 或 TYPE_FP32
        .width = WIDTH_16,             // BF16宽度
        .byte_stride1_u = d2 * 2,     // 每行128个元素，每个2字节
        .byte_stride2_u = d2 * d1/TILE_D1 * 2,
    };

    // 创建临时张量用于分块操作
    Tensor X_tile_spad0, W_tile, O_tile_spad0, O_tile_spad1, O_tile_out;
    int npu_mask_group[4];
    
    // 辅助函数：生成group级别的npu mask
    void make_group_mask(int group_idx, int* mask)
    {
        for (int g = 0; g < 4; ++g) {
            mask[g] = (g == group_idx) ? mask[g] : 0x0; // 0xf -> 4 cores active
        }
    }
    
    for (uint32_t block_iter = 0; block_iter < MAX_BLOCK; ++block_iter) {
        if (block_iter == 2) {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0x8;
        } else {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0xf;
        }
        
        for(uint32_t group_id = 0; group_id < 4; group_id++){
            make_group_mask(group_id, npu_mask_group);
        // if (block_iter == 2) {
        //     npu_mask_group[group_id] = 0xf;
        //     npu_mask_group[1] = 0xf;
        //     npu_mask_group[2] = 0xf;
        //     npu_mask_group[3] = 0x8;
        // } else {
        //     npu_mask_group[0] = 0xf;
        //     npu_mask_group[1] = 0xf;
        //     npu_mask_group[2] = 0xf;
        //     npu_mask_group[3] = 0xf;
        // }
        // for (uint32_t group_id = 0; group_id < 4; group_id++){

        // 按 N 方向分块处理 (720 行，每次处理 8 行)
            for (uint32_t n = 0; n < N; n += TILE_N) {
                uint32_t actual_n = (n + TILE_N <= N) ? TILE_N : (N - n);
                
                // 加载 X 数据到 SPAD0
                slice_to_spad(X_dram, n, actual_n, SPAD0_BASE, npu_mask_group, &X_tile_spad0);
                
                // 第一个矩阵乘法：计算 gate projection (前一半: 0 到 5760)
                for (uint32_t d2_idx = 0; d2_idx < d2_half; d2_idx += TILE_D2) {
                    uint32_t actual_d2 = (d2_idx + TILE_D2 <= d2_half) ? TILE_D2 : (d2_half - d2_idx);

                    O_tile_spad0.base_addr = SPAD2_BASE;
                    O_tile_spad0.dim0 = actual_d2;
                    O_tile_spad0.dim1 = actual_n;
                    O_tile_spad0.dim2 = 1;
                    O_tile_spad0.byte_stride1_u = actual_d2*2;
                    O_tile_spad0.byte_stride2_u = actual_d2*actual_n*2;
                    O_tile_spad0.width = gu_out->width;  // BF16
                    O_tile_spad0.type = gu_out->type;    // float type  
                    // 计算总的page数和是否需要分批处理
                    uint32_t total_pages = d1/TILE_D1;
                    uint32_t max_pages_per_load = 16;  // 硬件限制：最多16个page
                    // 分四个group_id进行计算
                    
                    // 初始化输出分块为0 - SPAD0 (gate projection 结果)
                    // 按page批次处理（如果total_pages > 16，需要分多次）
                    for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                        uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ? 
                                                        max_pages_per_load : (total_pages - page_batch);
                        
                        // 配置当前批次的权重加载 - gate projection (前一半权重)
                        W_tile.base_addr = gu_proj_addr[group_id] + d2_idx/2 + page_batch * W_gu_proj.byte_stride1_u;
                        W_tile.dim0 = TILE_D2;
                        W_tile.dim1 = TILE_D1;
                        W_tile.dim2 = current_batch_pages;  // 当前批次的page数
                        W_tile.byte_stride1_u = TILE_D2 / 2;
                        W_tile.byte_stride2_u = TILE_D1 * W_gu_proj.byte_stride1_u;
                        W_tile.type = TYPE_INT;
                        W_tile.width = WIDTH_4;
                        
                        // int npu_mask_group[4] = {0, 0, 0, 0};
                        // npu_mask_group[group_id] = 0xf;   
                        // 加载当前批次的权重到CIM
                        load(&W_tile, &cim_w, npu_mask_group);
                        
                        // 处理当前批次的所有page
                        for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {
                            uint32_t d1_idx = (page_batch + page_idx) * TILE_D1;
                            uint32_t actual_d1 = TILE_D1;  // 每个page都是128
                            scale.base_addr = scale_addr[group_id] + d1_idx/TILE_D1 * scale.byte_stride1_u + d2_idx * 2;
                            scale.dim0 = TILE_D2; 
                            scale.dim1 = 1;
                            scale.dim2 = 1;
                            scale.byte_stride1_u = d2 * 2;
                            scale.byte_stride2_u = d2 * 2;
                            // 设置 SPAD0 张量，指向 SPAD 中当前 d1 分块的数据
                            X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                            X_tile_spad0.dim0 = actual_d1;
                            X_tile_spad0.dim1 = actual_n;
                            X_tile_spad0.dim2 = 1;
                            X_tile_spad0.width = X_dram->width;  // BF16
                            X_tile_spad0.type = X_dram->type;    // float type
                            
                            // 更新权重配置（page_idx对应当前批次内的page索引）
                            conv_wt.page_index = page_idx;
                            
                            // 执行分块矩阵乘法 - gate projection
                            gemm(&X_tile_spad0, &O_tile_spad0, &O_tile_spad0, &conv_wt, npu_mask_group);
                            mul(&O_tile_spad0, &scale, &O_tile_spad0, &mul_vp_option, npu_mask_group);
                        }
                    }
                    
                
                    
                    VP_Option silu_vp_option = {
                        .special_case.disable0 = 0,
                        .special_case.round_mode = 0,
                        .special_case.saturate = 0,
                        .operation = OPERATION_MUL,
                        .scalar_in2 = 0
                    };
                    
                    // 创建临时内存用于 SiLU 计算
                    InterMemory silu_inter_mem[5];  // SiLU 需要至少 5 个内存块
                    InterMemoryArray silu_inter_array = { .memory = silu_inter_mem, .length = 5 };
                    
                    // 设置临时内存地址
                    silu_inter_mem[0].base_addr = SPAD2_BASE + actual_d2 * actual_n * 2 + 1024;  // 临时存储
                    silu_inter_mem[0].byte_size = actual_d2 * actual_n * 2;
                    silu_inter_mem[1].base_addr = silu_inter_mem[0].base_addr + silu_inter_mem[0].byte_size;
                    silu_inter_mem[1].byte_size = actual_d2 * actual_n * 2;
                    silu_inter_mem[2].base_addr = silu_inter_mem[1].base_addr + silu_inter_mem[1].byte_size;
                    silu_inter_mem[2].byte_size = actual_d2 * actual_n * 2;
                    silu_inter_mem[3].base_addr = silu_inter_mem[2].base_addr + silu_inter_mem[2].byte_size;
                    silu_inter_mem[3].byte_size = actual_d2 * actual_n * 2;
                    silu_inter_mem[4].base_addr = silu_inter_mem[3].base_addr + silu_inter_mem[3].byte_size;
                    silu_inter_mem[4].byte_size = actual_d2 * actual_n * 2;
                    
                    // 执行 SiLU 操作：silu(x) = x * sigmoid(x)
                    silu(&O_tile_spad0, &O_tile_spad0, &silu_vp_option, &silu_inter_array, npu_mask_group);

                    
                    // 初始化输出分块为0 - SPAD1 (up projection 结果)
                    O_tile_spad1.base_addr = SPAD2_BASE + actual_d2 * actual_n * 2;
                    O_tile_spad1.dim0 = actual_d2;
                    O_tile_spad1.dim1 = actual_n;
                    O_tile_spad1.dim2 = 1;
                    O_tile_spad1.byte_stride1_u = actual_d2*2;
                    O_tile_spad1.byte_stride2_u = actual_d2*actual_n*2;
                    O_tile_spad1.width = gu_out->width;  // BF16
                    O_tile_spad1.type = gu_out->type;  // float type

                    // 按page批次处理（如果total_pages > 16，需要分多次）
                    for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                        uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ? 
                                                    max_pages_per_load : (total_pages - page_batch);
                        
                        // 配置当前批次的权重加载 - up projection (后一半权重)
                        W_tile.base_addr = gu_proj_addr[group_id] + (d2_idx + d2_half)/2 + page_batch * W_gu_proj.byte_stride1_u;
                        W_tile.dim0 = TILE_D2;
                        W_tile.dim1 = TILE_D1;
                        W_tile.dim2 = current_batch_pages;  // 当前批次的page数
                        W_tile.byte_stride1_u = TILE_D2 / 2;
                        W_tile.byte_stride2_u = TILE_D1 * W_gu_proj.byte_stride1_u;
                        W_tile.type = TYPE_INT;
                        W_tile.width = WIDTH_4;

                        // int npu_mask_group[4] = {0, 0, 0, 0};
                        // npu_mask_group[group_id] = 0xf; 
                        // 加载当前批次的权重到CIM
                        load(&W_tile, &cim_w, npu_mask_group);
                        
                        // 处理当前批次的所有page
                        for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {
                            uint32_t d1_idx = (page_batch + page_idx) * TILE_D1;
                            uint32_t actual_d1 = TILE_D1;  // 每个page都是128
                            scale.base_addr = scale_addr[group_id] + d1_idx/TILE_D1 * scale.byte_stride1_u + d2_idx * 2;
                            scale.dim0 = TILE_D2; 
                            scale.dim1 = 1;
                            scale.dim2 = 1;
                            scale.byte_stride1_u = d2 * 2;
                            scale.byte_stride2_u = d2 * 2;
                            // 设置 SPAD0 张量，指向 SPAD 中当前 d1 分块的数据
                            X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                            X_tile_spad0.dim0 = actual_d1;
                            X_tile_spad0.dim1 = actual_n;
                            X_tile_spad0.dim2 = 1;
                            X_tile_spad0.width = X_dram->width;  // BF16
                            X_tile_spad0.type = X_dram->type;    // float type
                            
                            // 更新权重配置（page_idx对应当前批次内的page索引）
                            conv_wt.page_index = page_idx;
                            
                            // 执行分块矩阵乘法 - up projection
                            gemm(&X_tile_spad0, &O_tile_spad1, &O_tile_spad1, &conv_wt, npu_mask_group);
                            mul(&O_tile_spad1, &scale, &O_tile_spad1, &mul_vp_option, npu_mask_group);
                        }
                    }
                    
                    // 执行 element-wise 乘法：gate_result * up_result
                    mul(&O_tile_spad0, &O_tile_spad1, &O_tile_spad0, &mul_vp_option, npu_mask_group);

                    // 配置输出张量
                    O_tile_out.base_addr = gu_out->base_addr + n * gu_out->byte_stride1_u + d2_idx * 2;
                    O_tile_out.dim0 = actual_d2;
                    O_tile_out.dim1 = actual_n;
                    O_tile_out.dim2 = 1;
                    O_tile_out.byte_stride1_u = gu_out->byte_stride1_u;
                    O_tile_out.byte_stride2_u = gu_out->byte_stride2_u;
                    O_tile_out.width = gu_out->width;
                    O_tile_out.type = gu_out->type;
            
                    // 存储结果
                    store(&O_tile_spad0, &O_tile_out, npu_mask_group);
                }
            }
        } // 关闭 group_id 循环
    } // 关闭 block_iter 循环
}


   

 


/// @brief minicpmv2中的g_proj和u_proj以及SiLU和EWiseMul过程(decode)
/// @param layer_id 层号, 0-39
/// @param X_spad 输入X, shape=[1, d], 已在SPAD中
/// @param gu_out gate_proj和up_proj映射, 对gate_proj执行SiLU, 并EWiseMul后的输出, shape=[1, num_cols]
void minicpmv2_gu_proj_decode(int layer_id, const Tensor* X_spad, const Tensor* gu_out) {
    // 边界检查
    debug_assert(layer_id < MINICPMV2_NUM_LAYERS);
    debug_assert(X_spad != NULL);
    debug_assert(gu_out != NULL);

    const uint32_t TILE_D1 = 128;     // d1 方向的分块大小 (2304/128 = 18 次循环)
    const uint32_t TILE_D2 = 128;      // d2 方向的分块大小 (5760/128 = 45 次循环)
    const uint32_t MAX_BLOCK = 3;

    uint32_t N = X_spad->dim1;  // decode时N=1
    uint32_t d1 = X_spad->dim0;
    uint32_t d2 = gu_out->dim0;
    uint32_t d2_half = d2 / 2;

    // SPAD地址定义
    const uint32_t SPAD0_BASE = 0x00000000u;  // Scratchpad0 (输入X已在其中)
    const uint32_t SPAD2_BASE = 0x00200000u;  // Scratchpad2 (输出结果)

    uint32_t gu_proj_addr[4];
    uint32_t scale_addr[4];
    for (int i = 0; i < 4; i++) {
        gu_proj_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.gu_proj.addr_dram;
        scale_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.gu_proj_scale.addr_dram;
    }

    Tensor W_gu_proj = {
        .base_addr = gu_proj_addr[0],
        .dim0 = d2,
        .dim1 = d1,
        .dim2 = 1,
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = d2 / 2,
        .byte_stride2_u = d1 * d2 / 2,
    };

    // 验证约束条件
    debug_assert(d1 % TILE_D1 == 0);
    debug_assert(d2 % TILE_D2 == 0);
    debug_assert(X_spad->dim0 == W_gu_proj.dim1);
    debug_assert(gu_out->dim1 == N);
    debug_assert(gu_out->dim0 == d2);

    Tensor cim_w = {
        .base_addr = 0x00400000,
        .dim0 = TILE_D2,
        .dim1 = TILE_D1,
        .dim2 = d1 / TILE_D1,
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = TILE_D2 / 2,
    };

    CIM_Option conv_wt = {
        .type = TYPE_INT,
        .width = WIDTH_4,
        .page_index = 0,
        .accumulate = 0,
        .activate = 1,
    };

    VP_Option mul_vp_option = {
        .special_case.disable0 = 0,
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation = OPERATION_MUL,
        .scalar_in2 = 0
    };

    Tensor scale = {
        .base_addr = scale_addr[0],  // 指向scale数据的物理/虚拟地址
        .dim0 = d2,
        .dim1 = d1/TILE_D1,
        .dim2 = 1,
        .type = TYPE_BF,             // 或 TYPE_FP32
        .width = WIDTH_16,             // BF16宽度
        .byte_stride1_u = d2 * 2,     // 每行128个元素，每个2字节
        .byte_stride2_u = d2 * d1/TILE_D1 * 2,
    };

    // 创建临时张量用于分块操作
    Tensor X_tile_spad0, W_tile, O_tile_spad0, O_tile_spad1, O_tile_out;
    int npu_mask_group[4];
    
    // 辅助函数：生成group级别的npu mask
    void make_group_mask(int group_idx, int* mask)
    {
        for (int g = 0; g < 4; ++g) {
            mask[g] = (g == group_idx) ? mask[g] : 0x0; // 0xf -> 4 cores active
        }
    }
    
    for (uint32_t block_iter = 0; block_iter < MAX_BLOCK; ++block_iter) {
        if (block_iter == 2) {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0x8;
        } else {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0xf;
        }
        
        for(uint32_t group_id = 0; group_id < 4; group_id++){
            make_group_mask(group_id, npu_mask_group);
            
            uint32_t actual_n = 1;  // decode时只处理1个token
            uint32_t total_pages = d1 / TILE_D1;
            uint32_t max_pages_per_load = 16;

            // gate projection
            for (uint32_t d2_idx = 0; d2_idx < d2_half; d2_idx += TILE_D2) {
                uint32_t actual_d2 = (d2_idx + TILE_D2 <= d2_half) ? TILE_D2 : (d2_half - d2_idx);

                O_tile_spad0.base_addr = SPAD2_BASE;
                O_tile_spad0.dim0 = actual_d2;
                O_tile_spad0.dim1 = actual_n;
                O_tile_spad0.dim2 = 1;
                O_tile_spad0.byte_stride1_u = actual_d2 * 2;
                O_tile_spad0.byte_stride2_u = actual_d2 * actual_n * 2;
                O_tile_spad0.width = gu_out->width;
                O_tile_spad0.type = gu_out->type;

                for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                    uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ?
                        max_pages_per_load : (total_pages - page_batch);

                    W_tile.base_addr = gu_proj_addr[group_id] + d2_idx / 2 + page_batch * W_gu_proj.byte_stride1_u;
                    W_tile.dim0 = TILE_D2;
                    W_tile.dim1 = TILE_D1;
                    W_tile.dim2 = current_batch_pages;
                    W_tile.byte_stride1_u = TILE_D2 / 2;
                    W_tile.byte_stride2_u = TILE_D1 * W_gu_proj.byte_stride1_u;
                    W_tile.type = TYPE_INT;
                    W_tile.width = WIDTH_4;

                    load(&W_tile, &cim_w, npu_mask_group);

                    for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {
                        uint32_t d1_idx = (page_batch + page_idx) * TILE_D1;
                        scale.base_addr = scale_addr[group_id] + d1_idx/TILE_D1 * scale.byte_stride1_u + d2_idx * 2;
                        scale.dim0 = TILE_D2; 
                        scale.dim1 = 1;
                        scale.dim2 = 1;
                        scale.byte_stride1_u = d2 * 2;
                        scale.byte_stride2_u = d2 * 2;
                        uint32_t actual_d1 = TILE_D1;
                        X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                        X_tile_spad0.dim0 = actual_d1;
                        X_tile_spad0.dim1 = actual_n;
                        X_tile_spad0.dim2 = 1;
                        X_tile_spad0.width = X_spad->width;
                        X_tile_spad0.type = X_spad->type;

                        conv_wt.page_index = page_idx;

                        gemm(&X_tile_spad0, &O_tile_spad0, &O_tile_spad0, &conv_wt, npu_mask_group);
                        mul(&O_tile_spad0, &scale, &O_tile_spad0, &mul_vp_option, npu_mask_group);
                    }
                }

                VP_Option silu_vp_option = {
                    .special_case.disable0 = 0,
                    .special_case.round_mode = 0,
                    .special_case.saturate = 0,
                    .operation = OPERATION_MUL,
                    .scalar_in2 = 0
                };

                InterMemory silu_inter_mem[5];
                InterMemoryArray silu_inter_array = { .memory = silu_inter_mem, .length = 5 };

                silu_inter_mem[0].base_addr = SPAD2_BASE + actual_d2 * actual_n * 2 + 1024;
                silu_inter_mem[0].byte_size = actual_d2 * actual_n * 2;
                silu_inter_mem[1].base_addr = silu_inter_mem[0].base_addr + silu_inter_mem[0].byte_size;
                silu_inter_mem[1].byte_size = actual_d2 * actual_n * 2;
                silu_inter_mem[2].base_addr = silu_inter_mem[1].base_addr + silu_inter_mem[1].byte_size;
                silu_inter_mem[2].byte_size = actual_d2 * actual_n * 2;
                silu_inter_mem[3].base_addr = silu_inter_mem[2].base_addr + silu_inter_mem[2].byte_size;
                silu_inter_mem[3].byte_size = actual_d2 * actual_n * 2;
                silu_inter_mem[4].base_addr = silu_inter_mem[3].base_addr + silu_inter_mem[3].byte_size;
                silu_inter_mem[4].byte_size = actual_d2 * actual_n * 2;

                silu(&O_tile_spad0, &O_tile_spad0, &silu_vp_option, &silu_inter_array, npu_mask_group);

                // up projection
                O_tile_spad1.base_addr = SPAD2_BASE + actual_d2 * actual_n * 2;
                O_tile_spad1.dim0 = actual_d2;
                O_tile_spad1.dim1 = actual_n;
                O_tile_spad1.dim2 = 1;
                O_tile_spad1.byte_stride1_u = actual_d2*2;
                O_tile_spad1.byte_stride2_u = actual_d2*actual_n*2;
                O_tile_spad1.width = gu_out->width;
                O_tile_spad1.type = gu_out->type;

                for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                    uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ?
                        max_pages_per_load : (total_pages - page_batch);

                    W_tile.base_addr = gu_proj_addr[group_id] + (d2_idx + d2_half)/2 + page_batch * W_gu_proj.byte_stride1_u;
                    W_tile.dim0 = TILE_D2;
                    W_tile.dim1 = TILE_D1;
                    W_tile.dim2 = current_batch_pages;
                    W_tile.byte_stride1_u = TILE_D2 / 2;
                    W_tile.byte_stride2_u = TILE_D1 * W_gu_proj.byte_stride1_u;
                    W_tile.type = TYPE_INT;
                    W_tile.width = WIDTH_4;

                    load(&W_tile, &cim_w, npu_mask_group);

                    for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {
                        uint32_t d1_idx = (page_batch + page_idx) * TILE_D1;
                        uint32_t actual_d1 = TILE_D1;
                        scale.base_addr = scale_addr[group_id] + d1_idx/TILE_D1 * scale.byte_stride1_u + d2_idx * 2;
                        scale.dim0 = TILE_D2; 
                        scale.dim1 = 1;
                        scale.dim2 = 1;
                        scale.byte_stride1_u = d2 * 2;
                        scale.byte_stride2_u = d2 * 2;
                        X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                        X_tile_spad0.dim0 = actual_d1;
                        X_tile_spad0.dim1 = actual_n;
                        X_tile_spad0.dim2 = 1;
                        X_tile_spad0.width = X_spad->width;
                        X_tile_spad0.type = X_spad->type;

                        conv_wt.page_index = page_idx;

                        gemm(&X_tile_spad0, &O_tile_spad1, &O_tile_spad1, &conv_wt, npu_mask_group);
                        mul(&O_tile_spad1, &scale, &O_tile_spad1, &mul_vp_option, npu_mask_group);
                    }
                }

                mul(&O_tile_spad0, &O_tile_spad1, &O_tile_spad0, &mul_vp_option, npu_mask_group);

                O_tile_out.base_addr = gu_out->base_addr + d2_idx * 2;
                O_tile_out.dim0 = actual_d2;
                O_tile_out.dim1 = actual_n;
                O_tile_out.dim2 = 1;
                O_tile_out.byte_stride1_u = gu_out->byte_stride1_u;
                O_tile_out.byte_stride2_u = gu_out->byte_stride2_u;
                O_tile_out.width = gu_out->width;
                O_tile_out.type = gu_out->type;

                store(&O_tile_spad0, &O_tile_out, npu_mask_group);
            }
        } // 关闭 group_id 循环
    } // 关闭 block_iter 循环
}
                 
void minicpmv2_d_proj_prefill(int layer_id, const Tensor* gu_out, const Tensor* d_out){ 
    // 边界检查
    debug_assert(layer_id < MINICPMV2_NUM_LAYERS);
    debug_assert(d_out != NULL);
    debug_assert(gu_out != NULL);

    const uint32_t TILE_N = 5;        // N 方向的分块大小 (720/5 = 144 次循环)
    const uint32_t TILE_D1 = 128;     // d1 方向的分块大小 (2304/128 = 18 次循环)
    const uint32_t TILE_D2 = 128;      // d2 方向的分块大小 (5760/128 = 45 次循环)
    const uint32_t MAX_BLOCK = 3;

    uint32_t N = gu_out->dim1;
    uint32_t d1 = gu_out->dim0;
    uint32_t d2 = d_out->dim0;
    // SPAD地址定义
    const uint32_t SPAD0_BASE = 0x00000000u;  // Scratchpad0
    const uint32_t SPAD2_BASE = 0x00200000u;  // Scratchpad2


    uint32_t d_proj_addr[4];
    uint32_t scale_addr[4];
    for(int i = 0; i < 4; i ++) {
        d_proj_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.d_proj.addr_dram;
        scale_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.d_proj_scale.addr_dram;
    }


    // 构造权重张量 - d_proj是down projection
    // 输入: [N, 5760], 权重: [5760, 2304], 输出: [N, 2304]
    Tensor W_d_proj = {
        .base_addr = d_proj_addr[0],  // 使用对应组的权重地址
        .dim0 = d2,        // 输出维度
        .dim1 = d1,            // 输入维度
        .dim2 = 1,
        .type = TYPE_INT,
        .width = WIDTH_4,                       // INT4权重
        .byte_stride1_u = d2 / 2,  // INT4 = 0.5字节/元素
        .byte_stride2_u = d1 * d2/2,
    };


    // 验证约束条件
    debug_assert(d1 % TILE_D1 == 0);
    debug_assert(d2 % TILE_D2 == 0);
    debug_assert(gu_out->dim0 == W_d_proj.dim1);
    debug_assert(gu_out->dim1 == N);
    debug_assert(d_out->dim0 == d2);
    
    // CIM 配置参数
    Tensor cim_w = {
        .base_addr = 0x00400000,
        .dim0 = TILE_D2,
        .dim1 = TILE_D1,
        .dim2 = d1/TILE_D1,  // 18 pages
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = TILE_D2/2,
    };

    CIM_Option conv_wt = {
        .type = TYPE_INT,
        .width = WIDTH_4,
        .page_index = 0,
        .accumulate = 0,
        .activate = 1,
    };

    VP_Option mul_vp_option = {
        .special_case.disable0 = 0,
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation = OPERATION_MUL,
        .scalar_in2 = 0
    };
    

    Tensor scale = {
        .base_addr = scale_addr[0],  // 指向scale数据的物理/虚拟地址
        .dim0 = d2,
        .dim1 = d1/TILE_D1,
        .dim2 = 1,
        .type = TYPE_BF,             // 或 TYPE_FP32
        .width = WIDTH_16,             // BF16宽度
        .byte_stride1_u = d2 * 2,     // 每行128个元素，每个2字节
        .byte_stride2_u = d2 * d1/TILE_D1 * 2,
    };


    // 创建临时张量用于分块操作
    Tensor X_tile_spad0, W_tile, O_tile_spad0, O_tile_out;
    int npu_mask_group[4];
    
    // 辅助函数：生成group级别的npu mask
    void make_group_mask(int group_idx, int* mask)
    {
        for (int g = 0; g < 4; ++g) {
            mask[g] = (g == group_idx) ? mask[g] : 0x0; // 0xf -> 4 cores active
        }
    }
    
    for (uint32_t block_iter = 0; block_iter < MAX_BLOCK; ++block_iter) {
        if (block_iter == 2) {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0x8;
        } else {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0xf;
        }
        
        for(uint32_t group_id = 0; group_id < 4; group_id++){
            make_group_mask(group_id, npu_mask_group);
            
            // 计算总的page数和是否需要分批处理
            uint32_t total_pages = d2/TILE_D2;
            uint32_t max_pages_per_load = 16;  // 硬件限制：最多16个page
            // 按 N 方向分块处理 (720 行，每次处理 5 行)
            for (uint32_t n = 0; n < N; n += TILE_N) {
                uint32_t actual_n = (n + TILE_N <= N) ? TILE_N : (N - n);
                
                // 加载 X 数据到 SPAD0
                slice_to_spad(gu_out, n, actual_n, SPAD0_BASE, npu_mask_group, &X_tile_spad0);
                uint32_t actual_d2 = TILE_D2;
                // 矩阵乘法：计算 down projection
                for (uint32_t d1_idx = 0; d1_idx < d1; d1_idx += TILE_D1) {
                    uint32_t actual_d1 = (d1_idx + TILE_D1 <= d1) ? TILE_D1 : (d1 - d1_idx);

                    O_tile_spad0.base_addr = SPAD2_BASE;
                    O_tile_spad0.dim0 = actual_d2;
                    O_tile_spad0.dim1 = actual_n;
                    O_tile_spad0.dim2 = 1;
                    O_tile_spad0.byte_stride1_u = actual_d2*2;
                    O_tile_spad0.byte_stride2_u = actual_d2*actual_n*2;
                    O_tile_spad0.width = d_out->width;  // BF16
                    O_tile_spad0.type = d_out->type;    // float type  
                    
                    // 设置 SPAD0 张量，指向 SPAD 中当前 d1 分块的数据
                    X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                    X_tile_spad0.dim0 = actual_d1;
                    X_tile_spad0.dim1 = actual_n;
                    X_tile_spad0.dim2 = 1;
                    X_tile_spad0.width = gu_out->width;  // BF16
                    X_tile_spad0.type = gu_out->type;    // float type
                     // 按page批次处理（如果total_pages > 16，需要分多次）
                     for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                         uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ? 
                                                        max_pages_per_load : (total_pages - page_batch);
                         
                         // 配置当前批次的权重加载 - down projection
                         
                         W_tile.base_addr = d_proj_addr[group_id] + d1_idx * W_d_proj.byte_stride1_u + page_batch / 2;
                         W_tile.dim0 = TILE_D2;
                         W_tile.dim1 = TILE_D1;
                         W_tile.dim2 = current_batch_pages;  // 当前批次的page数
                         W_tile.byte_stride1_u = TILE_D2 / 2;
                         W_tile.byte_stride2_u = TILE_D1 * W_d_proj.byte_stride1_u;
                         W_tile.type = TYPE_INT;
                         W_tile.width = WIDTH_4;
                         
                         // 加载当前批次的权重到CIM
                         load(&W_tile, &cim_w, npu_mask_group);
                         
                         // 处理当前批次的所有page
                         for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {
                             uint32_t d2_idx = (page_batch + page_idx) * TILE_D2;
                             
                             
                            // 更新权重配置（page_idx对应当前批次内的page索引）
                            conv_wt.page_index = page_idx;
                            scale.base_addr = scale_addr[group_id] + d1_idx/TILE_D1 * scale.byte_stride1_u + d2_idx * 2;
                            scale.dim0 = TILE_D2; 
                            scale.dim1 = 1;
                            scale.dim2 = 1;
                            scale.byte_stride1_u = d2 * 2;
                            scale.byte_stride2_u = d2 * 2;
                            // 执行分块矩阵乘法 - down projection
                            gemm(&X_tile_spad0, &O_tile_spad0, &O_tile_spad0, &conv_wt, npu_mask_group);
                            mul(&O_tile_spad0, &scale, &O_tile_spad0, &mul_vp_option, npu_mask_group);

                            O_tile_out.dim0 = actual_d2;
                            O_tile_out.dim1 = actual_n;
                            O_tile_out.dim2 = 1;
                            O_tile_out.byte_stride1_u = d_out->byte_stride1_u;
                            O_tile_out.byte_stride2_u = d_out->byte_stride2_u;
                            O_tile_out.width = d_out->width;
                            O_tile_out.type = d_out->type;
                            O_tile_out.base_addr = d_out->base_addr + d2_idx * 2 + d1_idx * d_out->byte_stride1_u;

                            // 存储结果
                            store(&O_tile_spad0, &O_tile_out, npu_mask_group);
                        }
                    }
                }   
            }
        } // 关闭 group_id 循环
    } // 关闭 block_iter 循环
}

 
void minicpmv2_d_proj_decode(int layer_id, const Tensor* gu_out, const Tensor* d_out) {
    // 边界检查
    debug_assert(layer_id < MINICPMV2_NUM_LAYERS);
    debug_assert(gu_out != NULL);
    debug_assert(d_out != NULL);

    const uint32_t TILE_D1 = 128;     // d1 方向的分块大小 (5760/128 = 45 次循环)
    const uint32_t TILE_D2 = 128;     // d2 方向的分块大小 (2304/128 = 18 次循环)
    const uint32_t MAX_BLOCK = 3;

    uint32_t d1 = gu_out->dim0;         // 输入维度
    uint32_t d2 = d_out->dim0;        // 输出维度
    uint32_t N = gu_out->dim1;        // decode时N=1

    // SPAD地址定义
    const uint32_t SPAD0_BASE = 0x00000000u;  // Scratchpad0 (输入X已在其中)
    const uint32_t SPAD2_BASE = 0x00200000u;  // Scratchpad2 (输出结果)

    uint32_t d_proj_addr[4];
    uint32_t scale_addr[4];
    for (int i = 0; i < 4; i++) {
        d_proj_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.d_proj.addr_dram;
        scale_addr[i] = minicpmv2_weight.group[i].layer[layer_id].mlp.d_proj_scale.addr_dram;
    }

    Tensor W_d_proj = {
        .base_addr = d_proj_addr[0],
        .dim0 = d2,
        .dim1 = d1,
        .dim2 = 1,
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = d2 / 2,
        .byte_stride2_u = d1 * d2 / 2,
    };

    // 验证约束条件
    debug_assert(d1 % TILE_D1 == 0);
    debug_assert(d2 % TILE_D2 == 0);
    debug_assert(gu_out->dim0 == W_d_proj.dim1);
    debug_assert(gu_out->dim1 == N);
    debug_assert(d_out->dim0 == d2);

    uint32_t total_pages = d2/TILE_D2;
    uint32_t max_pages_per_load = 16;  // 硬件限制：最多16个page
    // CIM 配置参数
    Tensor cim_w = {
        .base_addr = 0x00400000,
        .dim0 = TILE_D2,
        .dim1 = TILE_D1,
        .dim2 = d1 / TILE_D1,
        .type = TYPE_INT,
        .width = WIDTH_4,
        .byte_stride1_u = TILE_D2 / 2,
    };

    CIM_Option conv_wt = {
        .type = TYPE_INT,
        .width = WIDTH_4,
        .page_index = 0,
        .accumulate = 0,
        .activate = 1,
    };

    VP_Option mul_vp_option = {
        .special_case.disable0 = 0,
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation = OPERATION_MUL,
        .scalar_in2 = 0
    };

    Tensor scale = {
        .base_addr = scale_addr[0],
        .dim0 = d2,
        .dim1 = d1 / TILE_D1,
        .dim2 = 1,
        .type = TYPE_BF,
        .width = WIDTH_16,
        .byte_stride1_u = d2 * 2,
        .byte_stride2_u = d2 * d1 / TILE_D1 * 2,
    };

    Tensor X_tile_spad0, W_tile, O_tile_spad0, O_tile_out;
    int npu_mask_group[4];
    
    // 辅助函数：生成group级别的npu mask
    void make_group_mask(int group_idx, int* mask)
    {
        for (int g = 0; g < 4; ++g) {
            mask[g] = (g == group_idx) ? mask[g] : 0x0; // 0xf -> 4 cores active
        }
    }
    
    for (uint32_t block_iter = 0; block_iter < MAX_BLOCK; ++block_iter) {
        if (block_iter == 2) {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0x8;
        } else {
            npu_mask_group[0] = 0xf;
            npu_mask_group[1] = 0xf;
            npu_mask_group[2] = 0xf;
            npu_mask_group[3] = 0xf;
        }
        
        for(uint32_t group_id = 0; group_id < 4; group_id++){
            make_group_mask(group_id, npu_mask_group);
    
    // decode场景：N=1，不需要N方向分块循环
            uint32_t actual_n = 1;  // decode时只处理1个token
            uint32_t actual_d2 = TILE_D2;
            // 矩阵乘法：计算 down projection
            for (uint32_t d1_idx = 0; d1_idx < d1; d1_idx += TILE_D1) {
                uint32_t actual_d1 = (d1_idx + TILE_D1 <= d1) ? TILE_D1 : (d1 - d1_idx);

                O_tile_spad0.base_addr = SPAD2_BASE;
                O_tile_spad0.dim0 = actual_d2;
                O_tile_spad0.dim1 = actual_n;
                O_tile_spad0.dim2 = 1;
                O_tile_spad0.byte_stride1_u = actual_d2*2;
                O_tile_spad0.byte_stride2_u = actual_d2*actual_n*2;
                O_tile_spad0.width = d_out->width;  // BF16
                O_tile_spad0.type = d_out->type;    // float type  
                
                // 分四个group_id进行计算
                
                // 初始化输出分块为0 - SPAD0 (down projection 结果)
                
                // 设置 SPAD0 张量，指向 SPAD 中当前 d1 分块的数据
                X_tile_spad0.base_addr = SPAD0_BASE + d1_idx * 2;
                X_tile_spad0.dim0 = actual_d1;
                X_tile_spad0.dim1 = actual_n;
                X_tile_spad0.dim2 = 1;
                X_tile_spad0.width = gu_out->width;  // BF16
                X_tile_spad0.type = gu_out->type;    // float type
                // 按page批次处理（如果total_pages > 16，需要分多次）
                for (uint32_t page_batch = 0; page_batch < total_pages; page_batch += max_pages_per_load) {
                    uint32_t current_batch_pages = (page_batch + max_pages_per_load <= total_pages) ? 
                                                    max_pages_per_load : (total_pages - page_batch);
                    
                    // 配置当前批次的权重加载 - down projection
                    
                    W_tile.base_addr = d_proj_addr[group_id] + d1_idx * W_d_proj.byte_stride1_u + page_batch / 2;
                    W_tile.dim0 = TILE_D2;
                    W_tile.dim1 = TILE_D1;
                    W_tile.dim2 = current_batch_pages;  // 当前批次的page数
                    W_tile.byte_stride1_u = TILE_D2 / 2;
                    W_tile.byte_stride2_u = TILE_D1 * W_d_proj.byte_stride1_u;
                    W_tile.type = TYPE_INT;
                    W_tile.width = WIDTH_4;
                    
                    int npu_mask_group[4] = {0, 0, 0, 0};
                    npu_mask_group[group_id] = 0xf;   
                    // 加载当前批次的权重到CIM
                    load(&W_tile, &cim_w, npu_mask_group);
                    
                    // 处理当前批次的所有page
                    for (uint32_t page_idx = 0; page_idx < current_batch_pages; page_idx++) {

                        uint32_t d2_idx = (page_batch + page_idx) * TILE_D2;
                        scale.base_addr = scale_addr[group_id] + d1_idx/TILE_D1 * scale.byte_stride1_u + d2_idx * 2;
                        scale.dim0 = TILE_D2; 
                        scale.dim1 = 1;
                        scale.dim2 = 1;
                        scale.byte_stride1_u = d2 * 2;
                        scale.byte_stride2_u = d2 * 2;
                        // 更新权重配置（page_idx对应当前批次内的page索引）
                        conv_wt.page_index = page_idx;
                        // 执行分块矩阵乘法 - down projection
                        gemm(&X_tile_spad0, &O_tile_spad0, &O_tile_spad0, &conv_wt, npu_mask_group);
                        mul(&O_tile_spad0, &scale, &O_tile_spad0, &mul_vp_option, npu_mask_group);

                        O_tile_out.dim0 = actual_d2;
                        O_tile_out.dim1 = actual_n;
                        O_tile_out.dim2 = 1;
                        O_tile_out.byte_stride1_u = d_out->byte_stride1_u;
                        O_tile_out.byte_stride2_u = d_out->byte_stride2_u;
                        O_tile_out.width = d_out->width;
                        O_tile_out.type = d_out->type;
                        O_tile_out.base_addr = d_out->base_addr + d2_idx * 2 + d1_idx * d_out->byte_stride1_u;

                        // 存储结果
                        store(&O_tile_spad0, &O_tile_out, npu_mask_group);
                    }
                }  
            }
        }
    }
}