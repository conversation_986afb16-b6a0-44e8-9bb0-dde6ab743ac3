#include <stdio.h>

#include "nice_inst_single.h"

inline static void end_simulation() {
    asm volatile(
        ".insn r 0x0b, 0, 0x7f, x0, x0, x0"    
    );
}

int test_allreduce();
int test_ddr_intensive(void);
/////////////////////////////////////////////////////
// Template for main function                      //
// Don't submit this file on git                   //
/////////////////////////////////////////////////////
int main() {
    // test_allreduce();
    test_ddr_intensive();
    sync_drv();
    end_simulation();
    printf("All tests finished\n");
}
