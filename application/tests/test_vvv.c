#include "hardware_inst_data.h"
#include "nuclei_sdk_soc.h"
#include "high_level.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}


int test_vvv()
{
    init_vnice();

    Tensor tensor_in1 = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 256,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 16 * 32, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF 
    };

    Tensor tensor_in2 = (Tensor){
        .base_addr      = SCRATCHPAD1_ADDR,
        .dim0           = 256,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 16 * 32, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF 
    };

    Tensor tensor_out = (Tensor){
        .base_addr      = SCRATCHPAD2_ADDR,
        .dim0           = 256,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 16 * 32, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF 
    };


    int npu_mask[MAX_MASK] = {0x5, 0, 0, 0xf};

    VP_Option vp_option = {
        .special_case.disable0 = 0, 
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation      = OPERATION_ADD, 
        .scalar_in2     = -1
    };

    tensor_tensor_operator(&tensor_in1, &tensor_in2, &tensor_out, &vp_option, npu_mask);
    return 0;
}
