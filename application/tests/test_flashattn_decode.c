#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include "high_level.h"
#include "minicpmv_attn.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

int test_flashattn_decode(void)
{
    init_vnice();

    // ---- Tensor shape ----
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t MAX_HEADS_PER_GROUP = 3;
    const uint32_t NUM_GROUPS = 4;
    const uint32_t MAX_HEADS_TOTAL = MAX_HEADS_PER_GROUP; // 3 (每次只处理一个group的head)

    // 每行字节计算
    const uint32_t Q_ROW_BYTES = HEAD_DIM * 2; // 64 * 2 = 128
    const uint32_t Q_STRIDE_256B = Q_ROW_BYTES / 32; // 4

    const uint32_t O_ROW_BYTES = HEAD_DIM * 2; // 64 * 2 = 128
    const uint32_t O_STRIDE_256B = O_ROW_BYTES / 32; // 4

    // ---------- Create base addresses ----------
    const uint32_t SPAD_BASE_Q            = SCRATCHPAD0_ADDR;        // Q在SPAD0
    const uint32_t SPAD_BASE_O_OUTPUT     = SCRATCHPAD1_ADDR;        // O在SPAD1
    const uint32_t INTERMEMORY_BASE       = SCRATCHPAD2_ADDR;        // InterMemory在SPAD2

    // Q输入张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64] 在SPAD上
    Tensor tensor_q_spad = {
        .base_addr      = SPAD_BASE_Q,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = Q_STRIDE_256B * 32,
        .byte_stride2_u = Q_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // O输出张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64] 在SPAD上
    Tensor tensor_o_output = {
        .base_addr      = SPAD_BASE_O_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = O_STRIDE_256B * 32,
        .byte_stride2_u = O_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // ---- InterMemoryArray 设置 (需要10个块，总计4.5KB) ----
    static InterMemory flash_mem[10];
    uint32_t base_addr = INTERMEMORY_BASE;

    // K/V/S 缓存分配 (3×1280B = 3840B)
    flash_mem[0].base_addr = base_addr;
    flash_mem[0].byte_size = 1280;  // K_block
    flash_mem[1].base_addr = base_addr + 1280;
    flash_mem[1].byte_size = 1280;  // K_block_T
    flash_mem[2].base_addr = base_addr + 2560;
    flash_mem[2].byte_size = 1280;  // V_block

    // 小缓存块 (288B)
    flash_mem[3].base_addr = base_addr + 3840;
    flash_mem[3].byte_size = 32;   // S/P_block (对齐到32B)
    flash_mem[4].base_addr = base_addr + 3872;
    flash_mem[4].byte_size = 128;  // O_accum
    flash_mem[5].base_addr = base_addr + 4000;
    flash_mem[5].byte_size = 128;  // O_update

    // exp_v1 中间缓存 (4×128B = 512B)
    for(int i = 6; i < 10; i++) {
        flash_mem[i].base_addr = base_addr + 4128 + (i-6) * 128;
        flash_mem[i].byte_size = 128;
    }

    InterMemoryArray flash_intermemory = {
        .memory = flash_mem,
        .length = 10
    };

    // 调用FlashAttention decode函数
    int seq_idx = 720; // decode阶段从prompt_len开始，历史长度 = seq_idx + 1 = 721
    int layer_id = 0;  // 测试第0层
    
    minicpmv2_flashattn_decode(seq_idx, layer_id, &tensor_q_spad, 
                              &tensor_o_output, &flash_intermemory);

    printf("FlashAttention Decode test completed for seq_idx=%d, layer_id=%d\n", seq_idx, layer_id);
    printf("Historical sequence length: %d\n", seq_idx + 1);
    return 0;
} 