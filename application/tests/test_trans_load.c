#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "high_level.h"



inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}


int test_transload(void)
{

    init_vnice(); 

    Tensor tensor_wt_dram = (Tensor){
        .base_addr      = DRAM_ADDR,
        .dim0           = 6,
        .dim1           = 4,
        .dim2           = 1,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*4,
        .width          = WIDTH_32,
        .type           = TYPE_INT 
    };

    Tensor tensor_wt_scratchpad = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 4,
        .dim1           = 6,
        .dim2           = 1,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*6,
        .width          = WIDTH_32,
        .type           = TYPE_INT 
    };

    int npu_mask[MAX_MASK] = {0x5, 0, 0, 0xf};
    trans_load(&tensor_wt_dram, &tensor_wt_scratchpad, npu_mask);


    return 0;
}
