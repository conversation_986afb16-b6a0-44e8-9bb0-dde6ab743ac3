#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include "high_level.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

int test_distributed_add_rmsnorm(void)
{
    init_vnice();

    // ---- Tensor shape ----
    const uint32_t D = MINICPMV2_EMBEDDING_DIM;   // 2304
    const uint32_t N = MINICPMV2_PROMPT_LEN;      // 720

    // 每行字节 = 2304 * 2 = 4608 → stride = 4608 
    const uint32_t ROW_BYTES  = 4608;
    const uint32_t STRIDE_256B = ROW_BYTES / 32;  // 144

    // ---------- Create Tensor descriptors ----------
    const uint32_t DRAM_BASE_INPUT        = DRAM_ADDR + 0x00000000;  // 0  MB offset
    const uint32_t DRAM_BASE_INPUT_X      = DRAM_ADDR + 0x00400000;  // 4  MB offset
    const uint32_t DRAM_BASE_OUTPUT       = DRAM_ADDR + 0x00800000;  // 8  MB offset
    const uint32_t DRAM_BASE_OUTPUT_NORM  = DRAM_ADDR + 0x00C00000;  // 12 MB offset
    const uint32_t DRAM_BASE_GAMMA        = DRAM_ADDR + 0x01000000;  // 16 MB offset (small vector)

    Tensor tensor_input = {
        .base_addr      = DRAM_BASE_INPUT,    // P_i
        .dim0           = D,
        .dim1           = N,
        .dim2           = 1,
        .byte_stride1_u = STRIDE_256B * 32,
        .byte_stride2_u = STRIDE_256B * 32 * N,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    Tensor tensor_input_x = tensor_input;
    tensor_input_x.base_addr = DRAM_BASE_INPUT_X; // residual X

    Tensor tensor_output = tensor_input;          // ResAdd 结果
    tensor_output.base_addr = DRAM_BASE_OUTPUT;

    Tensor tensor_output_norm = tensor_input;     // ResAdd+Norm 结果
    tensor_output_norm.base_addr = DRAM_BASE_OUTPUT_NORM;

    // γ 权重向量 (shape [D]) 放在 DRAM，按 dim0=D, dim1=1
    Tensor tensor_gamma = {
        .base_addr      = DRAM_BASE_GAMMA,
        .dim0           = D,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = STRIDE_256B * 32,   // 同一行宽
        .byte_stride2_u = STRIDE_256B * 32,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // InterMemoryArray 参数占位，不在该算子中使用
    InterMemoryArray dummy = { .memory = NULL, .length = 0 };


    DistributedAddRMSNormAllReduce(&tensor_input,
                                   &tensor_input_x,
                                   &tensor_output,
                                   &tensor_output_norm,
                                   &tensor_gamma,
                                   &dummy);

    return 0;
} 