#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include "high_level.h"
#include "minicpmv_attn.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

int test_qkvgen_prefill(void)
{
    init_vnice();

    // ---- Tensor shape ----
    const uint32_t D = MINICPMV2_EMBEDDING_DIM;   // 2304
    const uint32_t N = MINICPMV2_PROMPT_LEN;      // 720
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t NUM_HEADS_TOTAL = 36; // 总head数

    // 每行字节 = 2304 * 2 = 4608 → stride = 4608 
    const uint32_t ROW_BYTES  = 4608;
    const uint32_t STRIDE_256B = ROW_BYTES / 32;  // 144

    // Q输出每行字节 = 64 * 2 = 128
    const uint32_t Q_ROW_BYTES = HEAD_DIM * 2;
    const uint32_t Q_STRIDE_256B = Q_ROW_BYTES / 32; // 4

    // ---------- Create Tensor descriptors ----------
    const uint32_t DRAM_BASE_INPUT        = DRAM_ADDR + 0x00000000;  // 0  MB offset
    const uint32_t DRAM_BASE_Q_OUTPUT     = DRAM_ADDR + 0x00400000;  // 4  MB offset

    // X输入张量 [N, D] = [720, 2304]
    Tensor tensor_input = {
        .base_addr      = DRAM_BASE_INPUT,
        .dim0           = D,
        .dim1           = N,
        .dim2           = 1,
        .byte_stride1_u = STRIDE_256B * 32,
        .byte_stride2_u = STRIDE_256B * 32 * N,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };



    // 调用QKV prefill函数
    int layer_id = 0; // 测试第0层


    // Q输出张量 [head_mapping * N, HEAD_DIM] = [3*720, 64]
    Tensor tensor_q_output = {
        .base_addr      = DRAM_BASE_Q_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = 3 * N,
        .dim2           = 1,
        .byte_stride1_u = Q_STRIDE_256B * 32,
        .byte_stride2_u = Q_STRIDE_256B * 32 * (3 * N),
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    minicpmv2_qkvgen_prefill(layer_id, &tensor_input, &tensor_q_output);

    printf("QKV Prefill test completed for layer %d\n", layer_id);
    return 0;
} 