#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "high_level.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}


int test_tensor_tensor_add(void)
{
    init_vnice();

    Tensor tensor_in1 = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 4,
        .dim1           = 5,
        .dim2           = 6,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*5, 
        .width          = WIDTH_32, 
        .type           = TYPE_INT 
    };
    Tensor tensor_in2   = (Tensor){
        .base_addr      = SCRATCHPAD1_ADDR,
        .dim0           = 4,
        .dim1           = 5,
        .dim2           = 6,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*5, 
        .width          = WIDTH_32, 
        .type           = TYPE_INT 
    };
    Tensor tensor_out   = (Tensor){
        .base_addr      = SCRATCHPAD2_ADDR,
        .dim0           = 4,
        .dim1           = 5,
        .dim2           = 6,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*5, 
        .width          = WIDTH_32, 
        .type           = TYPE_INT 
    };
    VP_Option vp_option = {
        .special_case.disable0 = 0, 
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation      = OPERATION_ADD, 
        .scalar_in2     = 3
    };


    int npu_mask[MAX_MASK] = {0x5, 0, 0, 0xf};

    add(&tensor_in1, &tensor_in2, &tensor_out, &vp_option, npu_mask);


    return 0;
}
