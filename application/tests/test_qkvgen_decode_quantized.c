#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include "high_level.h"
#include "minicpmv_attn.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

int test_qkvgen_decode_quantized(void)
{
    init_vnice();

    // ---- Tensor shape ----
    const uint32_t D = MINICPMV2_EMBEDDING_DIM;   // 2304
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t MAX_HEADS_PER_GROUP = 3;
    const uint32_t NUM_GROUPS = 4;
    const uint32_t MAX_HEADS_TOTAL = MAX_HEADS_PER_GROUP; // 3 (每个core只处理3个heads)

    // 每行字节计算
    const uint32_t X_ROW_BYTES = D * 2; // 2304 * 2 = 4608
    const uint32_t X_STRIDE_256B = X_ROW_BYTES / 32; // 144

    const uint32_t QKV_ROW_BYTES = HEAD_DIM * 2; // 64 * 2 = 128
    const uint32_t QKV_STRIDE_256B = QKV_ROW_BYTES / 32; // 4

    // ---------- Create base addresses ----------
    const uint32_t SPAD_BASE_X            = SCRATCHPAD0_ADDR;        // X在SPAD0
    const uint32_t SPAD_BASE_Q_OUTPUT     = SCRATCHPAD1_ADDR;        // Q在SPAD1
    const uint32_t SPAD_BASE_K_OUTPUT     = SCRATCHPAD2_ADDR;        // K在SPAD2
    const uint32_t SPAD_BASE_V_OUTPUT     = SCRATCHPAD3_ADDR;        // V在SPAD3

    // X输入张量 [1, D] = [1, 2304] 在SPAD上
    Tensor tensor_x_spad = {
        .base_addr      = SPAD_BASE_X,
        .dim0           = D,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = X_STRIDE_256B * 32,
        .byte_stride2_u = X_STRIDE_256B * 32,
        .width          = WIDTH_16,
        .type           = TYPE_BF
    };

    // Q输出张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64] 在SPAD上
    // 注意：每个core处理3个heads，与未量化版本一致
    Tensor tensor_q_output = {
        .base_addr      = SPAD_BASE_Q_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = QKV_STRIDE_256B * 32,
        .byte_stride2_u = QKV_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_BF
    };

    // K输出张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64]
    Tensor tensor_k_output = {
        .base_addr      = SPAD_BASE_K_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = QKV_STRIDE_256B * 32,
        .byte_stride2_u = QKV_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_BF
    };

    // V输出张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64]
    Tensor tensor_v_output = {
        .base_addr      = SPAD_BASE_V_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = QKV_STRIDE_256B * 32,
        .byte_stride2_u = QKV_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_BF
    };

    // ---- InterMemoryArray 设置 (需要15个块，约6KB，合理分配到SPAD) ----
    static InterMemory decode_quant_mem[15];
    
    // ========== SPAD1: 量化参数 (2880B) ==========
    uint32_t spad1_offset = 0x8000; // 在SPAD1后半部分，避免与Q输出冲突
    // [0]: scale参数缓存 [18, 64] = 2304B
    decode_quant_mem[0].base_addr = SCRATCHPAD1_ADDR + spad1_offset;
    decode_quant_mem[0].byte_size = 2304;
    spad1_offset += 2304;

    // [1]: zero参数缓存 [18, 64] INT4 = 576B  
    decode_quant_mem[1].base_addr = SCRATCHPAD1_ADDR + spad1_offset;
    decode_quant_mem[1].byte_size = 576;
    spad1_offset += 576;

    // ========== SPAD2: 量化累加器和RoPE缓存 (640B) ==========
    uint32_t spad2_offset = 0x8000; // 在SPAD2后半部分，避免与K输出冲突
    // [2]: Q量化累加器 [1, 64] BF16 = 128B
    decode_quant_mem[2].base_addr = SCRATCHPAD2_ADDR + spad2_offset;
    decode_quant_mem[2].byte_size = 128;
    spad2_offset += 128;

    // [3]: K量化累加器 [1, 64] BF16 = 128B
    decode_quant_mem[3].base_addr = SCRATCHPAD2_ADDR + spad2_offset;
    decode_quant_mem[3].byte_size = 128;
    spad2_offset += 128;

    // [4]: V量化累加器 [1, 64] BF16 = 128B
    decode_quant_mem[4].base_addr = SCRATCHPAD2_ADDR + spad2_offset;
    decode_quant_mem[4].byte_size = 128;
    spad2_offset += 128;

    // [7-10]: RoPE Q中间缓存 (4个×32B = 128B)
    for(int i = 7; i < 11; i++) {
        decode_quant_mem[i].base_addr = SCRATCHPAD2_ADDR + spad2_offset;
        decode_quant_mem[i].byte_size = 32;
        spad2_offset += 32;
    }

    // [11-14]: RoPE K中间缓存 (4个×32B = 128B)
    for(int i = 11; i < 15; i++) {
        decode_quant_mem[i].base_addr = SCRATCHPAD2_ADDR + spad2_offset;
        decode_quant_mem[i].byte_size = 32;
        spad2_offset += 32;
    }

    // ========== SPAD3: cos/sin缓存 (256B) ==========
    uint32_t spad3_offset = 0x8000; // 在SPAD3后半部分，避免与V输出冲突
    // [5]: cos缓存 [1, 64] BF16 = 128B  
    decode_quant_mem[5].base_addr = SCRATCHPAD3_ADDR + spad3_offset;
    decode_quant_mem[5].byte_size = 128;
    spad3_offset += 128;

    // [6]: sin缓存 [1, 64] BF16 = 128B
    decode_quant_mem[6].base_addr = SCRATCHPAD3_ADDR + spad3_offset;
    decode_quant_mem[6].byte_size = 128;
    spad3_offset += 128;

    InterMemoryArray decode_quant_intermemory = {
        .memory = decode_quant_mem,
        .length = 15
    };

    // 调用QKV decode量化函数
    int seq_idx = 720; // decode阶段从prompt_len开始
    int layer_id = 0;  // 测试第0层

    printf("Starting QKV Decode Quantized test...\n");
    printf("seq_idx: %d, layer_id: %d\n", seq_idx, layer_id);
    printf("Input: [%d,%d], Output: Q/K/V [%d,%d]\n", 1, D, MAX_HEADS_TOTAL, HEAD_DIM);
    printf("InterMemory distribution (optimized for decode):\n");
    printf("- SPAD1: %d KB (quantization params)\n", (spad1_offset - 0x8000)/1024);
    printf("- SPAD2: %d KB (accumulators + RoPE)\n", (spad2_offset - 0x8000)/1024);
    printf("- SPAD3: %d KB (cos/sin cache)\n", (spad3_offset - 0x8000)/1024);
    printf("Total InterMemory: 15 blocks, ~%d KB\n", 
           ((spad1_offset - 0x8000) + (spad2_offset - 0x8000) + (spad3_offset - 0x8000))/1024);

    minicpmv2_qkvgen_decode_quantized(seq_idx, layer_id, &tensor_x_spad, 
                                     &tensor_q_output, &tensor_k_output, &tensor_v_output,
                                     &decode_quant_intermemory);

    printf("QKV Decode Quantized test completed for seq_idx=%d, layer_id=%d\n", seq_idx, layer_id);
    printf("Performance comparison vs standard decode:\n");
    printf("- Memory usage: ~5.25KB vs ~1.25KB (quantization overhead for precision)\n");
    printf("- Quantization: INT4 weights vs BF16 (4x compression)\n");
    printf("- Output: %d heads per core (same as standard version)\n", MAX_HEADS_TOTAL);
    printf("- Single token efficiency: Optimized for decode scenario\n");

    return 0;
}