#include <stdio.h>
#include "high_level.h"
#include "minicpmv_basic.h"
#include "minicpmv_attn.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

// 严格遵守内存限制的测试参数
// Scratchpad: 64KB*4, DRAM: 128MB
#define TEST_TOTAL_ROWS 1024    // 总行数
#define TEST_COLS 128           // 列数，确保每个tile不超过SPAD限制  
#define TEST_TILE_ROWS 32       // 每次处理行数: 32*128*2=8KB < 64KB SPAD限制
#define TEST_NUM_TILES (TEST_TOTAL_ROWS / TEST_TILE_ROWS)  // 32个tiles
#define TEST_ITERATIONS 8       // 8轮迭代，产生大量DDR读写

int test_ddr_intensive(void)
{
    init_vnice();
    printf("=== DDR Intensive Test with Memory Constraints ===\n");
    printf("SPAD limit: 64KB per scratchpad, DRAM: 128MB\n");
    printf("Tile size: %dx%d = %d bytes (%.1fKB)\n", 
           TEST_TILE_ROWS, TEST_COLS, TEST_TILE_ROWS * TEST_COLS * 2, 
           (TEST_TILE_ROWS * TEST_COLS * 2) / 1024.0);
    printf("Total data: %dx%d = %.1fMB\n", 
           TEST_TOTAL_ROWS, TEST_COLS, (TEST_TOTAL_ROWS * TEST_COLS * 2) / (1024.0*1024.0));
    printf("Processing %d tiles x %d iterations = %d operations\n", 
           TEST_NUM_TILES, TEST_ITERATIONS, TEST_NUM_TILES * TEST_ITERATIONS);

    // DDR张量定义 - 确保不超过128MB限制
    Tensor tensor_ddr_src, tensor_ddr_dst, tensor_ddr_temp;
    
    // 源数据: 1024*128*2 = 256KB
    build_tensor(DRAM_ADDR, TEST_TOTAL_ROWS, TEST_COLS, TYPE_BF, WIDTH_16, &tensor_ddr_src);
    
    // 目标数据: 偏移256KB
    uint32_t offset_dst = TEST_TOTAL_ROWS * TEST_COLS * 2;  
    build_tensor(DRAM_ADDR + offset_dst, TEST_TOTAL_ROWS, TEST_COLS, TYPE_BF, WIDTH_16, &tensor_ddr_dst);
    
    // 临时数据: 偏移512KB  
    uint32_t offset_temp = offset_dst * 2;
    build_tensor(DRAM_ADDR + offset_temp, TEST_TOTAL_ROWS, TEST_COLS, TYPE_BF, WIDTH_16, &tensor_ddr_temp);

    // SPAD工作张量 - 每个8KB，远小于64KB限制
    Tensor spad_work1, spad_work2, spad_work3;
    build_tensor(SCRATCHPAD0_ADDR, TEST_TILE_ROWS, TEST_COLS, TYPE_BF, WIDTH_16, &spad_work1);
    build_tensor(SCRATCHPAD1_ADDR, TEST_TILE_ROWS, TEST_COLS, TYPE_BF, WIDTH_16, &spad_work2);  
    build_tensor(SCRATCHPAD2_ADDR, TEST_TILE_ROWS, TEST_COLS, TYPE_BF, WIDTH_16, &spad_work3);

    // NPU mask设置
    int npu_mask[MAX_MASK] = {0xf, 0xf, 0xf, 0xf}; // 使用所有核心

    // 向量操作配置
    VP_Option vp_add = { .special_case = { 0 }, .operation = OPERATION_ADD, .scalar_in2 = 0 };
    VP_Option vp_mul = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = 0x3f80 }; // 1.0 BF16
    VP_Option vp_sub = { .special_case = { 0 }, .operation = OPERATION_SUB, .scalar_in2 = 0 };
    VP_Option vp_scalar = { .special_case = { 0 }, .operation = OPERATION_ADD, .scalar_in2 = 0x3f00 }; // 0.5 BF16

    printf("\nStarting intensive DDR operations...\n");

    // 主测试循环 - 大量DDR读写操作
    for (int iter = 0; iter < TEST_ITERATIONS; iter++) {
        printf("Iteration %d/%d: ", iter + 1, TEST_ITERATIONS);
        
        for (int tile = 0; tile < TEST_NUM_TILES; tile++) {
            uint32_t start_row = tile * TEST_TILE_ROWS;
            
            // === 密集DDR读写序列 ===
            
            // 1. DDR -> SPAD0: 读取源数据
            slice_to_spad(&tensor_ddr_src, start_row, TEST_TILE_ROWS, 
                         SCRATCHPAD0_ADDR, npu_mask, &spad_work1);
            
            // 2. DDR -> SPAD1: 读取临时数据用于计算
            slice_to_spad(&tensor_ddr_temp, start_row, TEST_TILE_ROWS,
                         SCRATCHPAD1_ADDR, npu_mask, &spad_work2);
            
            // 3. 向量计算: work1 + work2 -> work3
            tensor_tensor_operator(&spad_work1, &spad_work2, &spad_work3, &vp_add, npu_mask);
            
            // 4. 向量计算: work3 * work2 -> work1 (in-place)
            tensor_tensor_operator(&spad_work3, &spad_work2, &spad_work1, &vp_mul, npu_mask);
            
            // 5. SPAD1 -> DDR: 中间结果写回
            slice_to_ddr(&spad_work1, start_row, TEST_TILE_ROWS, &tensor_ddr_dst, npu_mask);
            
            // 6. DDR -> SPAD0: 重新读取刚写入的数据
            slice_to_spad(&tensor_ddr_dst, start_row, TEST_TILE_ROWS,
                         SCRATCHPAD0_ADDR, npu_mask, &spad_work1);
            
            // 7. 向量计算: work1 - work3 -> work2
            tensor_tensor_operator(&spad_work1, &spad_work3, &spad_work2, &vp_sub, npu_mask);
            
            // 8. 标量计算: work2 + 0.5 -> work1
            tensor_scalar_operator(&spad_work2, &spad_work3, &spad_work1, &vp_scalar, npu_mask);
            
            // 9. SPAD1 -> DDR: 最终结果写回临时区域
            slice_to_ddr(&spad_work1, start_row, TEST_TILE_ROWS, &tensor_ddr_temp, npu_mask);
            
            // 10. 额外的读写验证操作
            slice_to_spad(&tensor_ddr_temp, start_row, TEST_TILE_ROWS,
                         SCRATCHPAD2_ADDR, npu_mask, &spad_work3);
            slice_to_ddr(&spad_work3, start_row, TEST_TILE_ROWS, &tensor_ddr_src, npu_mask);
        }
        
        printf("completed %d tiles\n", TEST_NUM_TILES);
    }

    // 统计输出
    uint32_t total_ddr_reads = TEST_ITERATIONS * TEST_NUM_TILES * 4;  // 每tile 4次读
    uint32_t total_ddr_writes = TEST_ITERATIONS * TEST_NUM_TILES * 3; // 每tile 3次写  
    uint32_t total_vector_ops = TEST_ITERATIONS * TEST_NUM_TILES * 4; // 每tile 4个向量操作
    uint32_t total_data_moved = (total_ddr_reads + total_ddr_writes) * TEST_TILE_ROWS * TEST_COLS * 2;
    
    printf("\n=== Test Completed Successfully ===\n");
    printf("Total DDR reads:  %d operations\n", total_ddr_reads);
    printf("Total DDR writes: %d operations\n", total_ddr_writes);
    printf("Total vector ops: %d operations\n", total_vector_ops);
    printf("Total data moved: %.2f MB\n", total_data_moved / (1024.0*1024.0));
    printf("Memory usage: src(256KB) + dst(256KB) + temp(256KB) = 768KB << 128MB\n");
    
    return 0;
}
