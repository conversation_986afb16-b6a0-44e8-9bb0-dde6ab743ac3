#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include "high_level.h"
#include "minicpmv_attn.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

int test_flashattn_prefill_tokenwise(void)
{
    init_vnice();

    // ---- Tensor shape ----
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t PROMPT_LEN = MINICPMV2_PROMPT_LEN;       // 720
    const uint32_t MAX_HEADS_PER_GROUP = 3;

    // 每行字节计算 (Q输入: [prompt_len, HEAD_DIM])
    const uint32_t Q_ROW_BYTES = HEAD_DIM * 2; // 64 * 2 = 128
    const uint32_t Q_STRIDE_256B = Q_ROW_BYTES / 32; // 4

    // O输出: [prompt_len*head_mapping, HEAD_DIM] = [720*3, 64] (每个核独立地址空间)
    const uint32_t O_ROW_BYTES = HEAD_DIM * 2; // 64 * 2 = 128
    const uint32_t O_STRIDE_256B = O_ROW_BYTES / 32; // 4

    // ---------- Create base addresses ----------
    const uint32_t DRAM_BASE_Q_INPUT      = DRAM_ADDR + 0x00000000;  // 0  MB offset
    const uint32_t DRAM_BASE_O_OUTPUT     = DRAM_ADDR + 0x00400000;  // 4  MB offset
    const uint32_t INTERMEMORY_BASE       = SCRATCHPAD2_ADDR;        // InterMemory在SPAD2

    // Q输入张量 [PROMPT_LEN, HEAD_DIM] = [720, 64] 在DRAM上
    Tensor tensor_q_input = {
        .base_addr      = DRAM_BASE_Q_INPUT,
        .dim0           = HEAD_DIM,
        .dim1           = PROMPT_LEN,
        .dim2           = 1,
        .byte_stride1_u = Q_STRIDE_256B * 32,
        .byte_stride2_u = Q_STRIDE_256B * 32 * PROMPT_LEN,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // O输出张量 [PROMPT_LEN*MAX_HEADS_PER_GROUP, HEAD_DIM] = [720*3, 64] 在DRAM上
    // 每个核有独立的地址空间，支持不同的head_mapping
    Tensor tensor_o_output = {
        .base_addr      = DRAM_BASE_O_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = PROMPT_LEN * MAX_HEADS_PER_GROUP,
        .dim2           = 1,
        .byte_stride1_u = O_STRIDE_256B * 32,
        .byte_stride2_u = O_STRIDE_256B * 32 * (PROMPT_LEN * MAX_HEADS_PER_GROUP),
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // ---------- 准备InterMemory (7个块，总计约4.4KB) ----------
    static InterMemory flash_mem[7];
    uint32_t base_addr = INTERMEMORY_BASE;

    // K/V/S 大块缓存 (3×1280B = 3840B)
    flash_mem[0].base_addr = base_addr;             flash_mem[0].byte_size = 1280;  // K_block
    flash_mem[1].base_addr = base_addr + 1280;      flash_mem[1].byte_size = 1280;  // K_block_T
    flash_mem[2].base_addr = base_addr + 2560;      flash_mem[2].byte_size = 1280;  // V_block

    // S/P 复用缓存 (32 bytes, 对齐)
    flash_mem[3].base_addr = base_addr + 3840;      flash_mem[3].byte_size = 32;    // S_block/P_block

    // O相关缓存 (2×128B = 256B)
    flash_mem[4].base_addr = base_addr + 3872;      flash_mem[4].byte_size = 128;   // O_accum
    flash_mem[5].base_addr = base_addr + 4000;      flash_mem[5].byte_size = 128;   // O_update

    // exp_v1 中间缓存 (1×128B = 128B)
    flash_mem[6].base_addr = base_addr + 4128;      flash_mem[6].byte_size = 128;   // exp缓存

    InterMemoryArray flash_intermemory = {
        .memory = flash_mem,
        .length = 7
    };

    // ---------- 调用FlashAttention Token-wise Prefill函数 ----------
    int layer_id = 0; // 测试第0层
    

    
    minicpmv2_flashattn_prefill_tokenwise(layer_id, &tensor_q_input, &tensor_o_output, &flash_intermemory);

 
    
    return 0;
}

