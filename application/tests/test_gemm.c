#include <stdio.h>
#include "hardware_inst_data.h"
#include "nuclei_sdk_soc.h"
#include "high_level.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}


int test_gemm(void)
{
    init_vnice();

    Tensor tensor_in1 = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 256,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 16 * 32, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF 
    };

    Tensor tensor_wt_cimc = (Tensor){
        .base_addr      = CIMC_PAGE_BASE_ADDR,
        .dim0           = 64,
        .dim1           = 256,
        .dim2           = 1,
        .byte_stride1_u = 32 * 1, 
        .byte_stride2_u = 32 * 1 * 256, 
        .width          = WIDTH_4, 
        .type           = TYPE_INT 
    };



    Tensor tensor_out   = (Tensor){
        .base_addr      = SCRATCHPAD2_ADDR,
        .dim0           = 64,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 4, 
        .byte_stride2_u = 32 * 4 * 32, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF
    };

    Tensor tensor_orig  = (Tensor){
        .base_addr      = SCRATCHPAD2_ADDR,
        .dim0           = 64,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 4, 
        .byte_stride2_u = 32 * 4 * 32, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF
    };


    CIM_Option cim_option = {
        .type           = TYPE_INT,
        .width          = WIDTH_4,
        .page_index     = 0,
        .accumulate     = 1,
        .activate       = 0
    };

    int npu_mask[MAX_MASK] = {0x5, 0, 0, 0xf};

    gemm(&tensor_in1, &tensor_out, &tensor_orig, &cim_option, npu_mask);

    return 0;
}
