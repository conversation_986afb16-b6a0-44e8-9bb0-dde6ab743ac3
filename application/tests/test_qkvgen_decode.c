#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include "high_level.h"
#include "minicpmv_attn.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

int test_qkvgen_decode(void)
{
    init_vnice();

    // ---- Tensor shape ----
    const uint32_t D = MINICPMV2_EMBEDDING_DIM;   // 2304
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t MAX_HEADS_PER_GROUP = 3;
    const uint32_t NUM_GROUPS = 4;
    const uint32_t MAX_HEADS_TOTAL = MAX_HEADS_PER_GROUP; // 3 (每次只处理一个group的head)

    // 每行字节计算
    const uint32_t X_ROW_BYTES = D * 2; // 2304 * 2 = 4608
    const uint32_t X_STRIDE_256B = X_ROW_BYTES / 32; // 144

    const uint32_t QKV_ROW_BYTES = HEAD_DIM * 2; // 64 * 2 = 128
    const uint32_t QKV_STRIDE_256B = QKV_ROW_BYTES / 32; // 4

    // ---------- Create base addresses ----------
    const uint32_t SPAD_BASE_X            = SCRATCHPAD0_ADDR;        // X在SPAD0
    const uint32_t SPAD_BASE_Q_OUTPUT     = SCRATCHPAD1_ADDR;        // Q在SPAD1
    const uint32_t SPAD_BASE_K_OUTPUT     = SCRATCHPAD2_ADDR;        // K在SPAD2
    const uint32_t SPAD_BASE_V_OUTPUT     = SCRATCHPAD3_ADDR;        // V在SPAD3
    const uint32_t INTERMEMORY_BASE       = SCRATCHPAD1_ADDR + 0x1000; // InterMemory在SPAD1偏移4KB

    // X输入张量 [1, D] = [1, 2304] 在SPAD上
    Tensor tensor_x_spad = {
        .base_addr      = SPAD_BASE_X,
        .dim0           = D,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = X_STRIDE_256B * 32,
        .byte_stride2_u = X_STRIDE_256B * 32,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // Q输出张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64] 在SPAD上
    Tensor tensor_q_output = {
        .base_addr      = SPAD_BASE_Q_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = QKV_STRIDE_256B * 32,
        .byte_stride2_u = QKV_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // K输出张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64] 在SPAD上
    Tensor tensor_k_output = {
        .base_addr      = SPAD_BASE_K_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = QKV_STRIDE_256B * 32,
        .byte_stride2_u = QKV_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // V输出张量 [MAX_HEADS_TOTAL, HEAD_DIM] = [3, 64] 在SPAD上
    Tensor tensor_v_output = {
        .base_addr      = SPAD_BASE_V_OUTPUT,
        .dim0           = HEAD_DIM,
        .dim1           = MAX_HEADS_TOTAL,
        .dim2           = 1,
        .byte_stride1_u = QKV_STRIDE_256B * 32,
        .byte_stride2_u = QKV_STRIDE_256B * 32 * MAX_HEADS_TOTAL,
        .width          = WIDTH_16,
        .type           = TYPE_FP
    };

    // ---- InterMemoryArray 设置 (需要10个块，总计1.25KB) ----
    static InterMemory decode_mem[10];
    uint32_t base_addr = INTERMEMORY_BASE;

    // cos/sin 缓存 (2个×128B)
    decode_mem[0].base_addr = base_addr;
    decode_mem[0].byte_size = 128;  // cos 缓存
    decode_mem[1].base_addr = base_addr + 128;
    decode_mem[1].byte_size = 128;  // sin 缓存

    // Q RoPE 中间缓存 (4个×128B)
    for(int i = 2; i < 6; i++) {
        decode_mem[i].base_addr = base_addr + i * 128;
        decode_mem[i].byte_size = 128;
    }

    // K RoPE 中间缓存 (4个×128B)
    for(int i = 6; i < 10; i++) {
        decode_mem[i].base_addr = base_addr + i * 128;
        decode_mem[i].byte_size = 128;
    }

    InterMemoryArray decode_intermemory = {
        .memory = decode_mem,
        .length = 10
    };

    // 调用QKV decode函数
    int seq_idx = 720; // decode阶段从prompt_len开始
    int layer_id = 0;  // 测试第0层
    
    minicpmv2_qkvgen_decode(seq_idx, layer_id, &tensor_x_spad, 
                           &tensor_q_output, &tensor_k_output, &tensor_v_output,
                           &decode_intermemory);

    printf("QKV Decode test completed for seq_idx=%d, layer_id=%d\n", seq_idx, layer_id);
    return 0;
} 