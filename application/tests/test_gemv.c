#include <stdio.h>
#include "nuclei_sdk_soc.h"
#include "high_level.h"


inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}


int test_gemv(void)
{

    init_vnice();

    Tensor tensor_in1 = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 4,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*1, 
        .width          = WIDTH_4, 
        .type           = TYPE_INT 
    };

    Tensor tensor_wt_dram = (Tensor){
        .base_addr      = DRAM_ADDR,
        .dim0           = 6,
        .dim1           = 4,
        .dim2           = 1,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*4, 
        .width          = WIDTH_4, 
        .type           = TYPE_INT 
    };

    Tensor tensor_wt_cimc = (Tensor){
        .base_addr      = CIMC_PAGE_BASE_ADDR,
        .dim0           = 6,
        .dim1           = 4,
        .dim2           = 1,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*4, 
        .width          = WIDTH_4, 
        .type           = TYPE_INT 
    };


    Tensor tensor_out   = (Tensor){
        .base_addr      = SCRATCHPAD2_ADDR,
        .dim0           = 6,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*1,
        .width          = WIDTH_32, 
        .type           = TYPE_INT 
    };

    Tensor tensor_orig  = (Tensor){
        .base_addr      = SCRATCHPAD2_ADDR,
        .dim0           = 6,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = 1*32, 
        .byte_stride2_u = 1*32*1,
        .width          = WIDTH_32, 
        .type           = TYPE_INT 
    };



    CIM_Option cim_option = {
        .type           = TYPE_INT,
        .width          = WIDTH_4,
        .page_index     = 0,
        .accumulate     = 1,
        .activate       = 0
    };


    int npu_mask[MAX_MASK] = {0x5, 0, 0, 0xf};

    load(&tensor_wt_dram, &tensor_wt_cimc, npu_mask);
    gemv(&tensor_in1, &tensor_out, &tensor_orig, &cim_option, npu_mask);

    return 0;
}
