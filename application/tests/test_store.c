#include <stdio.h>
#include "hardware_inst_data.h"
#include "nuclei_sdk_soc.h"
#include "high_level.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}


int test_store(void)
{
    init_vnice();

    Tensor tensor_dram = (Tensor){
        .base_addr      = DRAM_ADDR,
        .dim0           = 256,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 512, 
        .width          = WIDTH_16,
        .type           = TYPE_BF 
    };

    Tensor tensor_spad = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 256,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 512,
        .width          = WIDTH_16,
        .type           = TYPE_BF
    };


    int npu_mask[MAX_MASK] = {0x5, 0, 0, 0xf};

    store(&tensor_spad, &tensor_dram, npu_mask);

    return 0;
}
