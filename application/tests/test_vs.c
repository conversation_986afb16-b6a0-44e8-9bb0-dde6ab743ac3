#include <riscv_vector.h>
#include <stdio.h>
#include <string.h>
#include "hardware_inst_data.h"
#include "nice_inst_batch.h"
#include "nice_inst_single.h"
#include "vnice_inst_batch.h"
#include "nuclei_sdk_soc.h"
#include "high_level.h"
#include <riscv_vector.h>


inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}

#define IEEE754(i32) (*(float*)(&i32))

int test_vs()
{
    init_vnice();

    Tensor tensor_in1 = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 256,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 16, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF 
    };


    VP_Option vp_option = {
        .special_case.disable0 = 0, 
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation      = OPERATION_ADD, 
        .scalar_in2     = -1 // 1
    };


    int npu_mask[MAX_MASK] = {0xf, 0xf, 0xf, 0xf}; 
    uint32_t ret_vals[16] = {0};

    vp_config_reg.op_width_type.type_in1 = TYPE_BF;
    vp_config_reg.op_width_type.type_out = TYPE_BF;
    vp_config_reg.op_width_type.width_in1 = WIDTH_16;
    vp_config_reg.op_width_type.width_out = WIDTH_16;
    vp_config_reg.op_width_type.operation = OPERATION_ADD;

    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1 = 0;
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = 16;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1 = 0;
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = 16;

    vp_config_reg.special_case.disable0   = 0;
    vp_config_reg.special_case.saturate   = 0;
    vp_config_reg.special_case.round_mode = 0;

    vp_config_reg.base_addr_in1 = tensor_in1.base_addr;


    for(int group = 0; group < 4; group ++) {
        for(int core = 0; core < 4; core ++) {
            npu_group_mask_drv(group, 1 << (core));
            ret_vals[group * 4 + core] = v_s_cfg_pre_drv(
                vp_config_reg.op_width_type_u,
                vp_config_reg.size_dim0b_rem_dim0_in1_u,
                vp_config_reg.special_case_u,                
                vp_config_reg.base_addr_in1
            );
        }
    }



    printf("ret_vals = \n");
    for(int i = 0; i < 16; i++) {
        printf("0x%08x\n", ret_vals[i]);
    }
    printf("\n");
    

    return 0;
}

int test_vs_vnice() {
    init_vnice();
    Tensor tensor_in1 = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 256,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 16, 
        .width          = WIDTH_16, 
        .type           = TYPE_BF 
    };

    int npu_mask[MAX_MASK] = {0x5, 0xa, 0x5, 0xa}; 
    uint32_t ret_vals[16] = {0};

    vp_config_reg.op_width_type.type_in1 = TYPE_BF;
    vp_config_reg.op_width_type.type_out = TYPE_BF;
    vp_config_reg.op_width_type.width_in1 = WIDTH_16;
    vp_config_reg.op_width_type.width_out = WIDTH_16;
    vp_config_reg.op_width_type.operation = OPERATION_ADD;

    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1 = 0;
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = 16;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1 = 0;
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = 16;

    vp_config_reg.special_case.disable0   = 0;
    vp_config_reg.special_case.saturate   = 0;
    vp_config_reg.special_case.round_mode = 0;

    vp_config_reg.base_addr_in1 = tensor_in1.base_addr;

    for(int group = 0; group < 4; group ++) {
        npu_group_mask_drv(group, npu_mask[group]);
        v_v_s_cfg_pre_drv(
            vp_config_reg.op_width_type_u, 
            vp_config_reg.size_dim0b_rem_dim0_in1_u, 
            vp_config_reg.special_case_u,
            vp_config_reg.base_addr_in1,
            (uint32_t*)&ret_vals[group * MAX_MASK]
        );
    }

    printf("ret_vals = \n");
    for(int i = 0; i < 16; i++) {
        printf("0x%08x\n", ret_vals[i]);
    }
    printf("\n");
    
    return 0;
}
