#ifndef __MINICPMV_DEF_H__
#define __MINICPMV_DEF_H__

#include <stdint.h>

#define MINICPMV2_PROMPT_LEN 720
#define MINICPMV2_DECODE_LEM 150
#define MINICPMV2_CONTEXT_LEN ((MINICPMV2_PROMPT_LEN) + (MINICPMV2_DECODE_LEM))
#define MINICPMV2_EMBEDDING_DIM 2304
#define MINICPMV2_NUM_LAYERS 40

#define MINICPMV2_NUM_GROUPS 4
#define MINICPMV2_NUM_NODES 16

// describe weight slices in only one npu core
typedef struct __WeightElem {
    uint32_t addr_dram;
    // uint32_t dim0;
    // uint32_t dim1; 
    // uint32_t width; // 4, 8, 16 bit
} __WeightElem;

typedef struct __MINICPMV2_WEIGHT_Layer {
    struct {
        __WeightElem norm;
    } input_norm;
    struct {
        __WeightElem q_proj;
        __WeightElem k_proj;
        __WeightElem v_proj;
        __WeightElem o_proj;
        __WeightElem q_proj_scale;
        __WeightElem k_proj_scale;
        __WeightElem v_proj_scale;
        __WeightElem o_proj_scale;
        __WeightElem q_proj_zero;
        __WeightElem k_proj_zero;
        __WeightElem v_proj_zero;
        __WeightElem o_proj_zero;
    } self_attn;
    struct {
        __WeightElem norm;
    } post_attention_norm;
    struct {
        __WeightElem gu_proj; // col concat of g_proj and u_proj
        __WeightElem d_proj;
        __WeightElem gu_proj_scale;
        __WeightElem d_proj_scale;
        __WeightElem gu_proj_zero;
        __WeightElem d_proj_zero;
    } mlp;
} __MINICPMV2_WEIGHT_Layer;

typedef struct MINICPMV2_WEIGHT {
    struct {
         __MINICPMV2_WEIGHT_Layer layer[MINICPMV2_NUM_LAYERS];
        struct {
            __WeightElem norm;
            __WeightElem head;
            __WeightElem head_scale;
            __WeightElem head_zero;
        } lm_head;
        struct {
            __WeightElem cos; // [N+M, head_dim]
            __WeightElem sin; // [N+M, head_dim]
        } rope;
    } group[MINICPMV2_NUM_GROUPS]; // same in one group
} MINICPMV2_WEIGHT;


typedef struct {
    struct {
        uint32_t addr_dram;
    } group[MINICPMV2_NUM_GROUPS][MINICPMV2_NUM_LAYERS]; // same in one group
} MINICPMV2_KVCACHE;


// start of free space diffrent across groups in case of uneven distribution
// BUT FOR NOW, it's the same in all groups
typedef struct {
    struct {
        uint32_t addr_dram;
        uint32_t size; // in bytes
    } group[MINICPMV2_NUM_GROUPS];
} FREE_SPACE;

extern MINICPMV2_WEIGHT minicpmv2_weight;
extern MINICPMV2_KVCACHE minicpmv2_kcache;
extern MINICPMV2_KVCACHE minicpmv2_vcache;
extern FREE_SPACE minicpmv2_free_space;


#include <assert.h>
#ifdef DEBUG_ASSERT 
    #define debug_assert assert
#else
    #define debug_assert
#endif


#endif // __MINICPMV_DEF_H__