#ifndef __MINICPMV_LMHEAD_H__
#define __MINICPMV_LMHEAD_H__

#include "minicpmv_def.h"
#include "software_port_data.h"

// mapping strategy
// total: 122572 = 119*16*64 + 14*64 + 1
// use dummy to fill: 122800 = 120*16*64

#define MINICPMV2_LMHEAD_COL_NUM  (120 * 16 * 64)      
#define MINICPMV2_LMHEAD_COLpNODE ((MINICPMV2_LMHEAD_COL_NUM) / (MINICPMV2_NUM_NODES))


/// @brief minicpmv2中lmhead的实现
/// @param X_sram 输入一个token, shape=[1, d]
/// @param O_sram 词表长度的向量, 每个Node中 shape=[1, MINICPMV2_LMHEAD_COLpNODE]
void minicpmv2_lmhead(const Tensor* X_sram, const Tensor* O_sram);



#endif // __MINICPMV_LMHEAD_H__