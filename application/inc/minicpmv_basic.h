#ifndef __MINICPMV_BASIC_H__
#define __MINICPMV_BASIC_H__

#include "minicpmv_def.h"
#include "software_port_data.h"

/// @brief 利用NoC进行AllGather操作(两组输入必须放在不同SPAD上面); \n 要求node[n]上的输入行号为line[core_rank[n] + 1 % NUM_NODES], line[k] = (k%2 == 0) ? in1[k/2] : in2[k/2]
/// @param input1 在lmem上面的输入, shape=[num_cores/2, d]
/// @param input2 在lmem上面的输入, shape=[num_cores/2, d]
void AllGather(const Tensor* input1, const Tensor* input2);

/// @brief 利用NoC进行ReduceScatter操作(两组输入必须放在不同SPAD上面); \n node[n]上的结果行号为line[core_rank[n] + 1 % NUM_NODES], line[k] = (k%2 == 0) ? in1[k/2] : in2[k/2]
/// @param input1 在lmem上面的输入, shape=[num_cores/2, d]
/// @param input2 在lmem上面的输入, shape=[num_cores/2, d]
/// @param InterMemory 临时储存空间, shape=[1, d]需要两组, [0]与input1在一个SPAD, [1]与input2在另一个SPAD
void ReduceScatter(const Tensor* input1, const Tensor* input2, InterMemoryArray *InterMemory);


/// @brief 实现分布式的RMSNorm
/// @param input 输入X(shape=[N,d]), 位于所有核, *不允许修改*
/// @param output 输出Y=RMSNorm(X),  位于所有核
/// @param norm_weight norm权重, shape=[1,N], 位于所有核
/// @param InterMemory 临时储存空间,shape=[1, N], 位于所有核
void DistributedRMSNorm(const Tensor* input, const Tensor* output, const Tensor* norm_weight);


/// @brief 实现Allreduce+ResAdd+RMSNorm的融合
/// @param input 每个核持有一份部分和P1, P2, ..., shape=[N,d]
/// @param input_x 每个核持有一份用于ResAdd的X, shape=[N,d]
/// @param output ResAdd的结果, 相当于P1+P2+... + inputX
/// @param output_norm ResAdd+RMSNorm后的结果
/// @param norm_weight norm权重, shape=[1,N], 位于所有核
/// @param InterMemory 临时储存空间
void DistributedAddRMSNormAllReduce(const Tensor* input, const Tensor* input_x, const Tensor* output, const Tensor* output_norm, const Tensor* norm_weight, InterMemoryArray *dummy);


void slice_to_spad(const Tensor *tensor_gmem,
    uint32_t     start_row,
    uint32_t     rows,
    uint32_t     spad_base,
    int         *npu_mask,
    Tensor      *dst_local);

/// @brief 将SPAD上的张量切片写回DDR指定行区间
void slice_to_ddr(const Tensor *tensor_spad,
    uint32_t     start_row,
    uint32_t     rows,
    const Tensor *tensor_gmem,
    int         *npu_mask);

/// @brief 从块张量中派生出视图, 行数为rows, 行号为start_row
/// @param src_block 源块张量
/// @param start_row 起始行
/// @param rows 行数
/// @param dst_row 目标视图
void make_row_tensor(const Tensor *src_block, uint32_t start_row, uint32_t rows, Tensor *dst_row);

/// @brief Creates a 2D tensor view from a larger source tensor, supporting row and column offsets.
/// @param src_tensor The source tensor (in DRAM or SPAD).
/// @param start_row The starting row for the view.
/// @param start_col The starting column for the view (in elements, not bytes).
/// @param rows The number of rows in the view.
/// @param cols The number of columns in the view.
/// @param dst_view Pointer to the tensor structure that will hold the view info.
void make_tensor_view(const Tensor *src_tensor,
                      uint32_t start_row,
                      uint32_t start_col,
                      uint32_t rows,
                      uint32_t cols,
                      Tensor *dst_view);

/// @brief 根据 core_idx 生成只激活单核的 npu_mask
/// @param core_idx 核索引
/// @param mask 掩码数组
void make_single_core_mask(uint32_t core_idx, int* mask);


#endif // __MINICPMV_BASIC_H__