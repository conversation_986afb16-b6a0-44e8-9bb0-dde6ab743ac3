#ifndef __MINICPMV_ATTN_H__
#define __MINICPMV_ATTN_H__

#include "hardware_inst_data.h"
#include "high_level.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include "primitive.h"
#include "software_port_data.h"
#include <stdint.h>
#include <string.h>

// mapping strategy
#define CIMC_ROW4COL1 001
#define CIMC_ROW2COL2 010
#define CIMC_ROW1COL4 100



// head num in each group every layer
inline static int minicpmv2_head_maping(int group_id, int layer_id)
{
    //  0- 9 layer: 3 3 3 3  2 2 2 2  2 2 2 2  2 2 2 2
    // 10-19 layer: 2 2 2 2  3 3 3 3  2 2 2 2  2 2 2 2
    // 21-29 layer: 2 2 2 2  2 2 2 2  3 3 3 3  2 2 2 2
    // 30-39 layer: 2 2 2 2  2 2 2 2  2 2 2 2  3 3 3 3
    debug_assert(group_id < MINICPMV2_NUM_GROUPS);
    debug_assert(layer_id < MINICPMV2_NUM_LAYERS);

    int layer_group = layer_id / 10;

    return (layer_group == group_id) ? (3) : (2);
}

// start addr of k/vcache of specific layer in specific group
inline static uint32_t minicpmv2_kvcache_offset(int group_id, int layer_id)
{
    debug_assert(layer_id < MINICPMV2_NUM_LAYERS);
    debug_assert(group_id < MINICPMV2_NUM_GROUPS);

    uint32_t head_size = MINICPMV2_CONTEXT_LEN * (MINICPMV2_EMBEDDING_DIM / 36) * 2; // 2 for BF16, 36 heads
    uint32_t offset = 0;

    for (int i = 0; i < layer_id; ++i) {
        offset += minicpmv2_head_maping(group_id, i) * head_size;
    }
    return offset;
}

// helper: convert enum WIDTH_x to bits per element
static inline uint32_t width_enum_to_bits(uint32_t width_enum)
{
    switch (width_enum) {
    case WIDTH_4:
        return 4;
    case WIDTH_8:
        return 8;
    case WIDTH_16:
        return 16;
    case WIDTH_32:
        return 32;
    default:
        return 8; // sensible default
    }
}

static inline void minicpmv2_reduce_max(const Tensor* in, int* npu_mask, uint32_t* reduce_max_return)
{
    VP_Option vp_option = {
        .special_case = { 0 },
        .operation = OPERATION_MAX,
        .scalar_in2 = 0
    };
    v_v_s_primitive((Tensor*)in, (Tensor*)in, &vp_option, npu_mask, reduce_max_return);
}

// Helper function: extract single effective value from reduce_max result based on one-hot npu_mask
static inline uint32_t minicpmv2_reduce_max_single(const Tensor* in, int* npu_mask)
{
    uint32_t reduce_max_return[16] = { 0 };
    minicpmv2_reduce_max(in, npu_mask, reduce_max_return);

    // Find the active core index from one-hot mask
    for (int g = 0; g < 4; ++g) {
        if (npu_mask[g] != 0) {
            for (int c = 0; c < 4; ++c) {
                if (npu_mask[g] & (1u << c)) {
                    int core_idx = g * 4 + c;
                    return reduce_max_return[core_idx];
                }
            }
        }
    }
    return 0; // fallback, should not reach here
}

// Helper function: reduce_sum with single effective value return
static inline uint32_t minicpmv2_reduce_sum_single(const Tensor* in, int* npu_mask)
{
    uint32_t reduce_sum_return[16] = { 0 };
    VP_Option vp_option = {
        .special_case = { 0 },
        .operation = OPERATION_ADD,
        .scalar_in2 = 0
    };
    v_v_s_primitive((Tensor*)in, (Tensor*)in, &vp_option, npu_mask, reduce_sum_return);

    // Find the active core index from one-hot mask
    for (int g = 0; g < 4; ++g) {
        if (npu_mask[g] != 0) {
            for (int c = 0; c < 4; ++c) {
                if (npu_mask[g] & (1u << c)) {
                    int core_idx = g * 4 + c;
                    return reduce_sum_return[core_idx];
                }
            }
        }
    }
    return 0; // fallback, should not reach here
}

static inline float bf16_to_float32(uint16_t in_bf16) // 1 sign + 8 exp + 7 mant (高 16 位)
{
    uint32_t tmp = ((uint32_t)in_bf16) << 16; // 对齐到 IEEE754 高 16 位
    float out;
    memcpy(&out, &tmp, 4);
    return out;
}


// Helper: determine head pairing type for quantization grouping
static inline int get_head_pair_type(int layer_id, int core_idx)
{
    int group_id = core_idx / 4;
    int heads_per_core = minicpmv2_head_maping(group_id, layer_id);
    if (heads_per_core != 3)
        return 0; // 2head情况总是前两个头为一组

    int core_in_group = core_idx % 4;
    if (core_in_group == 0 || core_in_group == 2) {
        return 0; // 前两个head为一组 (head0+head1)
    } else { // core_in_group == 1 || core_in_group == 3
        return 1; // 后两个head为一组 (head1+head2)
    }
}

// Helper: get scale index for current head group
static inline int get_scale_index(int layer_id, int core_idx, int head_iter, int use_dual_opt)
{
    int group_id = core_idx / 4;
    int heads_per_core = minicpmv2_head_maping(group_id, layer_id);

    if (heads_per_core == 2) {
        return 0; // 2个head总是使用scale_index=0
    }

    if (heads_per_core == 3) {
        int pair_type = get_head_pair_type(layer_id, core_idx);
        if (use_dual_opt) {
            // 双head优化时，根据pair_type确定scale_index
            return pair_type; // 0 or 1
        } else {
            // 单head时，根据head_iter和pair_type确定
            if (pair_type == 0) {
                return (head_iter == 2) ? 1 : 0; // head2使用scale_index=1
            } else {
                return (head_iter == 0) ? 0 : 1; // head0使用scale_index=0
            }
        }
    }

    return 0;
}

// Helper: apply dequantization fp_val = scale * (quantized_val - zero_point)
static inline void apply_dequantization(const Tensor* quantized_tensor, const Tensor* scale_tensor, const Tensor* zero_tensor,
    const Tensor* output_tensor, int* npu_mask)
{
    // 实现反量化：output = scale * (quantized - zero_point)
    // quantized_tensor: [TILE_N, 2304], scale_tensor: [1, 2304], zero_tensor: [1, 2304]
    // 需要逐行处理：对每行 output[i] = scale * (quantized[i] - zero_point)
    
    VP_Option vp_sub = { .special_case = { 0 }, .operation = OPERATION_SUB, .scalar_in2 = 0 };
    VP_Option vp_mul = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = 0 };

    uint32_t num_rows = quantized_tensor->dim1;  // TILE_N
    uint32_t num_cols = quantized_tensor->dim0;  // 2304
    
    // 逐行处理反量化
    for (uint32_t row = 0; row < num_rows; ++row) {
        // 获取当前行的视图
        Tensor quantized_row, output_row;
        make_tensor_view(quantized_tensor, row, 0, 1, num_cols, &quantized_row);  // [1, 2304]
        make_tensor_view((Tensor*)output_tensor, row, 0, 1, num_cols, &output_row);  // [1, 2304]
        
        // 当前行反量化：temp = quantized_row - zero_tensor
        sub(&quantized_row, (Tensor*)zero_tensor, &output_row, &vp_sub, npu_mask);
        // output_row = temp * scale_tensor  
        mul(&output_row, (Tensor*)scale_tensor, &output_row, &vp_mul, npu_mask);
    }
}


// Added helper: generate group-level npu mask (activate all 4 cores in the chosen group)
static inline void make_group_mask(int group_idx, int* mask)
{
    for (int g = 0; g < 4; ++g) {
        mask[g] = (g == group_idx) ? 0xf : 0x0; // 0xf -> 4 cores active
    }
}

// Helper: compute size (bytes) of one head slice [CONTEXT, HEAD_DIM]
static inline uint32_t head_slice_bytes()
{
    const uint32_t BYTES_PER_ELEM = 2; // BF16
    return (uint32_t)MINICPMV2_CONTEXT_LEN * (MINICPMV2_EMBEDDING_DIM / 36) * BYTES_PER_ELEM; // 720+150
}

// Helper: build a Tensor given base, rows, cols, width/type (BF16, 16bit)
static inline void build_tensor(uint32_t base_addr, uint32_t rows, uint32_t cols, uint32_t type, uint32_t width, Tensor* t)
{
    uint32_t size_dim0a = 256/width_enum_to_bits(width);
    uint32_t size_dim0b = (cols+size_dim0a-1)/size_dim0a;
    t->base_addr = base_addr;
    t->dim0 = cols; // dim0 : col
    t->dim1 = rows; // dim1 : row
    t->dim2 = 1;
    t->byte_stride1_u = 32 * size_dim0b; // contiguous rows
    t->byte_stride2_u = t->byte_stride1_u * rows;
    t->width = width;
    t->type = type;
}

static inline void build_cimc_tensor(uint32_t base_addr, uint32_t rows, uint32_t cols, uint32_t type, uint32_t width, uint32_t cimc_layout, Tensor* t)
{
    t->base_addr = base_addr;
    t->dim0 = cols; // dim0 : col
    t->dim1 = rows; // dim1 : row
    t->dim2 = 1;
    t->width = width;
    t->type = type;
    
    // 根据CIMC layout类型设置固定的stride值，与dim0,dim1无关
    switch (cimc_layout) {
        case CIMC_ROW4COL1:
            // ROW4COL1 [256, 256bit] = [256, 32bytes]
            t->byte_stride1_u = 32;  // 每行32字节
            t->byte_stride2_u = 32 * 256;  // 总共256行
            break;
            
        case CIMC_ROW2COL2:
            // ROW2COL2 [128, 512bit] = [128, 64bytes]
            t->byte_stride1_u = 64;  // 每行64字节
            t->byte_stride2_u = 64 * 128;  // 总共128行
            break;
            
        case CIMC_ROW1COL4:
            // ROW1COL4 [64, 1024bit] = [64, 128bytes]
            t->byte_stride1_u = 128;  // 每行128字节
            t->byte_stride2_u = 128 * 64;  // 总共64行
            break;
            
        default:
            // 默认使用ROW4COL1布局
            t->byte_stride1_u = 32;
            t->byte_stride2_u = 32 * 256;
            break;
    }
}

// // Helper function: 应用causal mask到S_chunk矩阵
// static inline void apply_causal_mask_to_chunk(Tensor* S_chunk, uint32_t chunk_q_idx, uint32_t chunk_k_idx,
//     uint32_t chunk_size, int* npu_mask)
// {
//     const uint16_t NEG_INF_BF16 = 0xFF80; // -∞ 在BF16中的表示

//     // 只有当chunk_q_idx == chunk_k_idx时才需要应用causal mask (同一块内的因果关系)
//     if (chunk_q_idx == chunk_k_idx) {
//         // 对上三角部分设置为-∞
//         VP_Option vp_mask = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };

//         // 简化实现：逐元素设置mask (实际硬件实现可能有更高效的方法)
//         for (uint32_t i = 0; i < chunk_size; i++) {
//             for (uint32_t j = i + 1; j < chunk_size; j++) {
//                 // S_chunk[i][j] = -∞ (i < j的上三角部分)
//                 // 这里需要具体的tensor元素设置函数，暂时用注释表示逻辑
//                 // set_tensor_element(S_chunk, i, j, NEG_INF_BF16);
//             }
//         }
//     }
// }

// // Helper function: 计算矩阵每行的最大值
// static inline void compute_row_max(const Tensor* S_chunk, uint32_t chunk_size, float* row_max_vals, int* npu_mask)
// {
//     // 对每一行计算最大值
//     for (uint32_t i = 0; i < chunk_size; i++) {
//         Tensor row_view;
//         make_tensor_view(S_chunk, i, 0, 1, chunk_size, &row_view);
//         uint32_t max_bf16 = minicpmv2_reduce_max_single(&row_view, npu_mask);
//         row_max_vals[i] = bf16_to_float32(max_bf16);
//     }
// }

// // Helper function: 计算矩阵每行经过exp和减去max后的和
// static inline void compute_row_exp_sum(const Tensor* P_chunk, uint32_t chunk_size, float* row_sum_vals, int* npu_mask)
// {
//     // 对每一行计算exp后的和
//     for (uint32_t i = 0; i < chunk_size; i++) {
//         Tensor row_view;
//         make_tensor_view(P_chunk, i, 0, 1, chunk_size, &row_view);
//         uint32_t sum_bf16 = minicpmv2_reduce_sum_single(&row_view, npu_mask);
//         row_sum_vals[i] = bf16_to_float32(sum_bf16);
//     }
// }

/// @brief MiniCPMV2中的 qkvgen阶段(prefill). k/v_proj结果储存到KCache和VCache中, 不另外设置输出
/// @param layer_id 层号, 0~39
/// @param X_dram  输入X(shape=[N,d]), 储存在dram上
/// @param Q_out   Q_proj结果保存到q_out,  shape=[heads_mapping*N  , d/num_heads]
void minicpmv2_qkvgen_prefill(int layer_id, const Tensor* X_dram, const Tensor* Q_out);

/// @brief MiniCPMV2中的 qkvgen阶段(decode)
/// @param seq_idx  decode token index 从prompt_len开始计数
/// @param layer_id 层号, 0~39
/// @param X_spad  输入X(shape=[1,d]), 储存在spad上
/// @param q_out   q_proj结果保存到q_out,  shape=[heads_mapping*1, d/num_heads]
/// @param k_out   k_proj结果保存到k_out,  shape=[heads_mapping*1, d/num_heads]
/// @param v_vout  v_proj结果保存到v_vout, shape=[heads_mapping*1, d/num_heads]
void minicpmv2_qkvgen_decode(int seq_idx, int layer_id, const Tensor* X_spad, const Tensor* q_out, const Tensor* k_out, const Tensor* v_vout, InterMemoryArray* intermemory);

/// @brief MiniCPMV2 QKV Generation Decode with INT4 Quantization Support
/// @param seq_idx 当前序列位置索引 (从720开始，即decode阶段)
/// @param layer_id 层号, 0~39
/// @param X_spad 输入X(shape=[1,2304]), 单token输入，已储存在spad上  
/// @param q_out Q_proj结果保存到q_out, shape=[heads_total*1, 64]
/// @param k_out K_proj结果保存到k_out, shape=[heads_total*1, 64]  
/// @param v_out V_proj结果保存到v_out, shape=[heads_total*1, 64]
/// @param intermemory 中间计算缓存数组，需要16个块，约8KB (比prefill小)
void minicpmv2_qkvgen_decode_quantized(
    int seq_idx,
    int layer_id, 
    const Tensor* X_spad, 
    const Tensor* q_out,
    const Tensor* k_out,
    const Tensor* v_out,
    InterMemoryArray* intermemory);

/// @brief MiniCPMV2 QKV Generation Prefill with INT4 Quantization Support
/// @param layer_id 层号, 0~39
/// @param X_dram 输入X(shape=[720,2304]), 储存在dram上  
/// @param Q_out Q_proj结果保存到q_out, shape=[heads_total*720, 64]
/// @param intermemory 中间计算缓存数组，需要19个块，约18KB
void minicpmv2_qkvgen_prefill_quantized(
    int layer_id, 
    const Tensor* X_dram, 
    const Tensor* Q_out,
    InterMemoryArray* intermemory);

void minicpmv2_flashattn_decode(int seq_idx,
    int layer_id,
    const Tensor* Q_spad, // [head_mapping, HEAD_DIM] 已在 SPAD
    const Tensor* O_out, // [heads_per_layer*1 , HEAD_DIM] 目标地址 (DRAM)
    InterMemoryArray* intermemory);

/// @brief MiniCPMV2中的 FlashAttention阶段(prefill). 基于Online-Softmax算法实现内存高效的自注意力计算
/// @param layer_id 层号, 0~39
/// @param Q_dram  查询张量(shape=[prompt_len, HEAD_DIM]), 储存在dram上
/// @param O_out   注意力输出张量 shape=[prompt_len, HEAD_DIM] 目标地址 (DRAM)
/// @param intermemory 中间计算缓存数组，至少12个块，约12KB
void minicpmv2_flashattn_prefill(int layer_id,
    const Tensor* Q_dram, // [prompt_len, HEAD_DIM] 在 DRAM
    const Tensor* O_out, // [prompt_len, HEAD_DIM] 目标地址 (DRAM)
    InterMemoryArray* intermemory);

/// @brief MiniCPMV2中的 FlashAttention Token-wise Prefill 实现. 逐token处理，天然满足因果性
/// @param layer_id 层号, 0~39
/// @param Q_dram  查询张量(shape=[prompt_len, HEAD_DIM]), 储存在dram上
/// @param O_out   注意力输出张量 shape=[prompt_len*head_mapping, HEAD_DIM] 目标地址 (DRAM)
/// @param intermemory 中间计算缓存数组，至少7个块，约4.4KB
void minicpmv2_flashattn_prefill_tokenwise(int layer_id,
    const Tensor* Q_dram, // [prompt_len, HEAD_DIM] 在 DRAM
    const Tensor* O_out, // [prompt_len*head_mapping, HEAD_DIM] 目标地址 (DRAM)
    InterMemoryArray* intermemory);

/// @brief MiniCPMV2中的 output projection 计算(prefill). 对attention结果进行o_proj线性变换
/// @param layer_id 层号, 0~39
/// @param attn_out attention结果 shape=[heads_per_core * prompt_len, head_dim] 在 DRAM
/// @param output_final 最终输出 shape=[prompt_len, 2304] 目标地址 (DRAM)
/// @param intermemory 中间计算缓存数组，至少4个块，约21KB
void minicpmv2_output_gen_prefill(int layer_id, 
    const Tensor* attn_out, 
    const Tensor* output_final, 
    InterMemoryArray* intermemory);

/// @brief MiniCPMV2中的 output projection 计算(decode). 对单token attention结果进行o_proj线性变换
/// @param layer_id 层号, 0~39
/// @param attn_out attention结果 shape=[heads_per_core, head_dim] 在 SPAD上
/// @param output_final 最终输出 shape=[1, 2304] 在 SPAD上
/// @param intermemory 中间计算缓存数组，至少4个块，约10KB
/// @note seq_idx不需要，因为output projection是位置无关的线性变换
void minicpmv2_output_gen_decode(int layer_id,
    const Tensor* attn_out,
    const Tensor* output_final,
    InterMemoryArray* intermemory);




#endif // __MINICPMV_ATTN_H__