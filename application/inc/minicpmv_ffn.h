#ifndef __MINICPMV_FFN_H__
#define __MINICPMV_FFN_H__

#include "minicpmv_def.h"
#include "software_port_data.h"

// mapping strategy

// ffn col_num in each core for each layer 
inline static int minicpmv2_ffn_mapping(int core_id, int layer_id) {
    // 0- 9 layer:    6   6   6   6   5.5 5.5 5.5 5.5   5.5 5.5 5.5 5.5   5.5 5.5 5.5 5.5
    // 10-19 layer: 5.5 5.5 5.5 5.5     6   6   6   6   5.5 5.5 5.5 5.5   5.5 5.5 5.5 5.5
    // 20-29 layer: 5.5 5.5 5.5 5.5   5.5 5.5 5.5 5.5     6   6   6   6   5.5 5.5 5.5 5.5
    // 30-39 layer: 5.5 5.5 5.5 5.5   5.5 5.5 5.5 5.5   5.5 5.5 5.5 5.5     6   6   6   6
    debug_assert(core_id < MINICPMV2_NUM_NODES);
    debug_assert(layer_id < MINICPMV2_NUM_LAYERS);

    int layer_group = layer_id / 10; // 0, 1, 2, 3
    int core_group = core_id / 4;    // 0, 1, 2, 3
    return (layer_group == core_group) ? (384) : (352); // 6 * 64 : 5.5 * 64
}


/// @brief 处理简单切分矩阵乘法. 尺寸中的d1可以被256整除, d2可以被64整除
/// @param X shape=[N,  d1], BF16, dram
/// @param W shape=[d1, d2], INT4, dram
/// @param O shape=[N,  d2], BF16, dram
void tiled_mm(const Tensor* X, const Tensor* W, Tensor* O);


/// @brief minicpmv2中的g_proj和u_proj以及SiLU和EWiseMul过程(prefill)
/// @param layer_id 层号, 0-39
/// @param X_dram 输入X, shape=[N, d]
/// @param gu_out gate_proj和up_proj映射, 对gate_proj执行SiLU, 并EWiseMul后的输出, shape=[N, num_cols]
void minicpmv2_gu_proj_prefill(int layer_id, const Tensor* X_dram, const Tensor* gu_out);


/// @brief minicpmv2中的g_proj和u_proj以及SiLU和EWiseMul过程(decode)
/// @param layer_id 层号, 0-39
/// @param X_dram 输入X, shape=[1, d]
/// @param gu_out gate_proj和up_proj映射, 对gate_proj执行SiLU, 并EWiseMul后的输出, shape=[1, num_cols]
void minicpmv2_gu_proj_decode(int layer_id, const Tensor* X_spad, const Tensor* gu_out);


/// @brief minicpmv2中d_proj过程(prefill)
/// @param layer_id 层号, 0-39
/// @param gu_out gate_proj和up_proj映射, 对gate_proj执行SiLU, 并EWiseMul后的输出, shape=[N, num_cols]
/// @param d_out d_proj映射后的部分和输出, shape=[N, d]
void minicpmv2_d_proj_prefill(int layer_id, const Tensor* gu_out, const Tensor* d_out);

/// @brief minicpmv2中d_proj过程(decode)
/// @param layer_id 层号, 0-39
/// @param gu_out gate_proj和up_proj映射, 对gate_proj执行SiLU, 并EWiseMul后的输出, shape=[1, num_cols]
/// @param d_out d_proj映射后的部分和输出, shape=[1, d]
void minicpmv2_d_proj_decode(int layer_id, const Tensor* gu_out, const Tensor* d_out);





#endif // __MINICPMV_ATTN_H__