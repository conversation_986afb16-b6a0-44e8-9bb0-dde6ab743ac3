/*
 * high_level.c
 *
 *  Created on: 2025年7月7日
 *      Author: zqguo
 */

#include <assert.h>
#include <stdbool.h>
#include <string.h>
#include "primitive.h"
#include "high_level.h"
#include "hardware_inst_data.h"
#include "type_utils_c.h"

//Elementwise Operation
void not(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    uint32_t negative = 0;
    if (tensor_in->type == TYPE_INT){
        negative = 0xffffffff;}
    else if (tensor_in->type == TYPE_FP){
        if (tensor_in->width == WIDTH_32){
            negative = 0xbf800000;}
        else if (tensor_in->width == WIDTH_16){
            negative = 0xbc00;}
    }
    else { 
        negative = 0xbf80;}
//vp_option->operation = OPERATION_XOR;
    vp_option->scalar_in2 = negative;
    mul_tensor_scalar(tensor_in, tensor_in, tensor_out, vp_option, npu_mask);
};

void neg(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    uint32_t negative = 0;
    if (tensor_in->type == TYPE_INT){
        negative = 0xffffffff;}
    else if (tensor_in->type == TYPE_FP){
        if (tensor_in->width == WIDTH_32){
            negative = 0xbf800000;}
        else if (tensor_in->width == WIDTH_16){
            negative = 0xbc00;}
    }
    else { 
        negative = 0xbf80;}
//vp_option->operation = OPERATION_MUL
    vp_option->scalar_in2 = negative;
    mul_tensor_scalar(tensor_in, tensor_in, tensor_out, vp_option, npu_mask);
};

void cast(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    v_v_primitive_cfg(tensor_in, tensor_out, vp_option, npu_mask);
    uint32_t temp_base_in, temp_base_out;
    for(int dim2 = 0; dim2 < tensor_in->dim2; dim2++){
        temp_base_in  = tensor_in->base_addr + dim2 * tensor_in->byte_stride2_u;
        temp_base_out = tensor_out->base_addr + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < tensor_in->dim1; dim1++){
            v_v_primitive_drv(temp_base_in, temp_base_out, npu_mask);        
            temp_base_in  += tensor_in->byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
}



//Matrix MAC Operation
void gemm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask){
    gemm_primitive(tensor_in, tensor_out, tensor_orig, cim_option, npu_mask);
}
void gemv(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *conv_option, int *npu_mask){
    gemv_primitive(tensor_in, tensor_out, tensor_orig, conv_option, npu_mask);
}

//RoPE
void rope_llama(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_sin, Tensor *tensor_cos, InterMemoryArray *intermemory, VP_Option *vp_option, int *npu_mask){
    //InterMemory Init
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr;
    Tensor temp_tensor2 = *tensor_in; 
    temp_tensor2.base_addr = intermemory->memory[1].base_addr;
    Tensor temp_tensor3 = {
        .base_addr = intermemory->memory[2].base_addr,
        .dim0 = tensor_in->dim1,
        .dim1 = tensor_in->dim0,
        .dim2 = tensor_in->dim2,
        .byte_stride1_u =  (tensor_in->dim1+(256>>tensor_in->width)-1)/(256>>tensor_in->width) * 32,
        .byte_stride2_u = ((tensor_in->dim1+(256>>tensor_in->width)-1)/(256>>tensor_in->width)) * tensor_in->dim0 * 32,
        .width = tensor_in->width,
        .type = tensor_in->type
    };
    Tensor temp_tensor4 = temp_tensor3;
    temp_tensor4.base_addr = intermemory->memory[3].base_addr;
    //negative one init
    uint32_t negative_one = 0;
    if (tensor_in->type == TYPE_INT){
        negative_one = 0xffffffff;}
    else if (tensor_in->type == TYPE_FP){
        if (tensor_in->width == WIDTH_32){
            negative_one = 0xbf800000;}
        else if (tensor_in->width == WIDTH_16){
            negative_one = 0xbc00;}
    }
    else { 
        negative_one = 0xbf80;}
    
    //positive one init
    uint32_t positive_one = 0;
    if (tensor_in->type == TYPE_INT){
        positive_one = 0x1;}
    else if (tensor_in->type == TYPE_FP){
        if (tensor_in->width == WIDTH_32){
            positive_one = 0x3f800000;}
        else if (tensor_in->width == WIDTH_16){
            positive_one = 0x3c00;}
    }
    else { 
        positive_one = 0x3f80;}

    //x * cos
    mul(tensor_in, tensor_cos, &temp_tensor1, vp_option, npu_mask);
    //sin
    transpose(tensor_in, &temp_tensor3, npu_mask);
    vp_option->operation = OPERATION_MUL;
    uint32_t temp_base_out = 0, temp_base_in  = 0;
    vs_v_primitive_cfg(&temp_tensor3, &temp_tensor3, &temp_tensor4, vp_option, npu_mask);
    for(int dim1 = 0; dim1 < temp_tensor1.dim1; dim1 ++){
        if(dim1 % 2 ==0){//偶数
            temp_base_in  = temp_tensor3.base_addr +  dim1 * temp_tensor3.byte_stride1_u;
            temp_base_out = temp_tensor4.base_addr +  (dim1 + 1) * temp_tensor4.byte_stride1_u;
            vs_v_primitive_pre(temp_base_in, positive_one, npu_mask);
            vs_v_primitive_drv(temp_base_out, npu_mask);
        }  
        else{//奇数
            temp_base_in  = temp_tensor3.base_addr +  dim1 * temp_tensor3.byte_stride1_u;
            temp_base_out = temp_tensor4.base_addr +  (dim1 - 1) * temp_tensor4.byte_stride1_u;
            vs_v_primitive_pre(temp_base_in, negative_one, npu_mask);
            vs_v_primitive_drv(temp_base_out, npu_mask);
        }
    }
    transpose(&temp_tensor4, &temp_tensor2, npu_mask);
    mul(&temp_tensor2, tensor_sin, &temp_tensor2, vp_option, npu_mask);
    //sin + cos
    add(&temp_tensor1, &temp_tensor2, tensor_out, vp_option, npu_mask);
}


void rope_minicpm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_sin, Tensor *tensor_cos, InterMemoryArray *intermemory, VP_Option *vp_option, int *npu_mask){
    //InterMemory Init
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr; 
    uint32_t bytes_offset  = tensor_in->byte_stride1_u/2;
    uint32_t temp_dim0     = tensor_in->dim0;
    uint32_t temp_in_addr  = tensor_in->base_addr;

    tensor_in->dim0 = temp_dim0/2;
    tensor_in->base_addr = temp_in_addr + bytes_offset;
    temp_tensor1.dim0 = temp_dim0/2;
    neg(tensor_in, &temp_tensor1, vp_option, npu_mask);
    tensor_in->base_addr = temp_in_addr;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr + bytes_offset;
    mov(tensor_in, &temp_tensor1, npu_mask);

    temp_tensor1.dim0 = temp_dim0;
    tensor_in->dim0 = temp_dim0;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr;    
    mul(tensor_in, tensor_cos, tensor_cos, vp_option, npu_mask);
    mul(&temp_tensor1, tensor_sin, tensor_sin, vp_option, npu_mask);
    add(tensor_cos, tensor_sin, tensor_out, vp_option, npu_mask);
}


//Normalization
void rmsnorm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_scale, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask){
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr;
    Tensor temp_tensor2 = *tensor_in;
    temp_tensor2.base_addr = intermemory->memory[1].base_addr;

    mul(tensor_in, tensor_in, &temp_tensor1, vp_option, npu_mask);
    v_vs_v_primitive_cfg(tensor_in, tensor_in, tensor_out, vp_option, npu_mask);

    uint32_t temp_base_in, temp_base_out;
    uint32_t var_scalar_16[16] = {0};
    float var_scale_fp32 = 1.0 / tensor_in->dim0;

    for(int dim2 = 0; dim2 < temp_tensor1.dim2; dim2++){
        temp_base_in  = temp_tensor1.base_addr + dim2 * temp_tensor1.byte_stride2_u;
        temp_base_out = temp_tensor2.base_addr + dim2 * temp_tensor2.byte_stride2_u;
        for(int dim1 = 0; dim1 < temp_tensor1.dim1; dim1++){
            vp_cfg_primitive_op(OPERATION_ADD, npu_mask);
            v_v_s_primitive_drv(temp_base_in, npu_mask, var_scalar_16);
            for(int id = 0; id < NPU_CORES; id++){
                float var_mean_fp32_orig = get_float32_from_uint32_t(var_scalar_16[id], tensor_in->type, tensor_in->width);
                float var_mean_fp32 = 1/sqrtf(var_mean_fp32_orig * var_scale_fp32);    
                var_scalar_16[id] = (tensor_in->width == WIDTH_32) ? (*(uint32_t*)&var_mean_fp32)     :
                                (tensor_in->type == TYPE_FP) ? float32_to_float16(var_mean_fp32)      :
                                float32_to_bf16(var_mean_fp32);
            }
            vp_cfg_primitive_op(OPERATION_MUL, npu_mask);
            v_vs_v_primitive_pre(temp_base_in, var_scalar_16, npu_mask);
            v_vs_v_primitive_drv(temp_base_out, npu_mask);
            temp_base_in  += temp_tensor1.byte_stride1_u;
            temp_base_out += temp_tensor2.byte_stride1_u;
        }
    }
    mul_tensor_vector(&temp_tensor2, tensor_scale, tensor_out, vp_option, npu_mask);  
}
void layernorm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_scale, Tensor *tensor_offset, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask){
    uint32_t deta = 0;
    if (tensor_in->type == TYPE_FP){
        if (tensor_in->width == WIDTH_32){
            deta = 0x3727c5ac;}
        else if (tensor_in->width == WIDTH_16){
            deta = 0x00a8;}
    } else { 
        deta = 0x3728;
    } 
 
 
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr;
    Tensor temp_tensor2 = *tensor_in;
    temp_tensor2.base_addr = intermemory->memory[1].base_addr;

    vp_option->operation = OPERATION_SUB;
    v_vs_v_primitive_cfg(tensor_in, tensor_in, &temp_tensor1, vp_option, npu_mask);

    uint32_t temp_base_in1, temp_base_in2, temp_base_out;
    uint32_t var_scalar_16[16] = {0};
    float var_scale_fp32 = 1.0 / tensor_in->dim0;
//x-mean
    for(int dim2 = 0; dim2 < tensor_in->dim2; dim2++){
        temp_base_in1 = tensor_in->base_addr   + dim2 * tensor_in->byte_stride2_u;
        temp_base_out = temp_tensor1.base_addr + dim2 * temp_tensor1.byte_stride2_u;
        for(int dim1 = 0; dim1 < tensor_in->dim2; dim1++){
            vp_cfg_primitive_op(OPERATION_ADD, npu_mask);
            v_v_s_primitive_drv(temp_base_in1, npu_mask, var_scalar_16);
            for(int id = 0; id < NPU_CORES; id++){
                float var_mean_fp32_orig = get_float32_from_uint32_t(var_scalar_16[id], tensor_in->type, tensor_in->width);
                float var_mean_fp32 = var_mean_fp32_orig * var_scale_fp32;    //需重新考虑
                var_scalar_16[id] = (tensor_in->width == WIDTH_32) ? (*(uint32_t*)&var_mean_fp32)       :
                                (tensor_in->type == TYPE_FP) ? float32_to_float16(var_mean_fp32)     :
                                float32_to_bf16(var_mean_fp32);
            }
            vp_cfg_primitive_op(OPERATION_SUB, npu_mask);
            v_vs_v_primitive_pre(temp_base_in1, var_scalar_16, npu_mask);
            v_vs_v_primitive_drv(temp_base_out, npu_mask);
            temp_base_in1  += tensor_in->byte_stride1_u;
            temp_base_out += temp_tensor1.byte_stride1_u;
        }
    }
//(x-mean)^2+deta
    mul(&temp_tensor1, &temp_tensor1, &temp_tensor2, vp_option, npu_mask);
    vp_option->scalar_in2 = deta;
    add_tensor_scalar(&temp_tensor2, &temp_tensor2, tensor_out, vp_option, npu_mask);
//sqrt((x-mean)^2+deta)
    vp_option->operation = OPERATION_MUL;
    v_vs_v_primitive_cfg(tensor_in, tensor_in, tensor_out, vp_option, npu_mask);

    temp_base_in1  = 0;
    temp_base_out = 0;
    var_scale_fp32 = 1.0 / tensor_in->dim0;

    for(int dim2 = 0; dim2 < temp_tensor1.dim2; dim2++){
        temp_base_in1 = tensor_out->base_addr   + dim2 * tensor_out->byte_stride2_u;
        temp_base_in2 = temp_tensor1.base_addr  + dim2 * temp_tensor1.byte_stride2_u;
        temp_base_out = temp_tensor2.base_addr  + dim2 * temp_tensor2.byte_stride2_u;
        for(int dim1 = 0; dim1 < temp_tensor1.dim1; dim1++){
            vp_cfg_primitive_op(OPERATION_ADD, npu_mask);
            v_v_s_primitive_drv(temp_base_in1, npu_mask, var_scalar_16);
            for(int id = 0; id < NPU_CORES; id++){
                float var_mean_fp32 = 1/sqrtf((*(float*)&var_scalar_16[id])*var_scale_fp32);
                var_scalar_16[id] = (tensor_in->width == WIDTH_32) ? (*(uint32_t*)&var_mean_fp32)     :
                                (tensor_in->type == TYPE_FP) ? float32_to_float16(var_mean_fp32)      :
                                float32_to_bf16(var_mean_fp32);
            }
            v_vs_v_primitive_pre(temp_base_in2, var_scalar_16, npu_mask);
            v_vs_v_primitive_drv(temp_base_out, npu_mask);          
            temp_base_in1 += tensor_out->byte_stride1_u;
            temp_base_in2 += temp_tensor1.byte_stride1_u;
            temp_base_out += temp_tensor2.byte_stride1_u;
        }
    }
    mul_tensor_vector(&temp_tensor2, tensor_scale, &temp_tensor1, vp_option, npu_mask);  
    add_tensor_vector(&temp_tensor1, tensor_offset, tensor_out, vp_option, npu_mask);  

}

//DeQuantization Operation
void dequant_gemm_group(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, Tensor *tensor_wt, int offset_en, CIM_Option *cim_option, VP_Option *vp_option, Tensor *scale_out_fp, Tensor *offset_wt_int, Tensor *offset_in_int, int *npu_mask){
    uint32_t cimc_addr_mode = 0;
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = page_addr(cim_option);
    if(tensor_in->dim0 == 64){
        cimc_addr_mode = CIMC_MODE_ROW1_COL4;
    } else if(tensor_in->dim0 == 128){
        cimc_addr_mode = CIMC_MODE_ROW2_COL2;
    } else if(tensor_in->dim0 == 256){
        cimc_addr_mode = CIMC_MODE_ROW4_COL1;
    } else {
        assert(0 && "Unsupported group quantization dimension");
    }

    switch_cimc_primitive(temp_tensor1.base_addr, cimc_addr_mode, npu_mask);
    if (tensor_in->type == TYPE_INT){
        add_tensor_vector(tensor_in, offset_wt_int, tensor_in, vp_option, npu_mask);
    }
    if (offset_en){
        add_tensor_vector(tensor_wt, offset_wt_int, tensor_wt, vp_option, npu_mask);
        mov(tensor_wt, &temp_tensor1, npu_mask);
    } else {
        load(tensor_wt, &temp_tensor1, npu_mask);
    }
    gemm(tensor_in, tensor_out, tensor_orig, cim_option, npu_mask); 
    cast(scale_out_fp, offset_wt_int, vp_option, npu_mask);//scale fp/bf16; out fp32，占据offset_wt_int的地址，数还是scale的数
    mul_tensor_vector(tensor_out, offset_wt_int, tensor_out, vp_option, npu_mask);
}

//Data transfer Operation
void load(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask){
    tld_primitive(tensor_gmem, tensor_lmem, npu_mask);
}
void trans_load(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask){
    ttld_primitive(tensor_gmem, tensor_lmem, npu_mask);
}
void store(Tensor *tensor_lmem, Tensor *tensor_gmem, int *npu_mask){
    tst_primitive(tensor_lmem, tensor_gmem, npu_mask);
}

//Shape
void transpose(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    trans_primitive(tensor_in, tensor_out, npu_mask);
}
void mov(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    mov_primitive(tensor_in, tensor_out, npu_mask);
}
void broadcast(uint32_t scalar_in, Tensor *tensor_out, int *npu_mask){
    bc_primitive(scalar_in, tensor_out, npu_mask);
}

void v_broadcast(uint32_t *scalar_in_16, Tensor *tensor_out, int *npu_mask){
    v_bc_primitive(scalar_in_16, tensor_out, npu_mask);
}

void permute(Tensor *tensor_in, Tensor *tensor_out, int dim_axis, InterMemoryArray *intermemory, int *npu_mask){
    if (dim_axis == 102) {
        transpose(tensor_in, tensor_out, npu_mask);
    } else if (dim_axis == 021) {
        permute_021(tensor_in, tensor_out, npu_mask);
    } else if (dim_axis == 120) {
        permute_120(tensor_in, tensor_out, intermemory, npu_mask);
    } else if (dim_axis == 201) {
        permute_201(tensor_in, tensor_out, intermemory, npu_mask);
    } else if (dim_axis == 210) {
        permute_210(tensor_in, tensor_out, intermemory, npu_mask);
    } else {
        assert(0 && "Unsupported permutation type");
    }
}

void concat(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, int dim_axis, int *npu_mask){
    uint32_t temp_dim0_out = tensor_out->dim0;
    uint32_t temp_dim1_out = tensor_out->dim1;
    uint32_t temp_dim2_out = tensor_out->dim2;
    uint32_t temp_base_out = tensor_out->base_addr;
    if (dim_axis == 0){
        tensor_out->dim0 = tensor_in1->dim0;
        mov_primitive(tensor_in1, tensor_out, npu_mask);
        tensor_out->base_addr = tensor_out->base_addr + tensor_in1->byte_stride1_u;
        tensor_out->dim0 = tensor_in2->dim0;
        mov_primitive(tensor_in2, tensor_out, npu_mask);
        tensor_out->dim0 = temp_dim0_out;
    } else if (dim_axis == 1){
        tensor_out->dim1 = tensor_in1->dim1;
        mov_primitive(tensor_in1, tensor_out, npu_mask);
        tensor_out->base_addr = tensor_out->base_addr + tensor_in1->byte_stride2_u;
        tensor_out->dim1 = tensor_in2->dim1;
        mov_primitive(tensor_in2, tensor_out, npu_mask); 
        tensor_out->dim1 = temp_dim1_out;
    } else if (dim_axis == 2){
        tensor_out->dim2 = tensor_in1->dim2;
        mov_primitive(tensor_in1, tensor_out, npu_mask);
        tensor_out->base_addr = tensor_out->base_addr + tensor_in1->byte_stride2_u * tensor_in1->dim2;
        tensor_out->dim2 = tensor_in2->dim2;
        mov_primitive(tensor_in2, tensor_out, npu_mask); 
        tensor_out->dim2 = temp_dim2_out;
    } else {
        assert(0 && "Unsupported concat dimension");
    }
    tensor_out->base_addr = temp_base_out;
}



void expand(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask){
    bool expand_d0 = (tensor_in->dim0 == 1 && tensor_out->dim0 > 1);
    bool expand_d1 = (tensor_in->dim1 == 1 && tensor_out->dim1 > 1);
    bool expand_d2 = (tensor_in->dim2 == 1 && tensor_out->dim2 > 1);
 
    // 143->343: 只扩展dim0
    if (expand_d0 && !expand_d1 && !expand_d2){
        expand_dim0(tensor_in, tensor_out, npu_mask);
    }
    // 141->343: 只扩展dim1
    if (!expand_d0 && expand_d1 && !expand_d2){
        expand_dim1(tensor_in, tensor_out, npu_mask);
    }
    // 341->343: 只扩展dim2
    if (!expand_d0 && !expand_d1 && expand_d2){
        expand_dim2(tensor_in, tensor_out, npu_mask);
    }
    // 113->343: 扩展dim0和dim1
    if (expand_d0 && expand_d1 && !expand_d2){
        // 先扩展dim1 (113->143)
        Tensor temp_tensor1 = {
            .base_addr = intermemory->memory[0].base_addr,
            .dim0 = tensor_in->dim0,
            .dim1 = tensor_out->dim1,
            .dim2 = tensor_in->dim2,
            .byte_stride1_u = tensor_in->byte_stride1_u * tensor_out->dim1, 
            .byte_stride2_u = tensor_in->byte_stride2_u,
            .width = tensor_in->width,
            .type = tensor_in->type
        };    
        expand_dim1(tensor_in, &temp_tensor1, npu_mask);    
        // 再扩展dim0 (143->343)
        expand_dim0(&temp_tensor1, tensor_out, npu_mask);
    }   
    // 311->343: 扩展dim1和dim2
    if (!expand_d0 && expand_d1 && expand_d2) {
        // 先扩展dim2 (311->313)
        Tensor temp_tensor1 = {
            .base_addr = intermemory->memory[0].base_addr,
            .dim0 = tensor_in->dim0,
            .dim1 = tensor_in->dim1,
            .dim2 = tensor_out->dim2,
            .byte_stride1_u = tensor_in->byte_stride1_u, 
            .byte_stride2_u = tensor_in->byte_stride2_u * tensor_out->dim2,
            .width = tensor_in->width,
            .type = tensor_in->type
        };   
        expand_dim2(tensor_in, &temp_tensor1, npu_mask);
        // 再扩展dim1 (313->343)
        expand_dim1(&temp_tensor1, tensor_out, npu_mask);
    }
    // 131->343: 扩展dim0和dim2
    if (expand_d0 && !expand_d1 && expand_d2) {
        // 先扩展dim2 (131->133)
        Tensor temp_tensor1 = {
            .base_addr = intermemory->memory[0].base_addr,
            .dim0 = tensor_in->dim0,
            .dim1 = tensor_in->dim1,
            .dim2 = tensor_out->dim2,
            .byte_stride1_u = tensor_in->byte_stride1_u, 
            .byte_stride2_u = tensor_in->byte_stride2_u * tensor_out->dim2,
            .width = tensor_in->width,
            .type = tensor_in->type
        };  
        expand_dim2(tensor_in, &temp_tensor1, npu_mask);
        // 再扩展dim0 (133->343)
        expand_dim0(&temp_tensor1, tensor_out, npu_mask);
    }
    // 111->343: 扩展所有维度
    if (expand_d0 && expand_d1 && expand_d2) {
        // 第一步：扩展dim2 (111->113)
        Tensor temp_tensor1 = {
            .base_addr = intermemory->memory[0].base_addr,
            .dim0 = tensor_in->dim0,
            .dim1 = tensor_in->dim1,
            .dim2 = tensor_out->dim2,
            .byte_stride1_u = tensor_in->byte_stride1_u, 
            .byte_stride2_u = tensor_in->byte_stride2_u * tensor_out->dim2,
            .width = tensor_in->width,
            .type = tensor_in->type
        }; 
        expand_dim2(tensor_in, &temp_tensor1, npu_mask);
        // 第二步：扩展dim1 (113->143)
        Tensor temp_tensor2 = {
            .base_addr = intermemory->memory[1].base_addr,
            .dim0 = tensor_in->dim0,
            .dim1 = tensor_out->dim1,
            .dim2 = tensor_out->dim2,
            .byte_stride1_u = tensor_in->byte_stride1_u * tensor_out->dim1, 
            .byte_stride2_u = tensor_in->byte_stride2_u,
            .width = tensor_in->width,
            .type = tensor_in->type
        }; 
        expand_dim1(&temp_tensor1, &temp_tensor2, npu_mask);
        // 第三步：扩展dim0 (143->343)
        expand_dim0(&temp_tensor2, tensor_out, npu_mask);
    }
}


void flatten(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    uint32_t flatten_dim = tensor_in->dim0 * tensor_in->dim1 * tensor_in->dim2;
    tensor_out->dim0 = flatten_dim;
    tensor_out->dim1 = 1;
    tensor_out->dim2 = 1;
}


//Index  
void reducemean_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    reducesum_npu(tensor_in, tensor_out, vp_option, npu_mask);

    float mean_scale_fp32 = 1.0 / tensor_in->dim0;
    uint32_t mean_scale_uint32 = get_uint32_t_from_float32(mean_scale_fp32, tensor_in->type, tensor_in->width);
    vp_option->scalar_in2 = mean_scale_uint32;

    mul_tensor_scalar(tensor_out, tensor_out, tensor_out, vp_option, npu_mask);
}

void reducemean_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask){
    v_v_s_primitive(tensor_in, tensor_out, vp_option, npu_mask, reduce_return_16);
    float mean_scale_fp32 = 1.0 / tensor_in->dim0;
    float reduce_return_16_fp32 = 0;

    for(int id = 0; id < NPU_CORES; id++){
        reduce_return_16_fp32 = get_float32_from_uint32_t(reduce_return_16[id], tensor_in->type, tensor_in->width);
        reduce_return_16_fp32 = reduce_return_16_fp32 / mean_scale_fp32;
        reduce_return_16[id] = get_uint32_t_from_float32(mean_scale_fp32, tensor_in->type, tensor_in->width);
    }
}




//Activation Operation
//Nonlinear 
void exp_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask){
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr;
    uint32_t a1 = 0x000065c5;
    uint32_t b1 = 0x0000737a;
    uint32_t r1 = 0x0000773a;
    uint32_t a2 = 0x4b38aa3b;
    uint32_t b2 = 0x4e7de9a3;
    uint32_t r2 = 0x7ef477da;

    uint32_t a = 0, b = 0, r = 0;
    if(tensor_in->width == WIDTH_16){
        a = a1;
        b = b1;
    }else if(tensor_in->width == WIDTH_32){
        a = a2;
        b = b2;}

    vp_option->scalar_in2 = a;
    mul_tensor_scalar(tensor_in, tensor_in, &temp_tensor1, vp_option, npu_mask);
    vp_option->scalar_in2 = b;
    add_tensor_scalar(&temp_tensor1, tensor_in, tensor_in, vp_option, npu_mask);
    tensor_out->type = TYPE_INT;
    cast(tensor_in, tensor_out, vp_option, npu_mask);
    tensor_out->type = TYPE_FP;

}

//Activation
void silu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask){
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[4].base_addr;

    sigmoid_v1(tensor_in, &temp_tensor1, vp_option, intermemory, npu_mask);
    mul(&temp_tensor1, tensor_in, tensor_out, vp_option, npu_mask);
}
void softmax(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask){
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[1].base_addr;


    v_vs_v_primitive_cfg(tensor_in, tensor_in, tensor_out, vp_option, npu_mask);
//x-max
    uint32_t temp_base_in1, temp_base_in2, temp_base_out;
    uint32_t var_scalar_16[16] = {0};
    for(int dim2 = 0; dim2 < temp_tensor1.dim2; dim2++){
        temp_base_in1 = tensor_in->base_addr  + dim2 * tensor_in->byte_stride2_u;
        temp_base_out = tensor_out->base_addr + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < temp_tensor1.dim1; dim1++){
            vp_cfg_primitive_op(OPERATION_MAX, npu_mask);
            v_v_s_primitive_drv(temp_base_in1, npu_mask, var_scalar_16);
            vp_cfg_primitive_op(OPERATION_SUB, npu_mask);
            v_vs_v_primitive_pre(temp_base_in1, var_scalar_16, npu_mask);
            v_vs_v_primitive_drv(temp_base_out, npu_mask);
            temp_base_in1 += tensor_in->byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
    exp_v1(tensor_out, &temp_tensor1, vp_option, intermemory, npu_mask);
//sum(x-max)
    float sum_scale_fp32 = 1.0 / tensor_in->dim0;
    for(int dim2 = 0; dim2 < temp_tensor1.dim2; dim2++){
        temp_base_in1 = temp_tensor1.base_addr + dim2 * temp_tensor1.byte_stride2_u;
        temp_base_in2 = temp_tensor1.base_addr + dim2 * temp_tensor1.byte_stride2_u;
        temp_base_out = tensor_out->base_addr  + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < temp_tensor1.dim1; dim1++){
            vp_cfg_primitive_op(OPERATION_ADD, npu_mask);
            v_v_s_primitive_drv(temp_base_in1, npu_mask, var_scalar_16);
            for(int id = 0; id < NPU_CORES; id++){
                float var_mean_fp32 = 1/((*(float*)&var_scalar_16[id])*sum_scale_fp32);
                var_scalar_16[id] = (tensor_in->width == WIDTH_32) ? (*(uint32_t*)&var_mean_fp32)     :
                                (tensor_in->type == TYPE_FP) ? float32_to_float16(var_mean_fp32)      :
                                float32_to_bf16(var_mean_fp32);
            }
            vp_cfg_primitive_op(OPERATION_MUL, npu_mask);
            v_vs_v_primitive_pre(temp_base_in2, var_scalar_16, npu_mask);
            v_vs_v_primitive_drv(temp_base_out, npu_mask);          
            temp_base_in1 += temp_tensor1.byte_stride1_u;
            temp_base_in2 += temp_tensor1.byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
}

void mask_softmax(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask){
        Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[1].base_addr;

    uint32_t temp_in_dim0 = tensor_in->dim0;
    uint32_t temp_out_dim0 = tensor_out->dim0;

    v_vs_v_primitive_cfg(tensor_in, tensor_in, tensor_out, vp_option, npu_mask);
//x-max
    uint32_t temp_base_in1, temp_base_in2, temp_base_out;
    uint32_t var_scalar_16[16] = {0};
    temp_base_in1 = tensor_in->base_addr;
    temp_base_out = tensor_out->base_addr;

    for(int dim1 = 0; dim1 < temp_tensor1.dim1; dim1++){
        tensor_in->dim0  = 1 + dim1;
        tensor_out->dim0 = 1 + dim1;
        vp_cfg_primitive_dim0(tensor_in, npu_mask);
        vp_cfg_primitive_dim0(tensor_out, npu_mask);

        vp_cfg_primitive_op(OPERATION_MAX, npu_mask);
        v_v_s_primitive_drv(temp_base_in1, npu_mask, var_scalar_16);
        vp_cfg_primitive_op(OPERATION_SUB, npu_mask);
        v_vs_v_primitive_pre(temp_base_in1, var_scalar_16, npu_mask);
        v_vs_v_primitive_drv(temp_base_out, npu_mask);
        temp_base_in1 += tensor_in->byte_stride1_u;
        temp_base_out += tensor_out->byte_stride1_u;
    }

    exp_v1(tensor_out, &temp_tensor1, vp_option, intermemory, npu_mask);
//sum(x-max)
    temp_base_in1 = temp_tensor1.base_addr;
    temp_base_in2 = temp_tensor1.base_addr;
    temp_base_out = tensor_out->base_addr;
    for(int dim1 = 0; dim1 < temp_tensor1.dim1; dim1++){
        temp_tensor1.dim0  = 1 + dim1;
        tensor_out->dim0 = 1 + dim1;
        vp_cfg_primitive_dim0(&temp_tensor1, npu_mask);
        vp_cfg_primitive_dim0(tensor_out, npu_mask);

        vp_cfg_primitive_op(OPERATION_ADD, npu_mask);
        v_v_s_primitive_drv(temp_base_in1, npu_mask, var_scalar_16);
        float sum_scale_fp32 = 1.0 / tensor_in->dim0;
        for(int id = 0; id < NPU_CORES; id++){
            float var_mean_fp32 = 1/((*(float*)&var_scalar_16[id])*sum_scale_fp32);
            var_scalar_16[id] = (tensor_in->width == WIDTH_32) ? (*(uint32_t*)&var_mean_fp32)     :
                            (tensor_in->type == TYPE_FP) ? float32_to_float16(var_mean_fp32)      :
                            float32_to_bf16(var_mean_fp32);
        }
        vp_cfg_primitive_op(OPERATION_MUL, npu_mask);
        v_vs_v_primitive_pre(temp_base_in2, var_scalar_16, npu_mask);
        v_vs_v_primitive_drv(temp_base_out, npu_mask);          
        temp_base_in1 += temp_tensor1.byte_stride1_u;
        temp_base_in2 += temp_tensor1.byte_stride1_u;
        temp_base_out += tensor_out->byte_stride1_u;
    }

}

void allreduce(Tensor *tensor_in, Tensor *tensor_out, int num_blocks, InterMemoryArray *intermemory, VP_Option *vp_option, uint32_t *dest_idx_16, uint32_t *src_idx_16, uint32_t *dest_addr_16, uint32_t *src_addr_16){
// 初始化地址模运算环形
    uint32_t buffer_memory[16] = {0};
    uint32_t dest_addr_out_16[16] = {0};
    int npu_mask[4] = {0};
    uint32_t addr_byte_offset = (tensor_in->byte_stride2_u * tensor_in->dim2 + num_blocks -1) / num_blocks;//要求硬件整除
    uint32_t addr_byte_range  = addr_byte_offset * num_blocks;
    uint32_t src_addr_16_base = tensor_in->base_addr;
    uint32_t src_addr_16_end  = tensor_in->base_addr + addr_byte_offset * (num_blocks -1);
    uint32_t tensor_in_temp   = tensor_in->dim0;
    uint32_t tensor_out_temp  = tensor_out->dim0;

// 保存初始地址数组
    uint32_t src_addr_16_temp[16] = {0};
    memcpy(src_addr_16_temp, src_addr_16, sizeof(src_addr_16));
// 初始化buffer_memory
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[0].base_addr;
    for (int i = 0; i < NPU_CORES; i++) { 
        if(src_addr_16[i] != 0){
            buffer_memory[i] = intermemory->memory[0].base_addr;
        }
    }
    for (int i = 0; i < NPU_CORES; i++) { 
        if(src_addr_16[i] != 0){
            dest_addr_out_16[i] = tensor_out->base_addr;
        }
    }
// Reduce-Scatter
// 主循环：执行num_block次数据移动
    tensor_in->dim0  = tensor_in_temp / num_blocks;
    tensor_out->dim0 = tensor_out_temp / num_blocks;
    for(int nums = 0; nums < num_blocks; nums++){
        // 更新每个有效地址
        for(int i =0; i < (NPU_CORES-1); i++){
            if(src_addr_16[i] != 0){
                // 更新src_addr_16为环形回绕后的地址
                src_addr_16[i] = src_addr_16_base + (src_addr_16_temp[i] - src_addr_16_base + addr_byte_offset * nums) % addr_byte_range;
                } else {
                    src_addr_16[i] = 0;  // 保持无效地址为0
            }
        }
        v_intercore_mov(tensor_in, dest_idx_16, src_idx_16, buffer_memory, src_addr_16);
        //由于需要reduce的npu cores数据切块处于不同的地址
        for(int i =0; i < NPU_CORES; i++){
            if(src_addr_16[i] != 0){
                uint32_t npu_group = (src_idx_16[i] >> 8) & 0xFF;
                npu_mask[npu_group] = (npu_group << 16) | (1 << (src_idx_16[i] & 0xFF));
                tensor_in->base_addr = src_addr_16[i];
                tensor_tensor_operator(tensor_in, &temp_tensor1, tensor_in, vp_option, npu_mask);
            }
        }
    }
// Reduce-Gather
// 更新reduce-gather的src地址，此时gather的起始地址为循环的最后一个地址
    memcpy(src_addr_16_temp, src_addr_16, sizeof(src_addr_16));
    for(int nums = 0; nums < num_blocks; nums++){
        // 更新每个有效地址
        for(int i =0; i < NPU_CORES; i++){
            if(src_addr_16[i] != 0){
                // 更新src_addr_16为环形回绕后的地址
                src_addr_16[i] = src_addr_16_base + (src_addr_16_temp[i] - src_addr_16_base + addr_byte_offset * nums) % addr_byte_range;
                } else {
                    src_addr_16[i] = 0;  // 保持无效地址为0
            }
        }
        v_intercore_mov(tensor_in, dest_idx_16, src_idx_16, dest_addr_out_16, src_addr_16);
    }
}


void v_intercore_mov(Tensor *tensor_in, uint32_t *dest_idx_16, uint32_t *src_idx_16, uint32_t *dest_addr_16, uint32_t *src_addr_16){
    int npu_mask[MAX_GROUP] = {0};
    int dest_mask[MAX_GROUP] = {0};
    int src_mask[MAX_GROUP] = {0};
    uint32_t noc_return_16[NPU_CORES] = {0};
    int all_zero = 1;

    for (int i = 0; i < NPU_CORES; i++) {
        uint32_t dest_group_id = (dest_idx_16[i] >> 8) & 0xFF;
        uint32_t dest_core_id = dest_idx_16[i] & 0xFF;
        uint32_t src_group_id = (src_idx_16[i] >> 8) & 0xFF;
        uint32_t src_core_id = src_idx_16[i] & 0xFF;
        if (dest_group_id < 4) {
            dest_mask[dest_group_id] |= (dest_group_id << 16);
            if (dest_core_id < 4) {
                dest_mask[dest_group_id] |= (1 << dest_core_id);
            }  
        }
        if (src_group_id < 4) {
            src_mask[src_group_id] |= (src_group_id << 16);
            if (src_core_id < 4) {
                src_mask[src_group_id] |= (1 << src_core_id);
            }
        }
    }

    for (int i = 0; i < MAX_GROUP; i++){
        npu_mask[i] = dest_mask[i] | src_mask[i];
    } 
    
    v_noc_primitive_fence_drv(noc_return_16, npu_mask);
    for (int i = 0; i < NPU_CORES; i++){
        if (noc_return_16[i] != 0){  
            all_zero = 0;   
            break;           
        } 
    }
    
    v_noc_primitive_cfg(tensor_in, npu_mask);
    if(all_zero){
        v_noc_primitive_dest_drv(src_addr_16, dest_idx_16, dest_mask);
        v_noc_primitive_src_drv(dest_addr_16, src_idx_16, src_mask);
    }
}




/**
 * 辅助函数：从NPU掩码数组中获取所有活跃的NPU ID
 * @param npu_mask: NPU掩码数组，每个元素代表一个group中的活跃NPU
 * @param active_npus: 输出数组，存储活跃的NPU ID
 * @return: 活跃NPU的数量
 */
static int get_active_npus(int *npu_mask, uint32_t *active_npus) {
    int num_active_npus = 0;
    for (int group = 0; group < MAX_GROUP; group++) {
        for (int i = 0; i < MAX_MASK; i++) {
            if ((npu_mask[group] >> i) & 1) {
                uint32_t npu_id = group * MAX_MASK + i;
                if (npu_id < NPU_CORES) {
                    active_npus[num_active_npus++] = npu_id;
                }
            }
        }
    }
    return num_active_npus;
}

void arguemax(Tensor *tensor_in, Tensor *argmax_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask, uint32_t *max_index, uint32_t *max_value)
{
    // 检查输入参数
    assert(tensor_in != NULL && argmax_out != NULL);
    assert(vp_option != NULL && intermemory != NULL);
    assert(intermemory->length >= 1);  // 需要1个中间内存块
    assert(npu_mask != NULL);
    assert(tensor_in->dim1 == 1 && tensor_in->dim2 == 1);  // 确保是一维向量
    
    // 1. 找到活跃的NPU及其数量
    uint32_t active_npus[NPU_CORES];
    int num_active_npus = get_active_npus(npu_mask, active_npus);

    if (num_active_npus <= 0) {
        return;  // 没有活跃NPU
    }

    // 2. 使用reduce_scalar_max找到每个核心上的局部最大值
    uint32_t local_max_values[NPU_CORES] = {0};
    reducemax_rv(tensor_in, tensor_in, vp_option, local_max_values, npu_mask);
    
    // 3. 找到全局最大值和对应的核心ID
    uint32_t global_max = local_max_values[active_npus[0]];
    int max_core_idx = 0;
    
    for (int i = 1; i < num_active_npus; i++) {
        uint32_t current_npu_id = active_npus[i];
        uint32_t current_max = local_max_values[current_npu_id];
        
        // 使用type_utils_c.h中的函数进行比较
        uint32_t comparison_result = type_based_operation(tensor_in->type, tensor_in->width, OP_MAX, global_max, current_max);
        
        if (comparison_result == current_max) {
            global_max = current_max;
            max_core_idx = i;
        }
    }

    // 4. 只在包含全局最大值的核心上查找argmax
    uint32_t max_npu_id = active_npus[max_core_idx];
    
    // 创建单核掩码
    int single_core_mask[MAX_GROUP] = {0};
    int group = max_npu_id / MAX_MASK;
    single_core_mask[group] = 1 << (max_npu_id % MAX_MASK);
    
    // 创建比较结果张量
    Tensor compare_result = *tensor_in;
    compare_result.base_addr = intermemory->memory[0].base_addr;
    compare_result.dim0 = 1;
    compare_result.dim1 = 1;
    compare_result.dim2 = 1;
    
    // 创建全局最大值张量用于比较
    Tensor global_max_tensor = compare_result;
    broadcast(global_max, &global_max_tensor, single_core_mask);
    
    // 5. 使用二分法在包含最大值的核心上查找第一个等于全局最大值的元素
    uint32_t base_offset = max_core_idx * tensor_in->dim0;  // 该核心在全局索引中的起始偏移
    
    // 使用二分法查找最大值的位置
    uint32_t left = 0;
    uint32_t right = tensor_in->dim0 - 1;
    uint32_t found_index = base_offset;  // 默认第一个位置
    
    while (left < right) {
        uint32_t mid = (left + right) / 2;  // 修复：添加括号确保正确的运算顺序
        
        // 创建左半区间张量 [left, mid]
        int left_len = mid - left + 1;
        Tensor left_range = *tensor_in;
        
        // 修复：安全的地址计算，考虑字节对齐
        uint32_t element_size = get_type_size(tensor_in->type, tensor_in->width) / 8;
        uint32_t byte_offset = (uint32_t)left * element_size;
        left_range.base_addr = tensor_in->base_addr + byte_offset;
        left_range.dim0 = left_len;
        left_range.dim1 = 1;
        left_range.dim2 = 1;
        
        // 计算左半区间的最大值
        Tensor left_max_result = compare_result;  // 重用compare_result内存
        VP_Option max_vp_option = *vp_option;
        max_vp_option.operation = OPERATION_MAX;
        reducemax_npu(&left_range, &left_max_result, &max_vp_option, single_core_mask);
        
        // 比较左半区间最大值是否等于全局最大值
        VP_Option eq_vp_option = *vp_option;
        eq_vp_option.operation = OPERATION_EQUAL;
        equal(&left_max_result, &global_max_tensor, &compare_result, &eq_vp_option, single_core_mask);
        
        // 使用reduce_scalar_max从compare_result中读取比较结果
        uint32_t comparison_results[NPU_CORES] = {0};
        VP_Option reduce_vp_option = *vp_option;
        reduce_vp_option.operation = OPERATION_MAX;
        reducemax_rv(&compare_result, &compare_result, &reduce_vp_option, comparison_results, single_core_mask);
        
        // 修复：更安全的布尔值判断
        uint32_t left_contains_max = comparison_results[max_npu_id];
        // 对于equal操作，结果应该是1或0，这里检查是否为1
        bool left_has_max = (left_contains_max == 1);
        
        // 如果左半区间包含全局最大值，在左半区间继续搜索
        if (left_has_max) {
            right = mid;  // 继续在左半部分搜索第一个最大值
        } else {
            // 否则在右半区间搜索
            left = mid + 1;
        }
        
    } 
    // 二分查找结束后，left指向第一个最大值的位置
    found_index = base_offset + left;  
    // 6. 将结果广播到所有核心
    broadcast(found_index, argmax_out, npu_mask);
    // 7. 返回全局最大值和索引
    *max_index = found_index;  // 返回全局最大值的索引
    *max_value = global_max;    // 返回全局最大值
}




void topk(Tensor *tensor_in, uint32_t *topk_out_index, uint32_t *topk_out_value, int k, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask)
{
    // 检查输入参数
    assert(tensor_in != NULL && topk_out_index != NULL && topk_out_value != NULL);
    assert(vp_option != NULL && intermemory != NULL);
    assert(intermemory->length >= 3);  // 需要3个中间内存块：argmax、工作副本、临时计算
    assert(npu_mask != NULL);
    assert(k > 0);  // k必须为正数
    assert(tensor_in->dim1 == 1 && tensor_in->dim2 == 1);  // 确保是一维向量
    
    // 计算总元素数，确保k不超过总元素数
    uint32_t active_npus[NPU_CORES];
    int num_active_npus = get_active_npus(npu_mask, active_npus);
    if (num_active_npus <= 0) {
        return;  // 没有活跃NPU
    }
    
    uint32_t total_elements = tensor_in->dim0 * num_active_npus;  // 考虑多核分布
    
    // 确保k不超过总元素数
    if (k > total_elements) {
        k = total_elements;
    }
    
    // 创建tensor_in的工作副本，用于逐步置零
    Tensor work_tensor = *tensor_in;
    work_tensor.base_addr = intermemory->memory[1].base_addr;
    
    // 复制输入数据到工作张量
    mov(tensor_in, &work_tensor, npu_mask);
    
    // 创建输出张量用于存储argmax结果
    Tensor argmax_result;
    argmax_result.base_addr = intermemory->memory[2].base_addr;  // 使用第3个内存块避免冲突
    argmax_result.dim0 = 1;
    argmax_result.dim1 = 1;
    argmax_result.dim2 = 1;
    argmax_result.type = TYPE_INT;
    argmax_result.width = WIDTH_32;
    argmax_result.byte_stride1_u = 1;
    argmax_result.byte_stride2_u = 1;
    
    // 获取该类型的最小值，用于覆盖找到的最大值
    uint32_t min_value = get_type_min(tensor_in->type, tensor_in->width);
    
    // 创建专用的中间内存数组给multicore_argmax使用
    InterMemoryArray argmax_intermemory;
    argmax_intermemory.length = 1;
    argmax_intermemory.memory[0] = intermemory->memory[0];  // 使用第1个内存块
    
    // 循环k次，每次找到一个最大值并将其置零
    for (int i = 0; i < k; i++) {
        // 使用multicore_argmax找到当前最大值的索引和值
        uint32_t max_index;
        uint32_t max_value;
        arguemax(&work_tensor, &argmax_result, vp_option, &argmax_intermemory, npu_mask, &max_index, &max_value);
        
        // 检查是否找到有效的最大值
        if (max_index == 0xffffffff) {
            // 如果找不到有效索引，说明已经没有更多元素了，填充剩余位置
            for (int j = i; j < k; j++) {
                topk_out_index[j] = 0xffffffff;  // 无效索引
                topk_out_value[j] = 0;           // 零值
            }
            break;
        }
        
        // 存储找到的索引和值
        topk_out_index[i] = max_index;
        topk_out_value[i] = max_value;
        
        // 计算该索引属于哪个核心（改进索引计算）
        uint32_t global_index = max_index;
        uint32_t core_id = global_index / tensor_in->dim0;
        uint32_t local_index = global_index % tensor_in->dim0;
        
        // 检查核心ID是否有效
        if (core_id < num_active_npus) {
            uint32_t target_npu_id = active_npus[core_id];
            
            // 创建单核掩码，只在包含该最大值的核心上操作
            int single_core_mask[MAX_GROUP] = {0};
            int group = target_npu_id / MAX_MASK;
            single_core_mask[group] = 1 << (target_npu_id % MAX_MASK);
            
            // 计算要置零的元素地址（改进字节偏移计算）
            uint32_t element_size_bits = get_type_size(tensor_in->type, tensor_in->width);
            uint32_t element_size_bytes = (element_size_bits + 7) / 8;  // 向上取整到字节
            uint32_t element_byte_offset = local_index * element_size_bytes;
            uint32_t target_addr = work_tensor.base_addr + element_byte_offset;

            // wr_lmem_primitive(target_addr, min_value, single_core_mask);  // 这东西怎么知道写入多少字节的
            uint32_t data_array[NPU_CORES] = {0};
            data_array[target_npu_id] = min_value;  // 只在目标NPU上写入
            v_wr_lmem_n(target_addr, data_array, element_size_bits, single_core_mask);
        }
    }
}
