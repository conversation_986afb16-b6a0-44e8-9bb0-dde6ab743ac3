/*
 * type_utils.cpp
 *
 *  Created on: 2025年7月24日
 *      Author: 1572
 */

#include "type_utils.h"
#include "type_utils_c.h"
#include <cstring>
#include <limits>


    
uint32_t float32_to_float16(float input){ // 1, 5, 10
    uint32_t inu = *((uint32_t*)&input);
    uint32_t out;

    // 提取符号、指数和尾数
    uint32_t sign = inu & 0x80000000U;
    uint32_t exponent = inu & 0x7F800000U;
    uint32_t mantissa = inu & 0x007FFFFFU;

    // 检查NaN
    if(exponent == 0x7F800000U && mantissa != 0)
        return 0x7FFFU; // 返回float16的NaN

    // 对指数进行偏移和缩放
    int32_t newexp = ((int32_t)(exponent >> 23) - 127) + 15;
    if (newexp >= 31) // 溢出
        exponent = 0x7C00U; // Inf
    else if (newexp <= 0) // 下溢出
        exponent = 0;
    else
        exponent = (uint32_t)newexp << 10;

    // 缩放尾数
    mantissa >>= 13;

    // 组合结果
    out = (sign >> 16) | (exponent & 0x7C00U) | (mantissa & 0x03FFU);

    return out;
}

uint32_t float32_to_bf16(float input) { // 1, 8, 7
    uint32_t inu = *((uint32_t*)&input);
    uint32_t out;

    out = (inu >> 16) & 0x0000ffff;
    
    return out;
}

float float16_to_float32(uint32_t input) { // 1, 5, 10
    uint32_t sign = (input & 0x8000U) << 16;        // 符号位
    uint32_t exponent = (input & 0x7C00U) >> 10;    // 指数位
    uint32_t mantissa = input & 0x03FFU;             // 尾数位

    // 处理特殊情况
    if (exponent == 0) {
        if (mantissa == 0) {
            // 零值
            return *reinterpret_cast<float*>(&sign);
        } else {
            // 非规格化数
            // 找到最高位1的位置
            uint32_t shift = 0;
            uint32_t temp = mantissa;
            while ((temp & 0x400) == 0) {
                temp <<= 1;
                shift++;
            }
            mantissa = (temp & 0x3FF) << 13;
            exponent = (127 - 15 - shift) << 23;
            uint32_t result = sign | exponent | mantissa;
            return *reinterpret_cast<float*>(&result);
        }
    } else if (exponent == 31) {
        // 无穷大或NaN
        exponent = 0xFF << 23;
        mantissa <<= 13;
        uint32_t result = sign | exponent | mantissa;
        return *reinterpret_cast<float*>(&result);
    } else {
        // 规格化数
        exponent = ((exponent - 15) + 127) << 23;  // 调整指数偏移
        mantissa <<= 13;                           // 扩展尾数
        uint32_t result = sign | exponent | mantissa;
        return *reinterpret_cast<float*>(&result);
    }
}

uint32_t get_uint32_t_from_float32(float input, int type, int width) {
    if (type == TYPE_FP && width == WIDTH_32) {
        uint32_t result;
        std::memcpy(&result, &input, sizeof(float));
        return result;
    } else if (type == TYPE_FP && width == WIDTH_16) {
        return float32_to_float16(input);
    } else if (type == TYPE_BF && width == WIDTH_16) {
        return float32_to_bf16(input);
    }
    return 0; // 无效的类型或宽度
}

float get_float32_from_uint32_t(uint32_t input, int type, int width)
{
    if (type == TYPE_FP && width == WIDTH_32) {
        float result;
        std::memcpy(&result, &input, sizeof(float));
        return result;
    } else if (type == TYPE_FP && width == WIDTH_16) {
        return float16_to_float32(input); // 正确地将float16转换为float32
    } else if (type == TYPE_BF && width == WIDTH_16) {
        uint32_t float_bits = input << 16; // 将bfloat16转换为float的高16位
        return *reinterpret_cast<float*>(&float_bits);
    }
    return 0.0f; // 无效的类型或宽度
}



// 模板函数 - 对指定类型执行运算
template<int TYPE, int WIDTH>
uint32_t perform_typed_operation(operation_t op, uint32_t a, uint32_t b) {
    using T = DataType<TYPE, WIDTH>;
    
    // 如果类型是void，说明是无效组合
    if constexpr (std::is_same_v<T, void>) {
        return 0;
    }
    
    uint32_t result = 0;
    
    // 特殊处理 bfloat16_t
    if constexpr (std::is_same_v<T, bfloat16_t>) {
        // 从uint32_t中取出bfloat16数据
        bfloat16_t val_a = *reinterpret_cast<const bfloat16_t*>(&a);
        bfloat16_t val_b = *reinterpret_cast<const bfloat16_t*>(&b);
        
        // 转换为float进行运算
        // bfloat16的简化转换：将16位值左移16位成为float的高16位
        uint32_t float_bits_a = static_cast<uint32_t>(val_a.value) << 16;
        uint32_t float_bits_b = static_cast<uint32_t>(val_b.value) << 16;
        float fa = *reinterpret_cast<const float*>(&float_bits_a);
        float fb = *reinterpret_cast<const float*>(&float_bits_b);
        
        float result_float = 0.0f;
        
        switch(op) {
            case OP_ADD:
                result_float = fa + fb;
                break;
            case OP_SUB:
                result_float = fa - fb;
                break;
            case OP_MUL:
                result_float = fa * fb;
                break;
            case OP_DIV:
                result_float = (fb != 0.0f) ? fa / fb : 0.0f;
                break;
            case OP_MAX:
                result_float = (fa > fb) ? fa : fb;
                break;
            case OP_MIN:
                result_float = (fa < fb) ? fa : fb;
                break;
            default:
                result_float = 0.0f;
                break;
        }
        
        // 将float结果转换回bfloat16
        uint32_t result_bits = *reinterpret_cast<const uint32_t*>(&result_float);
        uint16_t bfloat16_result = static_cast<uint16_t>(result_bits >> 16);
        bfloat16_t final_result(bfloat16_result);
        std::memcpy(&result, &final_result, sizeof(bfloat16_t));
    } else {
        // 对于其他类型，直接通过指针转换获取数据
        T val_a = *reinterpret_cast<const T*>(&a);
        T val_b = *reinterpret_cast<const T*>(&b);
        
        T result_val;
        
        switch(op) {
            case OP_ADD:
                result_val = val_a + val_b;
                break;
                
            case OP_SUB:
                result_val = val_a - val_b;
                break;
                
            case OP_MUL:
                result_val = val_a * val_b;
                break;
                
            case OP_DIV:
                if constexpr (std::is_integral_v<T>) {
                    result_val = (val_b != 0) ? val_a / val_b : T(0);
                } else {
                    result_val = (val_b != T(0)) ? val_a / val_b : T(0);
                }
                break;
                
            case OP_AND:
                if constexpr (std::is_integral_v<T>) {
                    result_val = val_a & val_b;
                } else {
                    result_val = T(0); // 浮点数不支持按位运算
                }
                break;
                
            case OP_OR:
                if constexpr (std::is_integral_v<T>) {
                    result_val = val_a | val_b;
                } else {
                    result_val = T(0); // 浮点数不支持按位运算
                }
                break;
                
            case OP_XOR:
                if constexpr (std::is_integral_v<T>) {
                    result_val = val_a ^ val_b;
                } else {
                    result_val = T(0); // 浮点数不支持按位运算
                }
                break;
                
            case OP_MAX:
                result_val = (val_a > val_b) ? val_a : val_b;
                break;
                
            case OP_MIN:
                result_val = (val_a < val_b) ? val_a : val_b;
                break;
                
            default:
                result_val = T(0);
                break;
        }
        
        // 将结果转换回uint32_t
        std::memcpy(&result, &result_val, sizeof(T));
    }
    
    return result;
}

// 模板函数 - 获取指定类型的零值
template<int TYPE, int WIDTH>
uint32_t get_typed_zero() {
    using T = DataType<TYPE, WIDTH>;
    
    if constexpr (std::is_same_v<T, void>) {
        return 0;
    }
    
    uint32_t result = 0;
    if constexpr (std::is_same_v<T, bfloat16_t>) {
        // bfloat16的零值
        float zero_float = 0.0f;
        uint32_t zero_bits = float32_to_bf16(zero_float);
        return zero_bits;
    }
    else
    {
        T zero_val = T(0);
        std::memcpy(&result, &zero_val, sizeof(T));
    }
    return result;
}

// 模板函数 - 获取指定类型的最小值
template<int TYPE, int WIDTH>
uint32_t get_typed_min() {
    using T = DataType<TYPE, WIDTH>;
    
    if constexpr (std::is_same_v<T, void>) {
        return 0;
    }
    
    uint32_t result = 0;
    T min_val;
    
    if constexpr (std::is_same_v<T, bfloat16_t>) {
        // bfloat16最小值 (负的最大值)
        result = float32_to_bf16(-3.402823466e+38f); // 负的最大值
    } else if constexpr (std::is_integral_v<T>) {
        min_val = std::numeric_limits<T>::min();
        std::memcpy(&result, &min_val, sizeof(T));
    } else {
        min_val = std::numeric_limits<T>::lowest();
        std::memcpy(&result, &min_val, sizeof(T));
    }
    
    return result;
}

// 模板函数 - 获取指定类型的最大值
template<int TYPE, int WIDTH>
uint32_t get_typed_max() {
    using T = DataType<TYPE, WIDTH>;
    
    if constexpr (std::is_same_v<T, void>) {
        return 0;
    }
    
    uint32_t result = 0;
    T max_val;
    
    if constexpr (std::is_same_v<T, bfloat16_t>) {
        // bfloat16最大值
        result = float32_to_bf16(3.402823466e+38f); // 正的最大值
    } else {
        max_val = std::numeric_limits<T>::max();
        std::memcpy(&result, &max_val, sizeof(T));
    }
    
    return result;
}

extern "C" {

uint32_t type_based_operation(int type, int width, operation_t op, uint32_t a, uint32_t b) {
    // 根据type和width分发到对应的模板实例
    if (type == TYPE_INT) {
        if (width == WIDTH_4) return perform_typed_operation<TYPE_INT, WIDTH_4>(op, a, b);
        if (width == WIDTH_8) return perform_typed_operation<TYPE_INT, WIDTH_8>(op, a, b);
        if (width == WIDTH_16) return perform_typed_operation<TYPE_INT, WIDTH_16>(op, a, b);
        if (width == WIDTH_32) return perform_typed_operation<TYPE_INT, WIDTH_32>(op, a, b);
    } else if (type == TYPE_FP) {
        if (width == WIDTH_16) return perform_typed_operation<TYPE_FP, WIDTH_16>(op, a, b);
        if (width == WIDTH_32) return perform_typed_operation<TYPE_FP, WIDTH_32>(op, a, b);
    } else if (type == TYPE_BF) {
        if (width == WIDTH_16) return perform_typed_operation<TYPE_BF, WIDTH_16>(op, a, b);
        if (width == WIDTH_32) return perform_typed_operation<TYPE_BF, WIDTH_32>(op, a, b);
    }
    
    return 0; // 无效的类型组合
}

uint32_t get_type_zero(int type, int width) {
    // 根据type和width分发到对应的模板实例
    if (type == TYPE_INT) {
        if (width == WIDTH_4) return get_typed_zero<TYPE_INT, WIDTH_4>();
        if (width == WIDTH_8) return get_typed_zero<TYPE_INT, WIDTH_8>();
        if (width == WIDTH_16) return get_typed_zero<TYPE_INT, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_zero<TYPE_INT, WIDTH_32>();
    } else if (type == TYPE_FP) {
        if (width == WIDTH_16) return get_typed_zero<TYPE_FP, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_zero<TYPE_FP, WIDTH_32>();
    } else if (type == TYPE_BF) {
        if (width == WIDTH_16) return get_typed_zero<TYPE_BF, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_zero<TYPE_BF, WIDTH_32>();
    }
    return 0;
}

uint32_t get_type_min(int type, int width) {
    // 根据type和width分发到对应的模板实例
    if (type == TYPE_INT) {
        if (width == WIDTH_4) return get_typed_min<TYPE_INT, WIDTH_4>();
        if (width == WIDTH_8) return get_typed_min<TYPE_INT, WIDTH_8>();
        if (width == WIDTH_16) return get_typed_min<TYPE_INT, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_min<TYPE_INT, WIDTH_32>();
    } else if (type == TYPE_FP) {
        if (width == WIDTH_16) return get_typed_min<TYPE_FP, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_min<TYPE_FP, WIDTH_32>();
    } else if (type == TYPE_BF) {
        if (width == WIDTH_16) return get_typed_min<TYPE_BF, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_min<TYPE_BF, WIDTH_32>();
    }
    return 0;
}

uint32_t get_type_max(int type, int width) {
    // 根据type和width分发到对应的模板实例
    if (type == TYPE_INT) {
        if (width == WIDTH_4) return get_typed_max<TYPE_INT, WIDTH_4>();
        if (width == WIDTH_8) return get_typed_max<TYPE_INT, WIDTH_8>();
        if (width == WIDTH_16) return get_typed_max<TYPE_INT, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_max<TYPE_INT, WIDTH_32>();
    } else if (type == TYPE_FP) {
        if (width == WIDTH_16) return get_typed_max<TYPE_FP, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_max<TYPE_FP, WIDTH_32>();
    } else if (type == TYPE_BF) {
        if (width == WIDTH_16) return get_typed_max<TYPE_BF, WIDTH_16>();
        if (width == WIDTH_32) return get_typed_max<TYPE_BF, WIDTH_32>();
    }
    return 0;
}

uint32_t get_type_size(int type, int width)
{
    if (width == WIDTH_4) return 4;
    if (width == WIDTH_8) return 8;
    if (width == WIDTH_16) return 16;
    if (width == WIDTH_32) return 32;
    return 0;
}


} // extern "C"