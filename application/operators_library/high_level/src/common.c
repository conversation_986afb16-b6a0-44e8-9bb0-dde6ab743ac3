/*
 * common.c
 *
 *  Created on: 2025年7月10日
 *      Author: zqguo
 */
#include "high_level.h"
#include "primitive.h"
#include "hardware_inst_data.h"
#include "software_port_data.h"


//initialize CFG Primitive
void init_vp_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_in2   = tensor_in2->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_in2  = tensor_in2->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in2.rem_dim0_in2   = rem_dim0_conversion(tensor_in2);
    vp_config_reg.size_dim0b_rem_dim0_in2.size_dim0b_in2 = dim0b_stride1_conversion(tensor_in2);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;
}

void init_mp_cfg(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option){
    mp_config_reg.op_width_type.type_out   = tensor_out->type;
    mp_config_reg.op_width_type.type_orig  = tensor_orig->type;
    mp_config_reg.op_width_type.type_in    = tensor_in->type;
    mp_config_reg.op_width_type.type_wt    = cim_option->type;
    mp_config_reg.op_width_type.width_out  = tensor_out->width;
    mp_config_reg.op_width_type.width_orig = tensor_orig->width;
    mp_config_reg.op_width_type.width_in   = tensor_in->width;
    mp_config_reg.op_width_type.width_wt   = cim_option->width;
    mp_config_reg.op_width_type.accu       = cim_option->accumulate;
    mp_config_reg.op_width_type.act        = cim_option->activate;
    mp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    mp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    mp_config_reg.size_dim2_dim1_out.size_dim1_out = tensor_out->dim1;
    mp_config_reg.size_dim2_dim1_out.size_dim2_out = tensor_out->dim2;
    mp_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    mp_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;
    mp_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    mp_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    mp_config_reg.size_dim2_dim1_in.size_dim1_in = tensor_in->dim1;
    mp_config_reg.size_dim2_dim1_in.size_dim2_in = tensor_in->dim2;
    mp_config_reg.stride_dim1_in = tensor_in->byte_stride1.stride1;
    mp_config_reg.stride_dim2_in = tensor_in->byte_stride2.stride2;    
}

void init_tm_cfg(Tensor *tensor_in, Tensor *tensor_out){
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_in->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_in->dim2;
    tm_config_reg.stride_dim1_in  = tensor_in->byte_stride1.stride1;
    tm_config_reg.stride_dim2_in  = tensor_in->byte_stride2.stride2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;    
}


//TENSOR-TENSOR OPERATION BASE FUNCTIOON
void tensor_tensor_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){

    uint32_t temp_base_out = 0, temp_base_in1 = 0, temp_base_in2 = 0;
    vv_v_primitive_cfg(tensor_in1, tensor_in2, tensor_out, vp_option, npu_mask);

    for(int dim2 = 0; dim2 < tensor_out->dim2; dim2++){
        temp_base_in1 = tensor_in1->base_addr + dim2 * tensor_in1->byte_stride2_u;
        temp_base_in2 = tensor_in2->base_addr + dim2 * tensor_in2->byte_stride2_u;
        temp_base_out = tensor_out->base_addr + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < tensor_out->dim1; dim1++){
            vv_v_primitive_pre(temp_base_in1, temp_base_in2, npu_mask);
            vv_v_primitive_drv(temp_base_out, npu_mask);
            temp_base_in1 += tensor_in1->byte_stride1_u;
            temp_base_in2 += tensor_in2->byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
}

//DEDUCTION
#define TENSOR_TENSOR_OPERATOR_TEMPLETE(suffix, op_type)                                             \
    void suffix(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){           \
        vp_option->operation = op_type;                                                              \
        tensor_tensor_operator(tensor_in1, tensor_in2, tensor_out, vp_option, npu_mask);     \
    }                                                                                                \

TENSOR_TENSOR_OPERATOR_TEMPLETE(add, OPERATION_ADD)
TENSOR_TENSOR_OPERATOR_TEMPLETE(sub, OPERATION_SUB)
TENSOR_TENSOR_OPERATOR_TEMPLETE(mul, OPERATION_MUL)
TENSOR_TENSOR_OPERATOR_TEMPLETE(equal, OPERATION_EQUAL)
TENSOR_TENSOR_OPERATOR_TEMPLETE(not_equal, OPERATION_NOT_EQUAL)
TENSOR_TENSOR_OPERATOR_TEMPLETE(less, OPERATION_LESS)
TENSOR_TENSOR_OPERATOR_TEMPLETE(less_or_equal, OPERATION_LESS_OR_EQUAL)
TENSOR_TENSOR_OPERATOR_TEMPLETE(greater, OPERATION_GREATER)
TENSOR_TENSOR_OPERATOR_TEMPLETE(greater_or_equal, OPERATION_GREATER_OR_EQEAL)
TENSOR_TENSOR_OPERATOR_TEMPLETE(and, OPERATION_AND)
TENSOR_TENSOR_OPERATOR_TEMPLETE(or, OPERATION_OR)

//TENSOR-VECTOR OPERATION BASE FUNCTIOON
void tensor_vector_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){

    uint32_t temp_base_out = 0, temp_base_in1 = 0, temp_base_in2 = 0;
    vv_v_primitive_cfg(tensor_in1, tensor_in2, tensor_out, vp_option, npu_mask);

    temp_base_in2 = tensor_in2->base_addr;

    for(int dim2 = 0; dim2 < tensor_out->dim2; dim2++){
        temp_base_in1 = tensor_in1->base_addr + dim2 * tensor_in1->byte_stride2_u;
        temp_base_out = tensor_out->base_addr + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < tensor_out->dim1; dim1++){
            vv_v_primitive_pre(temp_base_in1, temp_base_in2, npu_mask);
            vv_v_primitive_drv(temp_base_out, npu_mask);
            temp_base_in1 += tensor_in1->byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
}

//DEDUCTION
#define TENSOR_VECTOR_OPERATOR_TEMPLETE(suffix, op_type)                                             \
    void suffix##_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){           \
        vp_option->operation = op_type;                                                              \
        tensor_vector_operator(tensor_in1, tensor_in2, tensor_out, vp_option, npu_mask);     \
    }                                                                                                \

TENSOR_VECTOR_OPERATOR_TEMPLETE(add, OPERATION_ADD)
TENSOR_VECTOR_OPERATOR_TEMPLETE(sub, OPERATION_SUB)
TENSOR_VECTOR_OPERATOR_TEMPLETE(mul, OPERATION_MUL)
TENSOR_VECTOR_OPERATOR_TEMPLETE(equal, OPERATION_EQUAL)
TENSOR_VECTOR_OPERATOR_TEMPLETE(not_equal, OPERATION_NOT_EQUAL)
TENSOR_VECTOR_OPERATOR_TEMPLETE(less, OPERATION_LESS)
TENSOR_VECTOR_OPERATOR_TEMPLETE(less_or_equal, OPERATION_LESS_OR_EQUAL)
TENSOR_VECTOR_OPERATOR_TEMPLETE(greater, OPERATION_GREATER)
TENSOR_VECTOR_OPERATOR_TEMPLETE(greater_or_equal, OPERATION_GREATER_OR_EQEAL)
TENSOR_VECTOR_OPERATOR_TEMPLETE(and, OPERATION_AND)
TENSOR_VECTOR_OPERATOR_TEMPLETE(or, OPERATION_OR)

//TENSOR-SCALAR OPERATION BASE FUNCTIOON
void tensor_scalar_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    uint32_t temp_base_out = 0, temp_base_in1 = 0;
    vs_v_primitive_cfg(tensor_in1, tensor_in2, tensor_out, vp_option, npu_mask);
    for(int dim2 = 0; dim2 < tensor_out->dim2; dim2++){
        temp_base_in1 = tensor_in1->base_addr + dim2 * tensor_in1->byte_stride2_u;
        temp_base_out = tensor_out->base_addr + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < tensor_out->dim1; dim1++){
            vs_v_primitive_pre(temp_base_in1, vp_option->scalar_in2, npu_mask);
            vs_v_primitive_drv(temp_base_out, npu_mask);
            temp_base_in1 += tensor_in1->byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
}

//DEDUCTION
#define TENSOR_SCALAR_OPERATOR_TEMPLETE(suffix, op_type)                                             \
    void suffix##_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){           \
        vp_option->operation = op_type;                                                              \
        tensor_scalar_operator(tensor_in1, tensor_in2, tensor_out, vp_option, npu_mask);     \
    }                                                                                                \

TENSOR_SCALAR_OPERATOR_TEMPLETE(add, OPERATION_ADD)
TENSOR_SCALAR_OPERATOR_TEMPLETE(sub, OPERATION_SUB)
TENSOR_SCALAR_OPERATOR_TEMPLETE(mul, OPERATION_MUL)
TENSOR_SCALAR_OPERATOR_TEMPLETE(equal, OPERATION_EQUAL)
TENSOR_SCALAR_OPERATOR_TEMPLETE(not_equal, OPERATION_NOT_EQUAL)
TENSOR_SCALAR_OPERATOR_TEMPLETE(less, OPERATION_LESS)
TENSOR_SCALAR_OPERATOR_TEMPLETE(less_or_equal, OPERATION_LESS_OR_EQUAL)
TENSOR_SCALAR_OPERATOR_TEMPLETE(greater, OPERATION_GREATER)
TENSOR_SCALAR_OPERATOR_TEMPLETE(greater_or_equal, OPERATION_GREATER_OR_EQEAL)
TENSOR_SCALAR_OPERATOR_TEMPLETE(and, OPERATION_AND)
TENSOR_SCALAR_OPERATOR_TEMPLETE(or, OPERATION_OR)


//VNICE TENSOR-SCALAR OPERATION BASE FUNCTIOON
void v_tensor_scalar_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask){
    uint32_t temp_base_out = 0, temp_base_in1 = 0;
    v_vs_v_primitive_cfg(tensor_in1, tensor_in2, tensor_out, vp_option, npu_mask);
    for(int dim2 = 0; dim2 < tensor_out->dim2; dim2++){
        temp_base_in1 = tensor_in1->base_addr + dim2 * tensor_in1->byte_stride2_u;
        temp_base_out = tensor_out->base_addr + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < tensor_out->dim1; dim1++){
            v_vs_v_primitive_pre(temp_base_in1, scalar_in_16, npu_mask);
            v_vs_v_primitive_drv(temp_base_out, npu_mask);
            temp_base_in1 += tensor_in1->byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
}

//DEDUCTION
#define V_TENSOR_SCALAR_OPERATOR_TEMPLETE(suffix, op_type)                                             \
    void v_##suffix##_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask){           \
        vp_option->operation = op_type;                                                              \
        v_tensor_scalar_operator(tensor_in1, tensor_in2, tensor_out, vp_option, scalar_in_16, npu_mask);     \
    }                                                                                                \

V_TENSOR_SCALAR_OPERATOR_TEMPLETE(add, OPERATION_ADD)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(sub, OPERATION_SUB)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(mul, OPERATION_MUL)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(equal, OPERATION_EQUAL)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(not_equal, OPERATION_NOT_EQUAL)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(less, OPERATION_LESS)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(less_or_equal, OPERATION_LESS_OR_EQUAL)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(greater, OPERATION_GREATER)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(greater_or_equal, OPERATION_GREATER_OR_EQEAL)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(and, OPERATION_AND)
V_TENSOR_SCALAR_OPERATOR_TEMPLETE(or, OPERATION_OR)


void reduce_dim0_base_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    v_v_s_primitive_cfg(tensor_in, tensor_out, vp_option, npu_mask);

    uint32_t temp_base_in, temp_base_out;
    uint32_t v_s_return[NPU_CORES] = {0};

    for(int dim2 = 0; dim2 < tensor_in->dim2; dim2++){
        temp_base_in  = tensor_in->base_addr  + dim2 * tensor_in->byte_stride2_u;
        temp_base_out = tensor_out->base_addr + dim2 * tensor_out->byte_stride2_u;
        for(int dim1 = 0; dim1 < tensor_in->dim1; dim1++){
            v_v_s_primitive_drv(temp_base_in, npu_mask, v_s_return);
            v_wr_lmem_n(temp_base_out, v_s_return, tensor_out->width, npu_mask);
            temp_base_in  += tensor_in->byte_stride1_u;
            temp_base_out += tensor_out->byte_stride1_u;
        }
    }
}

#define REDUCE_DIM0_TEMPLETE_NPU(suffix, op_type)                                  \
    void reduce##suffix##_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){     \
        vp_option->operation = op_type;                                        \
        reduce_dim0_base_npu(tensor_in, tensor_out, vp_option, npu_mask);   \
    }                                                                          \

REDUCE_DIM0_TEMPLETE_NPU(max, OPERATION_MAX)
REDUCE_DIM0_TEMPLETE_NPU(min, OPERATION_MIN)
REDUCE_DIM0_TEMPLETE_NPU(sum, OPERATION_ADD)


void reduce_dim0_base_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask){
    v_v_s_primitive(tensor_in, tensor_out, vp_option, npu_mask, reduce_return_16);
}

#define REDUCE_DIM0_TEMPLETE_RV(suffix, op_type)                                  \
    void reduce##suffix##_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask){     \
        vp_option->operation = op_type;                                        \
        reduce_dim0_base_rv(tensor_in, tensor_out, vp_option, reduce_return_16, npu_mask);   \
    }                                                                          \

REDUCE_DIM0_TEMPLETE_RV(max, OPERATION_MAX)
REDUCE_DIM0_TEMPLETE_RV(min, OPERATION_MIN)
REDUCE_DIM0_TEMPLETE_RV(sum, OPERATION_ADD)


void nn_lut_base(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, NN_LUT *nn_lut, InterMemoryArray *intermemory, int *npu_mask){
    uint32_t temp_dim0 = tensor_out->dim0;
    uint32_t temp_dim1 = tensor_out->dim1;
    uint32_t temp_dim2 = tensor_out->dim2;

    tensor_in->dim0 = temp_dim0 * temp_dim1 * temp_dim2;
    tensor_in->dim1 = 1;
    tensor_in->dim2 = 1;
    tensor_out->dim0 = temp_dim0 * temp_dim1 * temp_dim2;
    tensor_out->dim1 = 1;
    tensor_out->dim2 = 1;


    Tensor temp_index1  = *tensor_in;
    temp_index1.base_addr  = intermemory->memory[0].base_addr;
    Tensor temp_index2  = *tensor_in;
    temp_index2.base_addr  = intermemory->memory[1].base_addr;
    Tensor temp_tensor1 = *tensor_in;
    temp_tensor1.base_addr = intermemory->memory[2].base_addr;
    Tensor temp_tensor2 = *tensor_in;
    temp_tensor2.base_addr = intermemory->memory[3].base_addr;


    uint32_t temp_addr_in1,temp_addr_in2,temp_addr_out;
    uint32_t index_addr_cur, index_addr_pre;
    vv_v_primitive_cfg(tensor_in, tensor_in, tensor_out, vp_option, npu_mask);

    for(int k = 0; k < NN_LUT_LEN; k++) {
        if(!IS_ODD(k)) {
            index_addr_cur = temp_index1.base_addr;
            index_addr_pre = temp_index2.base_addr;
        } else {
            index_addr_cur = temp_index2.base_addr;
            index_addr_pre = temp_index1.base_addr;
        }

// index operation
        vp_cfg_primitive_op(OPERATION_LESS, npu_mask);
        vs_v_primitive_pre(tensor_in->base_addr, nn_lut->break_point[k], npu_mask);
        vs_v_primitive_drv(index_addr_cur, npu_mask);

// xor index_tensor1 and index_tensor2 (differential)
        vp_cfg_primitive_op(OPERATION_SUB, npu_mask);
        vv_v_primitive_pre(index_addr_pre, index_addr_cur, npu_mask);
        vv_v_primitive_drv(index_addr_pre, npu_mask);

// mul scale
        vp_cfg_primitive_op(OPERATION_MUL, npu_mask);
        vs_v_primitive_pre(tensor_in->base_addr, nn_lut->lut_scale[k], npu_mask);
        vs_v_primitive_drv(temp_tensor1.base_addr, npu_mask);

// add offset
        vp_cfg_primitive_op(OPERATION_ADD, npu_mask);
        vs_v_primitive_pre(temp_tensor1.base_addr, nn_lut->lut_offset[k], npu_mask);
        vs_v_primitive_drv(temp_tensor2.base_addr, npu_mask);

// accumulate
        if(k == 0){
            vp_cfg_primitive_op(OPERATION_MUL, npu_mask);
            vv_v_primitive_pre(index_addr_pre, temp_tensor2.base_addr, npu_mask);
            vv_v_primitive_drv(tensor_out->base_addr, npu_mask);
        }else{
            vp_cfg_primitive_op(OPERATION_MUL, npu_mask);
            vv_v_primitive_pre(index_addr_pre, temp_tensor2.base_addr, npu_mask);
            vv_v_primitive_drv(temp_tensor1.base_addr, npu_mask);

            vp_cfg_primitive_op(OPERATION_ADD, npu_mask);
            vv_v_primitive_pre(temp_tensor1.base_addr, tensor_out->base_addr, npu_mask);
            vv_v_primitive_drv(tensor_out->base_addr, npu_mask);
        }
    }
}

#define ELEMWISE_TEMPLATE(sufix, nn_lut)                                \
    void sufix##_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask){     \
        nn_lut_base(tensor_in, tensor_out, vp_option, nn_lut, intermemory, npu_mask);        \
    }                                                                   \


ELEMWISE_TEMPLATE(sigmoid,  &nn_lut_sigmoid_fp16)
ELEMWISE_TEMPLATE(log,      &nn_lut_log_fp16)
ELEMWISE_TEMPLATE(tanh,     &nn_lut_tanh_fp16)
ELEMWISE_TEMPLATE(gelu,     &nn_lut_gelu_fp16)
ELEMWISE_TEMPLATE(reciprocal, &nn_lut_div_fp16)
ELEMWISE_TEMPLATE(squareroot, &nn_lut_squareroot_fp16)

//permute base
void permute_021(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    Tensor temp_tensor_in  = *tensor_in;
    Tensor temp_tensor_out = *tensor_out;

    temp_tensor_in.dim2  = 1;
    temp_tensor_out.dim2 = 1;
    temp_tensor_out.dim1 = tensor_in->dim1;
    temp_tensor_out.byte_stride1_u = tensor_out->byte_stride2_u;

    uint32_t temp_base_in, temp_base_out;
    mov_primitive_cfg(&temp_tensor_in, &temp_tensor_out, npu_mask);
    for(int dim2 = 0; dim2 < tensor_in->dim2; dim2++){
        temp_base_in  = temp_tensor_in.base_addr  + dim2 * temp_tensor_in.byte_stride2_u;
        temp_base_out = temp_tensor_out.base_addr + dim2 * temp_tensor_in.byte_stride1_u;
        mov_primitive_drv(temp_base_in, temp_base_out, npu_mask);
    }
}

void permute_120(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask){
    Tensor temp_tensor1 = {
        .base_addr = intermemory->memory[0].base_addr,
        .dim0 = tensor_in->dim1,
        .dim1 = tensor_in->dim0,
        .dim2 = tensor_in->dim2,
        .byte_stride1_u = (tensor_in->dim1+(256>>tensor_in->width)-1)/(256>>tensor_in->width) * 32,
        .byte_stride2_u = (tensor_in->dim1+(256>>tensor_in->width)-1)/(256>>tensor_in->width) * 32 * tensor_in->dim0,
        .width = tensor_in->width,
        .type = tensor_in->type
    };
    //012——102
    transpose(tensor_in, &temp_tensor1, npu_mask);
    //102——120
    permute_021(&temp_tensor1, tensor_out, npu_mask);
}

void permute_201(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask){
    Tensor temp_tensor1 = {
        .base_addr = intermemory->memory[0].base_addr,
        .dim0 = tensor_in->dim0,
        .dim1 = tensor_in->dim2,
        .dim2 = tensor_in->dim1,
        .byte_stride1_u = tensor_in->byte_stride1_u,
        .byte_stride2_u = tensor_in->byte_stride1_u * tensor_in->dim2,
        .width = tensor_in->width,
        .type = tensor_in->type
    };
    //012——021
    permute_021(tensor_in, &temp_tensor1, npu_mask);
    //021——201
    transpose(&temp_tensor1, tensor_out, npu_mask);
}

void permute_210(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask){
    Tensor temp_tensor1 = {
        .base_addr = intermemory->memory[0].base_addr,
        .dim0 = tensor_in->dim0,
        .dim1 = tensor_in->dim2,
        .dim2 = tensor_in->dim1,
        .byte_stride1_u = tensor_in->byte_stride1_u,
        .byte_stride2_u = tensor_in->byte_stride1_u * tensor_in->dim2,
        .width = tensor_in->width,
        .type = tensor_in->type
    };
    Tensor temp_tensor2 = {
        .base_addr = intermemory->memory[1].base_addr,
        .dim0 = temp_tensor1.dim0,
        .dim1 = temp_tensor1.dim2,
        .dim2 = temp_tensor1.dim1,
        .byte_stride1_u = temp_tensor1.byte_stride1_u,
        .byte_stride2_u = temp_tensor1.byte_stride1_u * temp_tensor1.dim2,
        .width = temp_tensor1.width,
        .type = temp_tensor1.type
    };
    //012——021
    permute_021(tensor_in, &temp_tensor1, npu_mask);
    //021——201
    transpose(&temp_tensor1, &temp_tensor2, npu_mask);
    //201——210
    permute_021(&temp_tensor2, tensor_out, npu_mask);
}


//expand base
void expand_dim0(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    mov_primitive_cfg(tensor_in, tensor_out, npu_mask);
    uint32_t temp_dim0_out = tensor_out->dim0;
    uint32_t temp_base_out = tensor_out->base_addr;
    uint32_t temp_base_in = tensor_in->base_addr;

    tensor_out->dim0 = tensor_in->dim0;
    for(int dim0 = 0; dim0 < temp_dim0_out; dim0++){
        tensor_out->base_addr = temp_base_out + tensor_in->width/8;
        mov_primitive_drv(temp_base_in, temp_base_out, npu_mask);
    }
    tensor_out->dim0 = temp_dim0_out;
}

void expand_dim1(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    mov_primitive_cfg(tensor_in, tensor_out, npu_mask);
    uint32_t temp_dim1_out = tensor_out->dim1;
    uint32_t temp_base_out = tensor_out->base_addr;
    uint32_t temp_base_in = tensor_in->base_addr;
    tensor_out->dim1 = tensor_in->dim1;
    for(int dim0 = 0; dim0 < temp_dim1_out; dim0++){
        tensor_out->base_addr = temp_base_out + tensor_in->byte_stride1_u;
        mov_primitive_drv(temp_base_in, temp_base_out, npu_mask);
    }
    tensor_out->dim1 = temp_dim1_out;
}

void expand_dim2(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    mov_primitive_cfg(tensor_in, tensor_out, npu_mask);
    uint32_t temp_dim2_out = tensor_out->dim2;
    uint32_t temp_base_out = tensor_out->base_addr;
    uint32_t temp_base_in = tensor_in->base_addr;
    tensor_out->dim2 = tensor_in->dim2;
    for(int dim0 = 0; dim0 < temp_dim2_out; dim0++){
        tensor_out->dim2 = tensor_in->dim2;
        tensor_out->base_addr = temp_base_out + tensor_in->byte_stride2_u * tensor_in->dim2;
        mov_primitive_drv(temp_base_in, temp_base_out, npu_mask);
    }
    tensor_out->dim2 = temp_dim2_out;
}


// memory
// 写入函数，支持 1B / 2B / 4B / 半字节（4bit）粒度写入
void wr_lmem_n(uint32_t addr, uint32_t data, int size_in_bits, int *npu_mask) {
    uint32_t aligned_addr = addr & 0xFFFFFFFC;
    uint32_t shift = (addr & 0x3) * 8;
    uint32_t rd_lmem_return[NPU_CORES] = {0};  // size = 16，存放每个 NPU 的原始数据
    v_rd_lmem_primitive(aligned_addr, rd_lmem_return, npu_mask);  // 每组 4 个读取

    // 修改每个 NPU 中对应的数据
    for (int group_id = 0; group_id < MAX_GROUP; group_id++) {
        if (!npu_mask[group_id]) continue;
        for (int lane = 0; lane < MAX_MASK; lane++) {  // group 内部 4 个 NPU
            int idx = group_id * MAX_MASK + lane;
            uint32_t ori_data = rd_lmem_return[idx];
            uint32_t new_data = 0;

            if (size_in_bits == WIDTH_4) {
                int is_high_nibble = (addr & 0x1);
                uint32_t nibble_shift = shift + (is_high_nibble ? 4 : 0);
                uint32_t mask = 0xF << nibble_shift;
                new_data = (ori_data & ~mask) | ((data & 0xF) << nibble_shift);
            }
            else if (size_in_bits == WIDTH_8) {
                uint32_t mask = 0xFF << shift;
                new_data = (ori_data & ~mask) | ((data & 0xFF) << shift);
            }
            else if (size_in_bits == WIDTH_16) {
                uint32_t word_sel = (addr & 0x2) * 8;
                uint32_t mask = 0xFFFF << word_sel;
                new_data = (ori_data & ~mask) | ((data & 0xFFFF) << word_sel);
            }
            else if (size_in_bits == WIDTH_32) {
                new_data = data;
            } else {
                assert(!"Unsupported size_in_bits!");
            }
            rd_lmem_return[idx] = new_data;  
        }
    }
    v_wr_lmem_primitive(aligned_addr, rd_lmem_return, npu_mask);  
}
// 写入函数，支持 1B / 2B / 4B / 半字节（4bit）粒度写入，每个 NPU 都有独立的 data_array
void v_wr_lmem_n(uint32_t addr, uint32_t *data_array, int size_in_bits, int *npu_mask) {
    uint32_t aligned_addr = addr & 0xFFFFFFFC;
    uint32_t shift = (addr & 0x3) * 8;
    uint32_t rd_lmem_return[NPU_CORES] = {0};  // 每个 NPU 的原始数据
    v_rd_lmem_primitive(aligned_addr, rd_lmem_return, npu_mask);  // 每组 4 个读取

    for (int group_id = 0; group_id < MAX_GROUP; group_id++) {
        if (!npu_mask[group_id]) continue;
        for (int lane = 0; lane < MAX_MASK; lane++) {  // 每个 group 内 4 个 NPU
            int idx = group_id * MAX_MASK + lane;
            uint32_t ori_data = rd_lmem_return[idx];
            uint32_t wr_data = data_array[idx];  // 每个 NPU 对应的 data
            uint32_t new_data = 0;
            if (size_in_bits == WIDTH_4) {
                int is_high_nibble = (addr & 0x1);
                uint32_t nibble_shift = shift + (is_high_nibble ? 4 : 0);
                uint32_t mask = 0xF << nibble_shift;
                new_data = (ori_data & ~mask) | ((wr_data & 0xF) << nibble_shift);
            }
            else if (size_in_bits == WIDTH_8) {
                uint32_t mask = 0xFF << shift;
                new_data = (ori_data & ~mask) | ((wr_data & 0xFF) << shift);
            }
            else if (size_in_bits == WIDTH_16) {
                uint32_t word_sel = (addr & 0x2) * 8;
                uint32_t mask = 0xFFFF << word_sel;
                new_data = (ori_data & ~mask) | ((wr_data & 0xFFFF) << word_sel);
            }
            else if (size_in_bits == WIDTH_32) {
                new_data = wr_data;
            } else {
                assert(!"Unsupported size_in_bits!");
            }
            rd_lmem_return[idx] = new_data;
        }
    }
    v_wr_lmem_primitive(aligned_addr, rd_lmem_return, npu_mask);  // 写回所有 NPU
}


// 读出函数，支持 1B / 2B / 4B / 半字节（4bit）粒度写入

void v_rd_lmem_n(uint32_t addr, int size_in_bits, uint32_t *rd_lmem_return, int *npu_mask) {
    uint32_t aligned_addr = addr & 0xFFFFFFFC;  // 4 字节对齐
    uint32_t raw_data[NPU_CORES] = {0};
    v_rd_lmem_primitive(aligned_addr, raw_data, npu_mask);

    for (int group_id = 0; group_id < MAX_GROUP; group_id++) {
        if (!npu_mask[group_id]) {
            for (int i = 0; i < MAX_MASK; i++) {
                rd_lmem_return[group_id * MAX_MASK + i] = 0;
            }
            continue;
        }
        for (int i = 0; i < MAX_MASK; i++) {
            uint32_t data = raw_data[group_id * MAX_MASK + i];
            uint32_t shift = (addr & 0x3) * 8;
            if (size_in_bits == WIDTH_4) {
                shift += (addr & 0x1) ? 4 : 0;
                rd_lmem_return[group_id * MAX_MASK + i] = (data >> shift) & 0xF;
            } else if (size_in_bits == WIDTH_8) {
                rd_lmem_return[group_id * MAX_MASK + i] = (data >> shift) & 0xFF;
            } else if (size_in_bits == WIDTH_16) {
                shift = (addr & 0x2) * 8;
                rd_lmem_return[group_id * MAX_MASK + i] = (data >> shift) & 0xFFFF;
            } else if (size_in_bits == WIDTH_32){
                rd_lmem_return[group_id * MAX_MASK + i] = data;
            } else {
                assert(!"Unsupported size_in_bits!");
            }
        }
    }
}


////////////////NN_LUT配置结构体///////////////////
#define MAX_FP16 0x7800 // s=0, e=11110, m=0000000000
NN_LUT nn_lut_sigmoid_fp16 = {
    .break_point = {0x0000c572,0x0000c380,0x0000c221,0x0000c202,0x0000c117,0x0000c07d,0x0000bf38,0x0000bea6,0x0000bd42,0x0000bce6,0x0000bab4,0x0000b86d,0x0000b72d,0x0000b598,0x00003cf6,0x0000413e, MAX_FP16},
    .lut_scale   = {0x00000000,0x00002255,0x00002862,0x00002929,0x00002ae0,0x00002d39,0x00002e34,0x000030af,0x000030cc,0x0000302d,0x000032ab,0x000032a4,0x0000339c,0x0000354b,0x00003365,0x00002f4f,0x000025e0},
	.lut_offset  = {0x00000000,0x00002c50,0x000030c8,0x00003160,0x000032aa,0x00003478,0x00003504,0x00003672,0x0000368a,0x00003622,0x000037a8,0x000037a5,0x000037ea,0x0000384a,0x00003803,0x0000392c,0x00003b16}
};
NN_LUT nn_lut_log_fp16 = {
    .break_point = {0x0000e169,0x0000b4af,0x0000a8bc,0x0000a5ea,0x00001cab,0x00003382,0x00003494,0x000034fb,0x000035c7,0x000035dc,0x00003620,0x00003663,0x000036d4,0x00004585,0x00004f08,0x000055e8, MAX_FP16},
    .lut_scale   = {0x00004c00,0x00004a7f,0x00004a81,0x00004a78,0x00004a6d,0x00004a82,0x000049b0,0x000048cc,0x000048b0,0x00004618,0x00004500,0x00004400,0x0000409a,0x000036b1,0x00002c3b,0x0000246e,0x00001c00},
    .lut_offset  = {0x00006810,0x0000c4be,0x0000c4bd,0x0000c4be,0x0000c4be,0x0000c4be,0x0000c45c,0x0000c3b2,0x0000c390,0x0000c132,0x0000c064,0x0000bf40,0x0000bc8a,0x0000b548,0x00003e74,0x000041f9,0x00004440}
};
NN_LUT nn_lut_tanh_fp16 = {
    .break_point = {0x0000c303,0x0000bf6c,0x0000be4e,0x0000be4b,0x0000bd3f,0x0000bcff,0x0000bc46,0x0000bad7,0x0000b93d,0x0000b76e,0x0000b462,0x0000355a,0x000038f2,0x00003b12,0x00003cc0,0x00004015, MAX_FP16},
    .lut_scale   = {0x0000365d,0x00003658,0x00003652,0x00003555,0x0000330b,0x00003400,0x0000351a,0x00003724,0x000038be,0x000039f3,0x00003b04,0x00003bd9,0x00003a60,0x000038c4,0x0000367b,0x00003332,0x00003338},
    .lut_offset  ={0x0000b4b0,0x0000b4c2,0x0000b4ce,0x0000b65c,0x0000b89b,0x0000b84a,0x0000b735,0x0000b507,0x0000b20c,0x0000adc8,0x0000a740,0x00000000,0x00002be0,0x000031f4,0x000035ac,0x0000388c,0x00003889}
};
NN_LUT nn_lut_div_fp16 = {
    .break_point = {0x00001cab,0x000031b1,0x00003318,0x000034ee,0x0000394f,0x00003a03,0x00003aef,0x00003b8b,0x00003bfb,0x00003c00,0x00003f92,0x0000415e,0x0000451c,0x000045c5,0x00004e09,0x00005a6e, MAX_FP16},
    .lut_scale   = {0x0000bc19,0x0000bb70,0x0000bbaa,0x0000ba54,0x0000b9a0,0x0000b93f,0x0000b904,0x0000b797,0x0000b825,0x0000bccd,0x0000b845,0x0000b336,0x0000ac22,0x0000ad90,0x00009e25,0x00008850,0x00008000},
    .lut_offset  ={0x00003ee8,0x00003ee7,0x00003eec,0x00003ec7,0x00003eab,0x00003e8a,0x00003e74,0x00003ded,0x00003e17,0x00004068,0x00003e25,0x00003b9f,0x0000382c,0x00003916,0x00003167,0x000026ef,0x00000000}
};
NN_LUT nn_lut_squareroot_fp16 ={
    .break_point = {0x0000e1e8,0x0000b41d,0x0000ab06,0x0000aa75,0x00009c6e,0x00001cab,0x00003c0a,0x000043c5,0x000049c9,0x00004ea5,0x00005294,0x000055ed,0x000058f5,0x00005beb,0x00005e19,0x000060a0, MAX_FP16},
	.lut_scale   = {0x00003800,0x000039de,0x00003a2f,0x00000000,0x00003811,0x00000000,0x00003a6c,0x0000358b,0x000031fa,0x00002f79,0x00002d20,0x00002b8a,0x000029c0,0x00002884,0x00002721,0x000025d4,0x00002800},
	.lut_offset  ={0x0000d980,0x00003399,0x000033ec,0x00003290,0x00003362,0x00003350,0x00003330,0x0000397c,0x00003d3a,0x0000403b,0x0000422e,0x00004434,0x00004588,0x00004710,0x00004879,0x00004977,0x000045e8}};
NN_LUT nn_lut_gelu_fp16 = {
    .break_point = {0x0000c5f9,0x0000c54e,0x0000c1b0,0x0000c036,0x0000baea,0x0000b7ca,0x0000b0b3,0x000031f5,0x0000367e,0x000038b1,0x00003b82,0x00004014,0x000040cc,0x000041ab,0x00004504,0x00004c61, MAX_FP16},
    .lut_offset  = {0x0000a796,0x00009f2c,0x0000a17e,0x0000af6b,0x0000b454,0x0000b082,0x0000a886,0x00001aa0,0x0000a9a0,0x0000ac28,0x0000b198,0x0000b470,0x0000b010,0x0000af80,0x0000a400,0x0000b000,0x0000b380},
    .lut_scale   = {0x00009dc0,0x0000977c,0x0000992e,0x0000a90f,0x0000af3b,0x000028c3,0x00003410,0x00003823,0x00003a29,0x00003a93,0x00003c09,0x00003c79,0x00003c2d,0x00003c29,0x00003c04,0x00003c1a,0x00003c20}
};

//////////////hareware_inst_data 配置结构体////////////
LS_CONFIG ls_config_reg;
TM_CONFIG tm_config_reg;
MP_CONFIG mp_config_reg;
VP_CONFIG vp_config_reg;
NOC_CONFIG noc_config_reg;
