/*
 * datastruct.h
 *
 *  Created on: 2025年6月23日
 *      Author: zqguo
 */

#ifndef OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_SOFTWARE_PORT_DATA_H_
#define OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_SOFTWARE_PORT_DATA_H_

#include <stdint.h>


/////////////张量信息结构体//////////////////
typedef struct {
        uint32_t base_addr;
        uint32_t dim0;
        uint32_t dim1;
        uint32_t dim2;
        union{                
            uint32_t byte_stride1_u;   // dim1 字节偏移量
            struct{
                uint32_t reserved1: 5;
                uint32_t stride1 : 27; // 256 bit
            }byte_stride1;
        };
        union{                
            uint32_t byte_stride2_u;
            struct{
                uint32_t reserved2: 5;
                uint32_t stride2 : 27; // 256 bit
            }byte_stride2;
        };
        uint32_t width;
        uint32_t type;
} Tensor;

/////////////中间内存数组结构体//////////////
typedef struct {
    uint32_t base_addr;               // 起始地址
    uint32_t byte_size;               // 内存字节大小
} InterMemory;

typedef struct {
    InterMemory *memory;
    uint32_t length;                  // 所需中间内存的个数
} InterMemoryArray;

/////////////CIM Page信息结构体//////////////
typedef struct {
    uint32_t type;                   //weight
    uint32_t width;
    uint32_t page_index;
    uint32_t accumulate;             //operator
    uint32_t activate;
    uint32_t shift;
}CIM_Option;

///////////VP Primitive配置结构体//////////////
typedef struct {
    union{                
        uint32_t special_case_u;
        struct{
            uint32_t saturate:1;
            uint32_t disable0:1;                 
            uint32_t round_mode:3;
        }special_case;  
    };  
    uint32_t operation;
    uint32_t scalar_in2;
}VP_Option;

///////////////NOC Primitive配置结构体//////////////
typedef struct {
    uint32_t base_addr_srcmem;     //源内存基地址
    uint32_t base_addr_destmem;    //目的内存基地址
    uint32_t src_idx;              //传输目的NPU组索引
    uint32_t dest_idx;             //传输源NPU组索引
}NOC_Option;

////////////////NN_LUT配置结构体///////////////////
#define NN_LUT_LEN 17
typedef struct{
    uint32_t lut_scale[NN_LUT_LEN];
    uint32_t lut_offset[NN_LUT_LEN];
    uint32_t break_point[NN_LUT_LEN+1];
}NN_LUT;

extern NN_LUT nn_lut_sigmoid_fp16;
extern NN_LUT nn_lut_log_fp16;
extern NN_LUT nn_lut_tanh_fp16;
extern NN_LUT nn_lut_div_fp16;
extern NN_LUT nn_lut_squareroot_fp16;
extern NN_LUT nn_lut_gelu_fp16;




#endif /* OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_SOFTWARE_PORT_DATA_H_ */
