/*
 * type_utils.h
 *
 *  Created on: 2025年7月24日
 *      Author: 1572
 */

#ifndef OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_TYPE_UTILS_H_
#define OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_TYPE_UTILS_H_

#include <type_traits>
#include "hardware_inst_data.h"
#include <type_traits>
#include <cstdint>  // for int8_t, int16_t, uint16_t, etc.

// BFloat16 类型定义 (简化版本)
struct bfloat16_t {
    uint16_t value;
    bfloat16_t() : value(0) {}
    bfloat16_t(int v) : value(0) {}
    explicit bfloat16_t(uint16_t v) : value(v) {}
};

// 类型特征模板 - 根据TYPE和WIDTH确定具体类型
template<int TYPE, int WIDTH>
struct TypeSelector {
    using type = void; // 默认为void，表示无效组合
};

// TYPE_INT (0) 的特化
template<>
struct TypeSelector<TYPE_INT, WIDTH_4> {
    using type = int8_t;  // 4位整数，使用int8_t
};

template<>
struct TypeSelector<TYPE_INT, WIDTH_8> {
    using type = int8_t;  // 8位整数
};

template<>
struct TypeSelector<TYPE_INT, WIDTH_16> {
    using type = int16_t; // 16位整数
};

template<>
struct TypeSelector<TYPE_INT, WIDTH_32> {
    using type = int32_t; // 32位整数
};

// TYPE_FP (1) 的特化
template<>
struct TypeSelector<TYPE_FP, WIDTH_16> {
    using type = float;   // 16位浮点，使用float表示
};

template<>
struct TypeSelector<TYPE_FP, WIDTH_32> {
    using type = float;   // 32位浮点
};

// TYPE_BF (2) 的特化
template<>
struct TypeSelector<TYPE_BF, WIDTH_16> {
    using type = bfloat16_t; // 16位BFloat
};

template<>
struct TypeSelector<TYPE_BF, WIDTH_32> {
    using type = float;      // 32位BFloat，使用float表示
};

// 便捷的类型别名模板
template<int TYPE, int WIDTH>
using DataType = typename TypeSelector<TYPE, WIDTH>::type;




#endif /* OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_TYPE_UTILS_H_ */
