/*
 * type_utils_c.h
 *
 *  Created on: 2025年7月24日
 *      Author: 1572
 */

#ifndef OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_TYPE_UTILS_C_H_
#define OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_TYPE_UTILS_C_H_

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 运算符类型定义
typedef enum {
    OP_ADD = 0,      // 加法
    OP_SUB = 1,      // 减法
    OP_MUL = 2,      // 乘法
    OP_DIV = 3,      // 除法
    OP_AND = 4,      // 按位与
    OP_OR = 5,       // 按位或
    OP_XOR = 6,      // 按位异或
    OP_MAX = 7,      // 最大值
    OP_MIN = 8,      // 最小值
} operation_t;

// 主要的运算函数 - 使用type_utils.h中的DataType模板
uint32_t type_based_operation(int type, int width, operation_t op, uint32_t a, uint32_t b);

// 获取指定类型的特殊值
uint32_t get_type_zero(int type, int width);         // 获取0值
uint32_t get_type_min(int type, int width);          // 获取最小值
uint32_t get_type_max(int type, int width);          // 获取最大值

uint32_t get_type_size(int type, int width);         // 获取类型大小
uint32_t get_uint32_t_from_float32(float input, int type, int width); // 将float转换为指定类型的uint32_t
float get_float32_from_uint32_t(uint32_t input, int type, int width); // 将指定类型的uint32_t转换为float

#ifdef __cplusplus
}
#endif




#endif /* OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_TYPE_UTILS_C_H_ */
