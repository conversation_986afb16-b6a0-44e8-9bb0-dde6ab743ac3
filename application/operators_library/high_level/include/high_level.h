/*
 * high_level.h
 *
 *  Created on: 2025年6月26日
 *      Author: zqguo
 */

#ifndef OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_HIGH_LEVEL_H_
#define OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_HIGH_LEVEL_H_

#include "software_port_data.h"
#include "hardware_inst_data.h"
#include <math.h>
#include <stdbool.h>

//common.c
#define IS_ODD(x)          ((x) & 0x1)
uint32_t float32_to_float16(float input);
uint32_t float32_to_bf16(float input);
void wr_lmem_n(uint32_t addr, uint32_t data, int size_in_bits, int *npu_mask);
void v_wr_lmem_n(uint32_t addr, uint32_t *data_array, int size_in_bits, int *npu_mask);
void v_rd_lmem_n(uint32_t addr, int size_in_bits, uint32_t *rd_lmem_return, int *npu_mask);


// ==================== 矩阵运算操作 ====================
/**
 * @brief 通用矩阵乘法（GEMM: General Matrix Multiply）
 * 
 * 执行矩阵乘法运算：tensor_out = tensor_in × tensor_wt
 * 支持CIM（Compute-In-Memory）加速
 * 
 * @param tensor_in    输入矩阵A (M×K)，限制配置成INT4,8、FP16、BF16
 * @param tensor_wt    输入权重B (KxN)，限制配置成INT4,8、FP16、BF16，在调用gemm之前需用load载入tensor_wt，并将wt信息更新至cim_option
 * @param tensor_out   输出矩阵C (M×N)，限制配置成INT16,32、FP16,32、BF16
 * @param tensor_orig  输出矩阵D (M×N)，限制配置成INT16,32、FP16,32、BF16，通常为原始输出，和C累加
 * @param cim_option   CIM计算选项，包含权重精度、地址和是否累加、激活等配置
 * @param npu_mask     NPU核掩码，指定参与计算的NPU核
 */
void gemm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask);

/**
 * @brief 通用矩阵向量乘法（GEMV: General Matrix-Vector Multiply）
 * 
 * 执行向量与矩阵的乘法：tensor_out = tensor_in × tensor_wt
 * 针对向量输入运算优化，减少内存访问
 * 
 * @param tensor_in    输入矩阵A (M×K)，限制配置成INT4,8、FP16、BF16
 * @param tensor_wt    输入权重B (KxN)，限制配置成INT4,8、FP16、BF16，在调用gemm之前需用load载入tensor_wt，并将wt信息更新至cim_option
 * @param tensor_out   输出矩阵C (M×N)，限制配置成INT16,32、FP16,32、BF16
 * @param tensor_orig  输出矩阵D (M×N)，限制配置成INT16,32、FP16,32、BF16，通常为原始输出，和C累加
 * @param cim_option   CIM计算选项，包含权重精度、地址和是否累加、激活等配置
 * @param npu_mask     NPU核掩码，指定参与计算的NPU核
 */
void gemv(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask);



// ==================== 激活函数操作 ====================
/**
 * @brief 基于查找表的神经网络激活函数
 * 
 * 使用查找表（LUT）实现各种非线性激活函数，支持自定义函数
 * 适用于需要高精度或特殊激活函数的场景
 * 
 * @param tensor_in      输入张量，限制配置FP16,32、BF16
 * @param tensor_out     输出张量，限制配置FP16,32、BF16
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param nn_lut         神经网络查找表，定义激活函数映射
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void nn_lut_base(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, NN_LUT *nn_lut, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 指数函数 e^x
 * 
 * 计算输入张量每个元素的自然指数
 * 常用于softmax等操作的中间步骤
 * 
 * @param tensor_in      输入张量，输入值域为[-10,0]
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void exp_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief Sigmoid激活函数
 * 
 * 计算 σ(x) = 1 / (1 + e^(-x))
 * 输出范围[0, 1]，常用于二分类和门控机制
 * 
 * @param tensor_in      输入张量，输入值域为[0,2]
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void sigmoid_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 自然对数函数 ln(x)
 * 
 * 计算输入张量每个元素的自然对数
 * 要求输入值大于0
 * 
 * @param tensor_in      输入张量，输入值域[0,2]
 * @param tensor_out     输出张量，元素值为ln(x)
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void log_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 双曲正切激活函数
 * 
 * 计算 tanh(x) = (e^x - e^(-x)) / (e^x + e^(-x))
 * 输出范围[-1, 1]，常用于RNN/LSTM
 * 
 * @param tensor_in      输入张量，输入值域[1,100]
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void tanh_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief GELU激活函数（高斯误差线性单元）
 * 
 * 计算 GELU(x) = x * Φ(x)，其中Φ(x)是高斯分布的累积分布函数
 * 广泛用于Transformer模型（BERT、GPT等）
 * 
 * @param tensor_in      输入张量，输入值域为[-5,5]
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void gelu_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 倒数函数 1/x
 * 
 * 计算输入张量每个元素的倒数
 * 注意：输入不能为0
 * 
 * @param tensor_in      输入张量，输入值域为[1,100]
 * @param tensor_out     输出张量，元素值为1/x
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void reciprocal_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 平方根函数 √x
 * 
 * 计算输入张量每个元素的平方根
 * 要求输入值非负
 * 
 * @param tensor_in      输入张量，输入值域为[1,80]
 * @param tensor_out     输出张量，元素值为√x
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void squareroot_v1(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief SiLU激活函数（Sigmoid Linear Unit）
 * 
 * 计算 SiLU(x) = x * sigmoid(x)
 * 也称为Swish函数，在某些模型中表现优于ReLU
 * 
 * @param tensor_in      输入张量，输入值域为[-10,10]
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void silu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief Softmax激活函数
 * 
 * 计算 softmax(x_i) = (e^(x_i)-e^max) / Σ(e^(x_i)-e^max)
 * 将输入归一化为概率分布，常用于多分类输出层
 * 
 * @param tensor_in      输入张量，输入值域为[-10,10]
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void softmax(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

void mask_softmax(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);


// ==================== 元素级操作 ====================
/**
 * @brief 张量加法
 * 
 * 逐元素相加：tensor_out = tensor_in1 + tensor_in2
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量（三个张量维度信息一致）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void add(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量减法
 * 
 * 逐元素相减：tensor_out = tensor_in1 - tensor_in2
 * 
 * @param tensor_in1     被减数张量
 * @param tensor_in2     减数张量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void sub(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量乘法（逐元素）
 * 
 * 逐元素相乘：tensor_out = tensor_in1 * tensor_in2
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void mul(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 相等比较
 * 
 * 逐元素比较：tensor_out[i] = (tensor_in1[i] == tensor_in2[i]) ? 1 : 0
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量（0或1）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void equal(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 不等比较
 * 
 * 逐元素比较：tensor_out[i] = (tensor_in1[i] != tensor_in2[i]) ? 1 : 0
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量（0或1）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void not_equal(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 小于比较
 * 
 * 逐元素比较：tensor_out[i] = (tensor_in1[i] < tensor_in2[i]) ? 1 : 0
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量（0或1）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void less(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 小于等于比较
 * 
 * 逐元素比较：tensor_out[i] = (tensor_in1[i] <= tensor_in2[i]) ? 1 : 0
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量（0或1）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void less_or_equal(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 大于比较
 * 
 * 逐元素比较：tensor_out[i] = (tensor_in1[i] > tensor_in2[i]) ? 1 : 0
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量（0或1）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void greater(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 大于等于比较
 * 
 * 逐元素比较：tensor_out[i] = (tensor_in1[i] >= tensor_in2[i]) ? 1 : 0
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量（0或1）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void greater_or_equal(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 逻辑与操作
 * 
 * 逐元素逻辑与：tensor_out[i] = tensor_in1[i] && tensor_in2[i]
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void and(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 逻辑或操作
 * 
 * 逐元素逻辑或：tensor_out[i] = tensor_in1[i] || tensor_in2[i]
 * 
 * @param tensor_in1     第一个输入布尔张量
 * @param tensor_in2     第二个输入布尔张量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void or(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 逻辑非操作
 * 
 * 逐元素逻辑非：tensor_out[i] = !tensor_in[i]
 * 
 * @param tensor_in1     输入张量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void not(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 取负操作
 * 
 * 逐元素取负：tensor_out[i] = -tensor_in[i]
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void neg(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 类型转换
 * 
 * 将张量从一种数据类型转换为另一种
 * 支持int8/int16/int32/fp16/fp32等类型间转换，具体详见指令集文档
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     输出张量（具有目标数据类型）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void cast(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

// ==================== 量化/反量化操作 ====================
/**
 * @brief 分组反量化GEMM操作
 * 
 * 执行带有分组量化的矩阵乘法，支持权重（INT/FP）和激活（FP/BF）的反量化，
 * 用于量化神经网络的推理加速，反量化分组为64、18、256
 * 
 * @param tensor_in      输入张量A，限制配置成INT4,8、FP16、BF16
 * @param tensor_wt      权重张量B，限制配置成INT4,8
 * @param tensor_out     输出张量B，限制配置成FP32
 * @param tensor_orig    输出原始张量，限制配置成FP32，通常为原始输出，和C累加
 * @param offset_en      是否启用偏移量
 * @param cim_option     CIM计算选项，包含权重精度、地址和是否累加、激活等配置
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param scale_out_fp   输出缩放因子（浮点），一般为一个向量，向量长度和输出维度相关
 * @param offset_wt_int  权重偏移量（整数），一般为一个向量，向量长度和输出维度相关
 * @param offset_in_int  输入偏移量（整数），一般为一个向量，向量长度和输出维度相关
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */void dequant_gemm_group(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, Tensor *tensor_wt, int offset_en, CIM_Option *cim_option, VP_Option *vp_option, Tensor *scale_out_fp, Tensor *offset_wt_int, Tensor *offset_in_int, int *npu_mask);



// ==================== 数据传输操作 ====================
/**
 * @brief 加载数据
 * 
 * 从外部存储器DRAM加载数据到NPU本地存储器Scratchpad
 * 
 * @param tensor_in      源张量（Global Memory DRAM）
 * @param tensor_out     目标张量（Local Memory Scratchpad）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void load(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);

/**
 * @brief 转置加载
 * 
 * 加载数据的同时进行转置操作，输入输出dim0和dim1转置
 * 优化了矩阵转置的内存访问模式
 * 
 * @param tensor_in      源张量（Global Memory DRAM）
 * @param tensor_out     目标张量（Local Memory Scratchpad）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void trans_load(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);

/**
 * @brief 存储数据
 * 
 * 将NPU本地存储器Scratchpad的数据存储到外部存储器DRAM
 * 
 * @param tensor_in      源张量（Local Memory Scratchpad）
 * @param tensor_out     目标张量（Global Memory DRAM）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void store(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);


/**
* @brief 多核All-Reduce操作函数
* 
* 该函数在多个NPU核心之间执行All-Reduce操作，将分布在各个核心的数据进行求和、求最大值和求最小值，
* 并将结果广播回所有参与的核心。支持大tensor的分块处理。
* 
* @param tensor_in        输入张量。待All-Reduce的输入张量
* @param tensor_out       输出张量。All-Reduce完成后，所有核心都持有相同的Reduce结果
* @param num_blocks       数据分块数量。用于处理大型tensor，将其分成多个块依次处理
* @param intermemory      中间内存数组，用于存储临时数据和核间通信缓冲区，要求和tensor_in、tensor_out不在一个Scratchpad
*                         - memory[0]: 用于存储临时张量数据
* @param vp_option        向量处理选项（标量、操作类型、舍入模式等）可传入max、min、sum操作
* @param dest_idx_16      目标核心索引数组（长度16），指定数据发送的目标NPU核心ID
* @param src_idx_16       源核心索引数组（长度16），指定数据发送的源NPU核心ID
*                         - [7:0]位:  控制NPU Core ID的参与状态
*                         - [15:8]位: 控制NPU GROUP的参与状态
*                         - [23:16]位:控制CHIP ID x的参与状态
*                         - [23:16]位:控制CHIP ID y的参与状态
* @param dest_addr_16     目标地址数组（长度16），每个核心的目标内存地址
*                         在分块处理时，该地址会根据块偏移进行更新
* @param src_addr_16      源地址数组（长度16），每个核心的源数据地址
*                         在分块处理时，该地址会根据块偏移进行循环更新（环形缓冲）
* @note 算法流程：
*       1. 解析参与计算的NPU核心（从src_idx_16生成npu_mask）
*       2. 建立Ring拓扑结构（每个核心连接到下一个核心）
*       3. 对每个数据块执行：
*          a. Reduce-Scatter阶段：每个核心负责累加一部分数据
*          b. All-Gather阶段：每个核心广播自己负责的部分给其他核心
*       4. 处理地址更新（支持环形缓冲区模式）
*/
void allreduce(Tensor *tensor_in, Tensor *tensor_out, int num_blocks, InterMemoryArray *intermemory, VP_Option *vp_option, uint32_t *dest_idx_16, uint32_t *src_idx_16, uint32_t *dest_addr_16, uint32_t *src_addr_16);


/**
 * @brief 核间数据移动
 * 
 * 在不同NPU核之间移动数据，通过NoC实现，要求核与核之间互相邻近
 * 支持灵活的源/目标核映射
 * 
 * @param tensor_in      要传输的输入张量
 * @param dest_idx_16    16个目标核索引数组
 * @param src_idx_16     16个源核索引数组
 * @param dest_addr_16   16个目标地址数组
 * @param src_addr_16    16个源地址数组
 */
void v_intercore_mov(Tensor *tensor_in, uint32_t *dest_idx_16, uint32_t *src_idx_16, uint32_t *dest_addr_16, uint32_t *src_addr_16);


// ==================== MLP操作 ====================
/**
 * @brief LLaMA3.2 旋转位置编码（RoPE）
 * 
 * 应用旋转位置编码，用于Transformer模型的位置信息注入
 * 通过复数域的旋转实现相对位置编码
 * 
 * @param tensor_in      输入张量（查询或键向量）
 * @param tensor_out     输出张量（应用RoPE后）
 * @param tensor_sin     正弦位置编码表
 * @param tensor_cos     余弦位置编码表
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void rope_llama(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_sin, Tensor *tensor_cos, InterMemoryArray *intermemory, VP_Option *vp_option, int *npu_mask);

/**
 * @brief MiniCPM旋转位置编码（RoPE）
 * 
 * 应用旋转位置编码，用于Transformer模型的位置信息注入
 * 通过复数域的旋转实现相对位置编码
 * 
 * @param tensor_in      输入张量（查询或键向量）
 * @param tensor_out     输出张量（应用RoPE后）
 * @param tensor_sin     正弦位置编码表
 * @param tensor_cos     余弦位置编码表
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void rope_minicpm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_sin, Tensor *tensor_cos, InterMemoryArray *intermemory, VP_Option *vp_option, int *npu_mask);


// ==================== 归一化操作 ====================
/**
 * @brief RMS归一化（Root Mean Square Normalization）
 * 
 * 计算 RMSNorm(x) = x / sqrt(mean(x²) + ε) * scale
 * 相比LayerNorm省略了均值计算，计算效率更高
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     归一化后的输出张量
 * @param tensor_scale   缩放参数（可学习，编译器传入）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void rmsnorm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_scale, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 层归一化（Layer Normalization）
 * 
 * 计算 LayerNorm(x) = (x - mean(x)) / sqrt(var(x) + ε) * scale + offset
 * 在特征维度上进行归一化，常用于Transformer
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     归一化后的输出张量
 * @param tensor_scale   缩放参数（可学习，编译器传入）
 * @param tensor_offset  偏移参数（可学习，编译器传入）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void layernorm(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_scale, Tensor *tensor_offset, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

// ==================== 形状操作 ====================
/**
 * @brief 张量转置
 * 
 * 交换张量的dim0和dim1维度
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     转置后的输出张量
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void transpose(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);

/**
 * @brief 数据移动
 * 
 * 简单的张量复制操作，保持形状不变
 * 可用于数据对齐，支持输入数据非对齐
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     输出张量（数据副本）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void mov(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);

/**
 * @brief 相同标量广播
 * 
 * 将相同标量值广播到指定NPU Core，整个张量所有元素设为相同值
 * 常用于初始化或常数张量创建
 * 
 * @param scalar_in      输入标量值
 * @param tensor_out     输出张量（所有元素等于标量值）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void broadcast(uint32_t scalar_in, Tensor *tensor_out, int *npu_mask);

/**
 * @brief 不同标量广播
 * 
 * 将不同标量值广播到指定NPU Core，整个张量所有元素设为相同值
 * 常用于初始化或常数张量创建
 * 
 * @param scalar_in_16   输入标量值向量形式
 * @param tensor_out     输出张量（所有元素等于标量值）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void v_broadcast(uint32_t *scalar_in_16, Tensor *tensor_out, int *npu_mask);

/**
 * @brief 维度置换
 * 
 * 按指定顺序重新排列张量的维度
 * 比transpose更通用，可以同时改变多个维度顺序
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     维度置换后的输出张量
 * @param dim_axis       维度置换规则，分别为‘102’、‘102’、‘120’、‘201’、‘210’
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void permute(Tensor *tensor_in, Tensor *tensor_out, int dim_axis, InterMemoryArray *intermemory, int *npu_mask);


/**
 * @brief 条件选择操作
 * 
 * 根据条件从两个输入张量中选择元素
 * 当条件满足时选择tensor_in1的元素，否则选择tensor_in2的元素
 * 
 * @param tensor_in1     条件为真时选择的张量
 * @param tensor_in2     条件为假时选择的张量  
 * @param tensor_out     输出张量
 * @param operator       比较操作符（如OPERATION_EQUAL, OPERATION_GREATER等）
 * @param value          比较值
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void where(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, uint32_t operator, uint32_t value, int *npu_mask);



/**
 * @brief 张量拼接
 * 
 * 沿指定维度拼接两个张量
 * 要求除拼接维度外其他维度大小相同
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     拼接后的输出张量
 * @param dim_axis       拼接的维度索引
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void concat(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, int dim_axis, int *npu_mask);

/**
 * @brief 张量扩展
 * 
 * 将张量在指定维度上进行复制扩展，动态根据输出维度判断
 * 常用于广播操作的显式实现
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     扩展后的输出张量
 * @param dim_axis       拼接的维度索引
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void expand(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 张量展平
 * 
 * 将多维张量展平为一维向量
 * 保持元素总数不变，按dim0方向顺序排列，默认256bits对齐
 * 
 * @param tensor_in      输入多维张量
 * @param tensor_out     输出一维张量
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void flatten(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);



// ==================== 归约操作 ====================
/**
 * @brief 沿第0维度执行归约操作（NPU版本）
 * 
 * 对输入张量的第0维进行归约操作
 * 此函数不返回中间结果，归约结果直接写入tensor_out
 * 归约后第0维大小变为1，支持支持输入三维/二维，输出降维至二维/一维
 * 
 * 操作示例：
 * - 输入张量形状：[B, H, W]
 * - 输出张量形状：[1, H, W]
 * - 对于每个[h,w]位置，跨所有B进行归约
 * 
 * @param tensor_in      输入张量，包含要归约的数据
 * @param tensor_out     输出张量，存储归约结果
 *                       - 第0维大小必须为1
 *                       - 其他维度与输入张量相同
 * @param vp_option      向量处理选项，指定归约操作类型：
 *                       - OPERATION_ADD：求和
 *                       - OPERATION_MAX：最大值
 *                       - OPERATION_MIN：最小值
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */ 
void reduce_dim0_base_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);


/**
 * @brief 最大值归约
 * 
 * 沿dim0维度找出最大值，默认输入输出都在Scratchpad
 * 可用于最大池化或argmax的前处理
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     归约后的降一维张量（包含最大值）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void reducemax_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 最小值归约
 * 
 * 沿dim0维度找出最小值，默认输入输出都在Scratchpad
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     归约后的降一维张量（包含最小值）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void reducemin_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 平均值归约
 * 
 * 沿dim0维度找出平均值，默认输入输出都在Scratchpad
 * 常用于全局平均池化或统计计算
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     归约后的张量（包含平均值）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void reducemean_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 求和归约
 * 
 * 沿dim0维度找出总和值，默认输入输出都在Scratchpad
 * 基础归约操作，广泛用于各种计算
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     归约后的张量（包含求和结果）
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void reducesum_npu(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 沿第0维度执行归约操作（RISC-V版本）
 * 
 * 功能与reduce_dim0_base_npu相同，但每个NPU core的归约结果返回至RISC-V
 * rv后缀表示"return value"，即带返回值版本
 * reduce_return必须由调用者分配，大小至少为16个uint32_t
 * 
 * @param tensor_in      输入张量，限定一维向量
 * @param tensor_out     输出张量，指定输出标量的数据精度信息和维度信息，限定标量
 *                       - 所有维度大小必须为1
 * @param vp_option      向量处理选项，指定归约操作类型：
 *                       - OPERATION_ADD：求和
 *                       - OPERATION_MAX：最大值
 *                       - OPERATION_MIN：最小值
 * @param[out] reduce_return_16  返回值数组，大小必须为16（NPU_CORES）
 *                       - 存储每个NPU核的局部归约结果
 *                       - 未参与计算的核对应值为0或未定义
 *                       - 数组索引对应NPU核ID（0-15）
 * @param npu_mask       NPU核掩码数组，指定参与计算的核
 */ 
void reduce_dim0_base_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask);

/**
 * @brief 执行最大值归约操作（带返回值版本）
 * 
 * 对输入张量执行元素级最大值归约，同时返回每个NPU核的局部最大值
 * 
 * @param tensor_in      一维输入向量
 * @param tensor_out     输出标量，指定数据精度
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param reduce_return_16    返回16个NPU核的局部最大值数组
 * @param npu_mask       NPU核掩码数组，指定参与计算的核
 */ 
void reducemax_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask);

/**
 * @brief 执行最小值归约操作（带返回值版本）
 * 
 * 对输入张量执行元素级最小值归约，同时返回每个NPU核的局部最小值
 * 
 * @param tensor_in      一维输入向量
 * @param tensor_out     输出标量，指定数据精度
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param reduce_return_16    返回16个NPU核的局部最小值数组
 * @param npu_mask       NPU核掩码数组，指定参与计算的核
 */ 
void reducemin_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask);

/**
 * @brief 执行总和值归约操作（带返回值版本）
 * 
 * 对输入张量执行元素级平均值归约，同时返回每个NPU核的平均值
 * 
 * @param tensor_in      一维输入向量
 * @param tensor_out     输出标量，指定数据精度
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param reduce_return_16    返回16个NPU核的局部平均值数组
 * @param npu_mask       NPU核掩码数组，指定参与计算的核
 */ 
void reducemean_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask);

/**
 * @brief 执行总和值归约操作（带返回值版本）
 * 
 * 对输入张量执行元素级总和值归约，同时返回每个NPU核的总和值
 * 
 * @param tensor_in      一维输入向量
 * @param tensor_out     输出标量，指定数据精度
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param reduce_return_16    返回16个NPU核的局部总和值数组
 * @param npu_mask       NPU核掩码数组，指定参与计算的核
 */ 
void reducesum_rv(Tensor *tensor_in, Tensor *tensor_out, VP_Option *vp_option, uint32_t *reduce_return_16, int *npu_mask);


/**
 * 多核argmax函数
 * 在多个NPU核心上并行计算tensor的最大值索引，使用二分法优化查找过程
 * 每个核心上的tensor_in包含该核心负责的数据部分（一维向量，只在dim0上分布）
 * 
 * @param tensor_in: 输入张量（一维向量，dim1=1, dim2=1，每个核心上都有对应的数据）
 * @param argmax_out: 输出张量，存储argmax结果（标量张量，shape: (1, 1, 1)）
 * @param vp_option: 向量处理选项配置
 * @param intermemory: 中间内存数组，需要至少1个内存块
 *                     memory[0]: temp_compare_result - 比较结果张量，二分法过程中重用于区间最大值计算
 *                                同时用于equal操作结果存储，通过reduce_scalar_max读取比较结果
 * @param npu_mask: NPU掩码，指定参与计算的核心
 * @param max_index: 输出参数，存储全局最大值的索引
 * @param max_value: 输出参数，存储全局最大值
 */

void arguemax(Tensor *tensor_in, Tensor *argmax_out, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask, uint32_t *max_index, uint32_t *max_value);


/**
 * 多核TopK函数 - 使用多核argmax循环查找前k个最大值并返回索引数组
 * 通过多次调用multicore_argmax并将找到的最大值位置置零来实现
 * 
 * @param tensor_in: 输入张量（一维向量，dim1=1, dim2=1，每个核心上都有对应的数据）
 * @param topk_out_index: 输出数组，存储前k个最大值的索引，需要调用者预先分配足够空间
 * @param topk_out_value: 输出数组，存储前k个最大值的值，需要调用者预先分配足够空间
 * @param k: 需要查找的top-k个最大值的数量
 * @param vp_option: 向量处理选项配置
 * @param intermemory: 中间内存数组，需要至少3个内存块
 *                     memory[0]: 用于multicore_argmax的中间计算
 *                     memory[1]: tensor_in的工作副本，用于逐步置零找到的最大值
 *                     memory[2]: argmax结果存储，避免与multicore_argmax内部冲突
 * @param npu_mask: NPU掩码，指定参与计算的核心
 */
void topk(Tensor *tensor_in, uint32_t *topk_out_index, uint32_t *topk_out_value, int k, VP_Option *vp_option, InterMemoryArray *intermemory, int *npu_mask);

//Intermediate operation
// ==================== NICE操作 ====================
// NICE 为16个NPU核提供了灵活的张量、向量、标量运算能力
/**
 * @brief 通用张量-张量操作
 * 
 * 执行两个张量之间的通用操作，具体操作类型由vp_option指定
 * 通过向量计算遍历张量dim1*dim2
 * 支持逐元素运算，要求两个张量形状相同和满足广播规则
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入张量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void tensor_tensor_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 通用张量-向量操作
 * 
 * 执行张量与向量之间的通用操作，具体操作类型由vp_option指定
 * 通过向量计算遍历张量dim1*dim2
 * 支持逐元素运算，对于形状[C,H,W]的张量和长度C的向量，向量会加到每个[:,H,W]切片
 * 
 * @param tensor_in1     第一个输入张量
 * @param tensor_in2     第二个输入向量
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void tensor_vector_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量加法
 */
void add_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量减法
 */
void sub_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量乘法
 */
void mul_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量等于比较
 */
void equal_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量不等比较
 */
void not_equal_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量小于比较
 */
void less_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量小于等于比较
 */
void less_or_equal_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量大于比较
 */
void greater_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量向量大于等于比较
 */
void greater_or_equal_tensor_vector(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);


// ==================== NICE操作 ====================
// NICE 支持16个NPU核的标量化操作，每个核可以使用相同的标量值
/**
 * @brief 标量化张量-标量操作（通用）
 * 
 * 对16个NPU核分别执行张量与标量的操作，每个核可以使用相同的标量值
 * 执行张量与标量的通用操作，标量在硬件层面会广播到张量的所有元素
 * 实现了数据并行的标量操作，提高了灵活性
 * 
 * @param tensor_in1     输入张量
 * @param tensor_in2     输入张量（输入标量的维度信息）
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void tensor_scalar_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量加法
 */
void add_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量减法
 */
void sub_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量乘法
 */
void mul_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量相等比较
 */
void equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量不等比较
 */
void not_equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量小于比较
 */
void less_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量小于等于比较
 */
void less_or_equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量大于比较
 */
void greater_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

/**
 * @brief 张量标量大于等于比较
 */
void greater_or_equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);

// ==================== VNICE操作 ====================
// VNICE (Vector NICE) 支持16个NPU核的向量化操作，每个核可以使用不同的标量值
/**
 * @brief 向量化张量-标量操作（通用）
 * 
 * 对16个NPU核分别执行张量与标量的操作，每个核可以使用不同的标量值
 * 执行张量与标量的通用操作，标量在硬件层面会广播到张量的所有元素
 * 实现了数据并行的标量操作，提高了灵活性
 * 
 * @param tensor_in1     输入张量
 * @param tensor_in2     输入张量（输入标量的维度信息）
 * @param tensor_out     输出张量
 * @param vp_option      向量处理选项（标量、操作类型、舍入模式等）
 * @param scalar_in_16   16个标量值数组，每个NPU核使用对应的标量
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void v_tensor_scalar_operator(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量加法
 * 每个NPU核使用自己的标量值进行加法操作
 */
void v_add_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量减法
 * 每个NPU核使用自己的标量值进行减法操作
 */
void v_sub_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量乘法
 * 每个NPU核使用自己的标量值进行乘法操作
 */
void v_mul_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量等于比较
 * 每个NPU核使用自己的标量值进行等于比较
 */
void v_equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量不等比较
 * 每个NPU核使用自己的标量值进行不等比较
 */
void v_not_equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量小于比较
 * 每个NPU核使用自己的标量值进行小于比较
 */
void v_less_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量小于等于比较
 * 每个NPU核使用自己的标量值进行小于等于比较
 */
void v_less_or_equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量大于比较
 * 每个NPU核使用自己的标量值进行大于比较
 */
void v_greater_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);

/**
 * @brief 向量化张量标量大于等于比较
 * 每个NPU核使用自己的标量值进行大于等于比较
 */
void v_greater_or_equal_tensor_scalar(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);



// ==================== 维度扩展中间操作 ====================
/**
 * @brief 在第0维扩展张量
 * 
 * 在dim0维度上复制张量
 * 例如：[1,H,W] -> [C,H,W]，其中每个C维度包含相同的[1,H,W]数据
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     扩展后的输出张量（第0维大小增加）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void expand_dim0(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
/**
 * @brief 在第1维扩展张量
 * 
 * 在dim1维度上复制张量
 * 例如：[C,1,W] -> [C,H,W]，其中每个H维度包含相同的[C,1,W]数据
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     扩展后的输出张量（第1维大小增加）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void expand_dim1(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
/**
 * @brief 在第2维扩展张量
 * 
 * 在dim2维度上复制张量
 * 例如：[C,H,1] -> [C,H,W]，其中每个H维度包含相同的[C,H,1]数据
 * 
 * @param tensor_in      输入张量
 * @param tensor_out     扩展后的输出张量（第2维大小增加）
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void expand_dim2(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);


// ==================== 维度置换中间操作 ====================
/**
 * @brief 维度置换：[0,1,2] -> [0,2,1]
 * 
 * 交换第1维和第2维，保持第0维不变
 * 
 * @param tensor_in      输入张量 [D0,D1,D2]
 * @param tensor_out     输出张量 [D0,D2,D1]
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void permute_021(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
/**
 * @brief 维度置换：[0,1,2] -> [1,2,0]
 * 
 * 循环右移所有维度
 * 
 * @param tensor_in      输入张量 [D0,D1,D2]
 * @param tensor_out     输出张量 [D1,D2,D0]
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void permute_120(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 维度置换：[0,1,2] -> [2,0,1]
 * 
 * 将最后一个维度移到最前面
 * 
 * @param tensor_in      输入张量 [D0,D1,D2]
 * @param tensor_out     输出张量 [D2,D0,D1]
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void permute_201(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask);

/**
 * @brief 维度置换：[0,1,2] -> [2,1,0]
 * 
 * 反转第0维和第2维，保持第1维不变
 * 
 * @param tensor_in      输入张量 [D0,D1,D2]
 * @param tensor_out     输出张量 [D2,D1,D0]
 * @param intermemory    中间内存数组，用于临时存储
 * @param npu_mask       NPU核掩码，指定参与计算的NPU核
 */
void permute_210(Tensor *tensor_in, Tensor *tensor_out, InterMemoryArray *intermemory, int *npu_mask);


// ==================== 矩阵运算、向量运算和张量变换寄存器配置初始化 ====================
/**
* @brief 初始化向量处理器（Vector Processor）配置
* 
* 配置VP单元执行向量运算所需的参数，包括输入输出张量和运算选项
* 
* @param tensor_in1  第一个输入张量
* @param tensor_in2  第二个输入张量
* @param tensor_out  输出张量
* @param vp_option   向量处理选项（标量、操作类型、舍入模式等）
*/
void init_vp_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option);

/**
* @brief 初始化矩阵处理器（Matrix Processor）配置
* 
* 配置MP/CIM（Compute-In-Memory）单元执行矩阵运算所需的参数
* 
* @param tensor_in   输入张量
* @param tensor_out  输出张量
* @param tensor_orig 原始张量（可能用于残差连接或参考）
* @param cim_option  存内计算选项（如计算模式、位宽配置等）
*/
void init_mp_cfg(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option);

/**
* @brief 初始化张量搬移（Tensor Move）配置
* 
* 配置TM单元在不同存储层级间搬移张量数据的参数
* 
* @param tensor_in   输入张量（源位置）
* @param tensor_out  输出张量（目标位置）
*/
void init_tm_cfg(Tensor *tensor_in, Tensor *tensor_out);


#endif /* OPERATORS_LIBRARY_HIGH_LEVEL_INCLUDE_HIGH_LEVEL_H_ */
