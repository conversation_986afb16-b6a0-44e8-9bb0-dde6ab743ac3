/*
 * vnice_inst_single.h
 *
 *  Created on: 2025年7月3日
 *      Author: zqguo
 */

#ifndef OPERATORS_LIBRARY_INTRINSIC_VNICE_INST_SINGLE_H_
#define OPERATORS_LIBRARY_INTRINSIC_VNICE_INST_SINGLE_H_

#include <stdint.h>
#include <riscv_vector.h>
#include <nuclei_sdk_soc.h>



//RISC-V Write Local Memory Primitive
//      base_addr_lmem:          配置要写入的Local Memory的起始地址
//      wr_data_lmem_4:          配置要写入的Local Memory的数据
__STATIC_FORCEINLINE void v_wr_lmem_drv(uint32_t base_addr_lmem, uint32_t *wr_data_lmem_4){
    vuint32m1_t wr_data_lmem;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK);  
    wr_data_lmem = __riscv_vle32_v_u32m1(wr_data_lmem_4, vl);

    asm volatile(
            "vsetivli zero,4,e32,m1,ta,ma\n"
            ".insn r 0x2b, 3, %0, x0, %1, %2" 
            : :"i"(V_WR_LMEM_DRV), "r"(base_addr_lmem), "vr"(wr_data_lmem));
}


//RISC-V Read Local Memory Primitive
//      base_addr_lmem:          配置要读取的Local Memory的起始地址
//      rd_data_lmem_4:          函数返回要读取的Local Memory的数据，数据类型默认INT32
__STATIC_FORCEINLINE void v_rd_lmem_drv(uint32_t base_addr_lmem, uint32_t *rd_data_lmem_4){
    vuint32m1_t scalar_in_vec;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK); 

    asm volatile(
            "vsetivli zero,4,e32,m1,ta,ma\n"
            ".insn r 0x2b, 6, %1, %0, %2, x0" 
            : "=vr"(scalar_in_vec) :"i"(V_RD_LMEM_DRV), "r"(base_addr_lmem));

    __riscv_vse32_v_u32m1(rd_data_lmem_4, scalar_in_vec, vl);
}


//NoC Primitive
//      base_addr_srcmem_4:        配置要读取的源NPU Core Local/Global Memory的起始地址
//      dest_idx_4:                配置要写入的目的NPU Core的索引
__STATIC_FORCEINLINE void v_noc_src_drv(uint32_t *base_addr_srcmem_4, uint32_t *dest_idx_4){
    vuint32m1_t base_addr_srcmem;
    vuint32m1_t dest_idx;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK); 

    base_addr_srcmem = __riscv_vle32_v_u32m1(base_addr_srcmem_4, vl);
    dest_idx = __riscv_vle32_v_u32m1(dest_idx_4, vl);

    asm volatile(
            "vsetivli zero,4,e32,m1,ta,ma\n"
            ".insn r 0x2b, 3, %0, x0, %1, %2" 
            : :"i"(V_NOC_SRC_DRV), "vr"(base_addr_srcmem), "vr"(dest_idx));
}
//      base_addr_destmem_4:      配置要写入的目的Local/Global Memory的起始地址
//      src_idx_4:                配置要读取的源NPU Core的索引
__STATIC_FORCEINLINE void v_noc_dest_drv(uint32_t *base_addr_destmem_4, uint32_t *src_idx_4){
    vuint32m1_t base_addr_destmem;
    vuint32m1_t src_idx;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK); 

    base_addr_destmem = __riscv_vle32_v_u32m1(base_addr_destmem_4, vl);
    src_idx = __riscv_vle32_v_u32m1(src_idx_4, vl);

    asm volatile(
            "vsetivli zero,4,e32,m1,ta,ma\n"
            ".insn r 0x2b, 3, %0, x0, %1, %2" 
            : :"i"(V_NOC_DST_DRV), "vr"(base_addr_destmem), "vr"(src_idx));
}

//BC Primitive
// scalar_in_4:       输入标量的数值
// base_addr_lmem:    存储在local Memory的输出张量地址
__STATIC_FORCEINLINE void v_bc_drv(uint32_t base_addr_out, uint32_t *scalar_in_4){
    vuint32m1_t scalar_in_vec;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK);  
    scalar_in_vec = __riscv_vle32_v_u32m1(scalar_in_4, vl);

    asm volatile(
            "vsetivli zero,4,e32,m1,ta,ma\n"
            ".insn r 0x2b, 3, %0, x0, %1, %2" 
            : :"i"(V_BC_DRV), "r"(base_addr_out), "vr"(scalar_in_vec));
}

//VV_V Primitive
//base_addr_in   ：存储在local Memory的输入向量地址
//scalar_in_4    : RISC-V寄存器中传入的标量值
__STATIC_FORCEINLINE void v_vs_v_pre(uint32_t base_addr_in, uint32_t *scalar_in_4) {
    vuint32m1_t scalar_in_vec;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK);  
    scalar_in_vec = __riscv_vle32_v_u32m1(scalar_in_4, vl);

    asm volatile(
            "vsetivli zero,4,e32,m1,ta,ma\n"
            ".insn r 0x2b, 3, %0, x0, %1, %2" 
            : : "i"(V_VS_V_PRE), "r"(base_addr_in), "vr"(scalar_in_vec)); 
}

//V_S Primitive
//base_addr_in ：存储在local Memory的输入向量地址
//scalar_out   : NPU返回至RISC-V寄存器中的标量值
__STATIC_FORCEINLINE void v_v_s_drv(uint32_t base_addr_in, uint32_t *scalar_out_4) {
    vuint32m1_t scalar_out_vec;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK);  

    asm volatile(
            "vsetivli zero,4,e32,m1,ta,ma\n"
            ".insn r 0x2b, 6, %1, %0, %2, x0" 
            : "=vr"(scalar_out_vec) : "i"(V_V_S_DRV), "r"(base_addr_in)); 
    __riscv_vse32_v_u32m1(scalar_out_4, scalar_out_vec, vl);

}



#endif /* OPERATORS_LIBRARY_INTRINSIC_VNICE_INST_SINGLE_H_ */
