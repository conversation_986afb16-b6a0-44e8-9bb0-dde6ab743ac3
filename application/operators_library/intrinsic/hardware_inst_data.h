/*
 * isa_funct7.h
 *
 *  Created on: 2025年6月19日
 *      Author: zqguo
 */

#ifndef OPERATORS_LIBRARY_uint32_RINSIC_HARDWARE_INST_DATA_H_
#define OPERATORS_LIBRARY_uint32_RINSIC_HARDWARE_INST_DATA_H_

#include <stdio.h>
#include <riscv_vector.h>


//GROUP/MASK
#define MAX_MASK    4
#define MAX_GROUP   4
#define NPU_CORES   16

//NICE
//Control Peimitive
#define NPU_GROUP_MASK_DRV   0b0000000
#define NPU_CFG_DRV          0b0000001
#define WR_LMEM_DRV          0b0000010
#define RD_LMEM_DRV          0b0000011
#define SWITCH_CIMC_DRV      0b0000100
#define SYNC_DRV             0b0000101

//NOC Primitive
#define NOC_SRC_DRV          0b0010000
#define NOC_DST_DRV          0b0010001
#define NOC_FENCE_DRV        0b0010010

//Tensor Load/Store Primitive
#define TLD_DRV              0b0100000
#define TLD_INDEX_PRE        0b0100001
#define TLD_INDEX_DRV        0b0100010
#define TST_DRV              0b0100011
#define TST_INDEX_PRE        0b0100100
#define TST_INDEX_DRV        0b0100101
#define TTLD_DRV             0b0100110

//Tensor Movement Primitive
#define BC_DRV               0b0110000
#define MOV_DRV              0b0110001
#define TRAN_DRV             0b0110010
#define SETCIMEXP_DRV        0b0110011
#define GETCIMEXP_DRV        0b0110100

//Tensor Processing Primitive
#define CONV_PRE             0b1000000
#define CONV_DRV             0b1000001
#define GEMV_PRE             0b1000010
#define GEMV_DRV             0b1000011
#define GEMM_PRE             0b1000100
#define GEMM_DRV             0b1000101

//Vector Processing Primitive
#define VV_V_PRE             0b1010000
#define VV_V_DRV             0b1010001
#define VS_V_PRE             0b1010010
#define VS_V_DRV             0b1010011
#define V_S_DRV              0b1010100
#define V_V_DRV              0b1010101

//VNICE
#define V_WR_LMEM_DRV        0b0100010
#define V_RD_LMEM_DRV        0b0100011
#define V_NOC_SRC_DRV        0b1100110
#define V_NOC_DST_DRV        0b1100111
#define V_BC_DRV             0b0101110
#define V_VS_V_PRE           0b0111000
#define V_V_S_DRV            0b0111010

//Address of configure register
#define TLD_CFG_WIDTH_TYPE              0x00000000
#define TLD_CFG_SIZE_DIM0B_REM_DIM0     0x00000001
#define TLD_CFG_SIZE_DIM2_DIM1          0x00000002
#define TLD_CFG_STRIDE_DIM1_GMEM        0x00000003
#define TLD_CFG_STRIDE_DIM2_GMEM        0x00000004
#define TLD_CFG_STRIDE_DIM1_LMEM        0x00000005
#define TLD_CFG_STRIDE_DIM2_LMEM        0x00000006

#define TST_CFG_WIDTH_TYPE              0x00010000
#define TST_CFG_SIZE_DIM0B_REM_DIM0     0x00010001
#define TST_CFG_SIZE_DIM2_DIM1          0x00010002
#define TST_CFG_STRIDE_DIM1_GMEM        0x00010003
#define TST_CFG_STRIDE_DIM2_GMEM        0x00010004
#define TST_CFG_STRIDE_DIM1_LMEM        0x00010005
#define TST_CFG_STRIDE_DIM2_LMEM        0x00010006

#define TM_CFG_WIDTH_TYPE               0x00020000
#define TM_CFG_SIZE_DIM0B_REM_DIM0_IN   0x00020001
#define TM_CFG_SIZE_DIM0B_REM_DIM0_OUT  0x00020002
#define TM_CFG_SIZE_DIM2_DIM1           0x00020003
#define TM_CFG_STRIDE_DIM1_OUT          0x00020004
#define TM_CFG_STRIDE_DIM2_OUT          0x00020005
#define TM_CFG_STRIDE_DIM1_IN           0x00020006
#define TM_CFG_STRIDE_DIM2_IN           0x00020007

#define MP_CFG_OP_WIDTH_TYPE            0x00030000
#define MP_CFG_SIZE_DIM0B_REM_DIM0_OUT  0x00030001
#define MP_CFG_SIZE_DIM2_DIM1_OUT       0x00030002
#define MP_CFG_STRIDE_DIM1_OUT          0x00030003
#define MP_CFG_STRIDE_DIM2_OUT          0x00030004
#define MP_CFG_SIZE_DIM0B_REM_DIM0_IN   0x00030005
#define MP_CFG_SIZE_DIM2_DIM1_IN        0x00030006
#define MP_CFG_STRIDE_DIM1_IN           0x00030007
#define MP_CFG_STRIDE_DIM2_IN           0x00030008

#define VP_CFG_OP_WIDTH_TYPE            0x00040000
#define VP_CFG_SIZE_DIM0B_REM_DIM0_OUT  0x00040001
#define VP_CFG_SIZE_DIM0B_REM_DIM0_IN1  0x00040002
#define VP_CFG_SIZE_DIM0B_REM_DIM0_IN2  0x00040003
#define VP_CFG_SPECIAL_CASE             0x00040004

#define NOC_CFG_WIDTH_TYPE              0x00050000
#define NOC_CFG_SIZE_DIM0B_REM_DIM0     0x00050001
#define NOC_CFG_SIZE_DIM2_DIM1          0x00050002
#define NOC_CFG_STRIDE_DIM1             0x00050003
#define NOC_CFG_STRIDE_DIM2             0x00050004

// cfg type
#define TYPE_INT                        0	//0b00
#define TYPE_FP							1	//0b01
#define TYPE_BF							2	//0b10

// cfg wd
#define WIDTH_4							2   //0b010
#define WIDTH_8							3   //0b011
#define WIDTH_16						4   //0b100
#define WIDTH_32						5   //0b101

// cfg op
#define OPERATION_ADD                   0	//0b000000
#define OPERATION_SUB                   1	//0b000001
#define OPERATION_RSUB                  2	//0b000010
#define OPERATION_MIN                   8	//0b001000
#define OPERATION_MAX                   9	//0b001001
#define OPERATION_EQUAL             	16	//0b010000
#define OPERATION_NOT_EQUAL         	17	//0b010001
#define OPERATION_GREATER            	18	//0b010010
#define OPERATION_GREATER_OR_EQEAL  	19	//0b010011
#define OPERATION_LESS              	20	//0b010100
#define OPERATION_LESS_OR_EQUAL     	21	//0b010101
#define OPERATION_LEFT_SHIFT       	    24	//0b011000
#define OPERATION_RIGHT_SHIFT           25	//0b011001
// #define OPERATION_RIGHT_SHIFT_CEIL      26	//0b011010
// #define OPERATION_RIGHT_SHIFT_ROUND     27	//0b011011
#define OPERATION_AND                   32	//0b100000
#define OPERATION_OR                    33	//0b100001
#define OPERATION_XOR                   34	//0b100010
#define OPERATION_MUL                   40	//0b101000
#define OPERATION_NULL                  3

//CIMC_MODE
#define CIMC_MODE_MEMORY            0b000
#define CIMC_MODE_ROW4_COL1         0b001
#define CIMC_MODE_ROW2_COL2         0b010
#define CIMC_MODE_ROW1_COL4         0b100

//CIMC Page
#define CIMC_PAGE_BASE_ADDR			0x00400000   //0x00400000——0x0041FFFF  8KB
#define CIMC_PAGE_OFFSET 			0x00002000   //0x00000000——0x00001FFF  8KB
//Scratchpad 
#define SCRATCHPAD0_ADDR	        0x00000000   //0x00000000——0x0000FFFF  64KB     offset = 0x20
#define SCRATCHPAD1_ADDR	        0x00100000   //0x00100000——0x0010FFFF  64KB     offset = 0x20
#define SCRATCHPAD2_ADDR	        0x00200000   //0x00200000——0x0020FFFF  64KB     offset = 0x20     
#define SCRATCHPAD3_ADDR	        0x00300000   //0x00300000——0x0030FFFF  64KB     offset = 0x20

//DRAM
#define DRAM_ADDR	                0x10000000   //0x10000000——0x17FFFFFF  128MB

//hardware instruction data struct


//Tensor Load/Store Primitive
typedef struct {
    union {
        uint32_t width_type_u;
        struct {
            uint32_t type     : 2;
            uint32_t width    : 3;
            uint32_t reserved : 27;
        } width_type;
    };
    union {
        uint32_t size_dim0b_rem_dim0_u;
        struct {
            uint32_t rem_dim0   : 6;
            uint32_t size_dim0b : 11;
            uint32_t reserved   : 15;
        } size_dim0b_rem_dim0;
    };

    union {
        uint32_t size_dim2_dim1_u;
        struct {
            uint32_t size_dim1 : 13;
            uint32_t size_dim2 : 13;
            uint32_t reserved  : 6;
        } size_dim2_dim1;
    };
    uint32_t stride_dim1_gmem;   // 13
    uint32_t stride_dim2_gmem;   // 25
    uint32_t stride_dim1_lmem;   // 13
    uint32_t stride_dim2_lmem;   // 25
    uint32_t base_addr_gmem;     // 32
    uint32_t base_addr_lmem;     // 32
} LS_CONFIG;

//Tensor Movement Primitive
typedef struct {
    union {
        uint32_t width_type_u;
        struct {
            uint32_t type : 2;
            uint32_t width : 3;
            uint32_t reserved: 27;
        } width_type;
    };
    union {
        uint32_t size_dim0b_rem_dim0_in_u;
        struct {
            uint32_t rem_dim0_in    : 6;
            uint32_t size_dim0b_in  : 11;
            uint32_t reserved       : 15;
        } size_dim0b_rem_dim0_in;
    };
    union {
        uint32_t size_dim0b_rem_dim0_out_u;
        struct {
            uint32_t rem_dim0_out    : 6;
            uint32_t size_dim0b_out  : 11;
            uint32_t reserved        : 15;
        } size_dim0b_rem_dim0_out;
    };
    union {
        uint32_t size_dim2_dim1_u;
        struct {
            uint32_t size_dim1 : 13;
            uint32_t size_dim2 : 13;
            uint32_t reserved : 6;
        } size_dim2_dim1;
    };
    uint32_t stride_dim1_out;  // 13
    uint32_t stride_dim2_out;  // 25
    uint32_t stride_dim1_in;   // 13
    uint32_t stride_dim2_in;   // 25
    uint32_t scalar_in;        // 32
    uint32_t base_addr_in;     // 32
    uint32_t base_addr_out;    // 32
} TM_CONFIG;

//Tensor Processing Primitive
typedef struct {
    union {
        uint32_t op_width_type_u;
        struct {
            uint32_t type_out   : 2;
            uint32_t type_orig  : 2;
            uint32_t type_in    : 2;
            uint32_t type_wt    : 2;
            uint32_t width_out  : 3;
            uint32_t width_orig : 3;
            uint32_t width_in   : 3;
            uint32_t width_wt   : 3;
            uint32_t accu       : 1;
            uint32_t act        : 1;
            uint32_t shift      : 5;
            uint32_t reserved   : 5;
        } op_width_type;
    };
    union {
        uint32_t size_dim0b_rem_dim0_out_u;
        struct {
            uint32_t rem_dim0_out   : 6;
            uint32_t size_dim0b_out : 11;
            uint32_t reserved       : 15;
        } size_dim0b_rem_dim0_out;
    };
    union {
        uint32_t size_dim2_dim1_out_u;
        struct {
            uint32_t size_dim1_out : 13;
            uint32_t size_dim2_out : 13;
            uint32_t reserved : 6;
        } size_dim2_dim1_out;
    };
    uint32_t stride_dim1_out;        //13
    uint32_t stride_dim2_out;        //25
    union {
        uint32_t size_dim0b_rem_dim0_in_u;
        struct {
            uint32_t rem_dim0_in   : 6;
            uint32_t size_dim0b_in : 11;
            uint32_t reserved      : 15;
        } size_dim0b_rem_dim0_in;
    };
    union {
        uint32_t size_dim2_dim1_in_u;
        struct {
            uint32_t size_dim1_in : 13;
            uint32_t size_dim2_in : 13;
            uint32_t reserved     : 6;
        } size_dim2_dim1_in;
    };
    uint32_t stride_dim1_in;       //13
    uint32_t stride_dim2_in;       //25
    uint32_t base_addr_wt;         //32
    uint32_t base_addr_in;         //32
    uint32_t base_addr_out;        //32
    uint32_t base_addr_orig;       //32

} MP_CONFIG;

//Vector Processing Primitive
typedef struct {
    union {
        uint32_t op_width_type_u;
        struct{
            uint32_t type_out		: 2;
            uint32_t type_in1		: 2;
            uint32_t type_in2		: 2;
            uint32_t width_out		: 3;
            uint32_t width_in1		: 3;
            uint32_t width_in2		: 3;
            uint32_t operation   	: 6;
            uint32_t reserved       : 11;
        }op_width_type;
    };    
    union {
        uint32_t size_dim0b_rem_dim0_out_u;
		struct{
	        uint32_t rem_dim0_out    : 6;
	        uint32_t size_dim0b_out	 : 11;
            uint32_t reserved        : 15;
		} size_dim0b_rem_dim0_out;
    };
    union {
        uint32_t size_dim0b_rem_dim0_in1_u;
		struct{
	        uint32_t rem_dim0_in1    : 6;
	        uint32_t size_dim0b_in1  : 11;
            uint32_t reserved        : 15;
		} size_dim0b_rem_dim0_in1;
    };
    union {
        uint32_t size_dim0b_rem_dim0_in2_u;
		struct{
	        uint32_t rem_dim0_in2     : 6;
	        uint32_t size_dim0b_in2	  : 11;
            uint32_t reserved         : 15;
		} size_dim0b_rem_dim0_in2;
    };
    union {
        uint32_t special_case_u;
		struct{
	        uint32_t saturate      : 1;
	        uint32_t disable0	   : 1;
            uint32_t round_mode	   : 3;
            uint32_t reserved      : 27;
		} special_case;
    };
    uint32_t scalar_in2;        //32
    uint32_t base_addr_in1;    //32
    uint32_t base_addr_in2;    //32
    uint32_t base_addr_out;    //32
} VP_CONFIG;

//NOC Primitive
typedef struct {
    union {
        uint32_t width_type_u;
        struct {
            uint32_t type     : 2;
            uint32_t width    : 3;
            uint32_t reserved : 27;
        } width_type;
    };
    union {
        uint32_t size_dim0b_rem_dim0_u;
		struct{
	        uint32_t rem_dim0     : 6;
	        uint32_t size_dim0b	  : 11;
            uint32_t reserved     : 15;
		} size_dim0b_rem_dim0;
    };
    union {
        uint32_t size_dim2_dim1_u;
        struct {
            uint32_t size_dim1    : 13;
            uint32_t size_dim2    : 13;
            uint32_t reserved     : 6;
        } size_dim2_dim1;
    };
    uint32_t stride_dim1;         // 13
    uint32_t stride_dim2;         // 25
    uint32_t base_addr_srcmem;    // 32
    uint32_t base_addr_destmem;   // 32
    uint32_t src_idx;             // 32
    uint32_t dest_idx;            // 32

} NOC_CONFIG;

extern  LS_CONFIG ls_config_reg;
extern  TM_CONFIG tm_config_reg;
extern  MP_CONFIG mp_config_reg;
extern  VP_CONFIG vp_config_reg;
extern  NOC_CONFIG noc_config_reg;



#endif /* OPERATORS_LIBRARY_uint32_RINSIC_HARDWARE_INST_DATA_H_ */
