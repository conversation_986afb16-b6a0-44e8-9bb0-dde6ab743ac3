/*
 * vnice_inst_batch.h
 *
 *  Created on: 2025年7月3日
 *      Author: 1572
 */

#ifndef OPERATORS_LIBRARY_INTRINSIC_VNICE_INST_BATCH_H_
#define OPERATORS_LIBRARY_INTRINSIC_VNICE_INST_BATCH_H_

#include <stdint.h>
#include <riscv_vector.h>
#include <nuclei_sdk_soc.h>



//BC Primitive Configuration Register
__STATIC_FORCEINLINE void v_bc_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0_out, uint32_t cfg_size_dim2_dim1, 
										uint32_t cfg_stride_dim1_out, uint32_t cfg_stride_dim2_out, 
										uint32_t *scalar_in_4, uint32_t base_addr_out){										
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_WIDTH_TYPE),               "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_OUT),  "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM2_DIM1),           "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_OUT),          "r"(cfg_stride_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_OUT),          "r"(cfg_stride_dim2_out));

    vuint32m1_t scalar_in_vec;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK);  
    scalar_in_vec = __riscv_vle32_v_u32m1(scalar_in_4, vl);

    asm volatile(
			"vsetivli zero,4,e32,m1,ta,ma\n"
			".insn r 0x2b, 3, %0, x0, %1, %2" 
			: :"i"(V_BC_DRV), "r"(base_addr_out), "vr"(scalar_in_vec));
}

//VS_V Primitive Configuration Register
__STATIC_FORCEINLINE void v_vs_v_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_in1, 
										uint32_t cfg_size_dim0b_rem_dim0_out, uint32_t cfg_special_case,
										uint32_t base_addr_in1, uint32_t *scalar_in_4, uint32_t base_addr_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN1), "r"(cfg_size_dim0b_rem_dim0_in1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SPECIAL_CASE),            "r"(cfg_special_case));

    vuint32m1_t scalar_in_vec;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK);  
    scalar_in_vec = __riscv_vle32_v_u32m1(scalar_in_4, vl);

    asm volatile(
			"vsetivli zero,4,e32,m1,ta,ma\n"
			".insn r 0x2b, 3, %0, x0, %1, %2" 
			: : "i"(V_VS_V_PRE), "r"(base_addr_in1), "vr"(scalar_in_vec));
    asm volatile(".insn r 0x0b, 2, %0, x0, %1, x0" : : "i"(VS_V_DRV),   "r"(base_addr_out)); 
}

//V_S Primitive Configuration Register
__STATIC_FORCEINLINE void v_v_s_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_in1,
										uint32_t cfg_special_case, uint32_t base_addr_in, uint32_t *scalar_out_4){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN1), "r"(cfg_size_dim0b_rem_dim0_in1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SPECIAL_CASE),            "r"(cfg_special_case));

    vuint32m1_t scalar_out_vec;
    size_t vl = __riscv_vsetvl_e32m1(MAX_MASK);

    asm volatile(
		"vsetivli zero,4,e32,m1,ta,ma\n"	
		".insn r 0x2b, 6, %1, %0, %2, x0" 
		: "=vr"(scalar_out_vec) : "i"(V_V_S_DRV), "r"(base_addr_in)); 
    __riscv_vse32_v_u32m1(scalar_out_4, scalar_out_vec, vl);
}


//NOC Primitive Configuration Register
// __STATIC_FORCEINLINE int v_noc_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0, uint32_t cfg_size_dim2_dim1,
// 										uint32_t cfg_stride_dim1, uint32_t cfg_stride_dim2, 
// 										uint32_t base_addr_srcmem, vuint32m1_t dest_idx, 
// 										uint32_t base_addr_destmem, vuint32m1_t src_idx){

// 	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_WIDTH_TYPE),          "r"(cfg_width_type));
// 	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
// 	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_SIZE_DIM2_DIM1),      "r"(cfg_size_dim2_dim1));
// 	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_STRIDE_DIM1),         "r"(cfg_stride_dim1));
// 	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_STRIDE_DIM2),         "r"(cfg_stride_dim2));

//     asm volatile(".insn r 0x2b, 3, %0, x0, %1, %2" : :"i"(V_NOC_SRC_DRV), "r"(base_addr_srcmem), "vr"(dest_idx));
//     asm volatile(".insn r 0x2b, 3, %0, x0, %1, %2" : :"i"(V_NOC_DST_DRV), "r"(base_addr_destmem), "vr"(src_idx));

// 	uint32_t status;
//     asm volatile(".insn r 0x0b, 4, %1, %0, x0, x0" : "=r"(status) : "i"(NOC_FENCE_DRV));
//     return status;
// }


#endif /* OPERATORS_LIBRARY_INTRINSIC_VNICE_INST_BATCH_H_ */
