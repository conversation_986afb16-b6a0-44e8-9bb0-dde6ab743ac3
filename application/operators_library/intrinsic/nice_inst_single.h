/*
 * inst_thin.h
 *
 *  Created on: 2025年6月19日
 *      Author: zqguo
 */

#ifndef OPERATORS_LIBRARY_INTRINSIC_NICE_INST_SINGLE_H_
#define OPERATORS_LIBRARY_INTRINSIC_NICE_INST_SINGLE_H_

#include <nuclei_sdk_soc.h>
#include <hardware_inst_data.h>
#include <stdint.h>


///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////
//NICE
//1     Control Primitive
//1.1   NPU Mask/Group Primitive
__STATIC_FORCEINLINE void npu_group_mask_drv(int npu_group, int npu_mask){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_GROUP_MASK_DRV), "r"(npu_group), "r"(npu_mask));
}
//////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////
//1.2   NPU Core CFG Primitive
//1.2.1 Tensor Load/Transpose-Load Configuration Register
// cfg_width_type[4:0]
//      cfg_wd[4:2]     : 配置传输的每个数据宽度
//      cfg_type[1:0]   : 配置传输的每个数据类型
__STATIC_FORCEINLINE void tld_cfg_width_type(uint32_t cfg_width_type){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_WIDTH_TYPE), "r"(cfg_width_type));
}

// cfg_size_dim0b_rem_dim0[16:0]
//      cfg_size_dim0b[16:6]: 配置传输的张量Dim0b大小
//      cfg_rem_dim0[5:0]   : 配置传输的张量Dim0余数大小
__STATIC_FORCEINLINE void tld_cfg_size_dim0b_rem_dim0(uint32_t cfg_size_dim0b_rem_dim0){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
}

// cfg_size_dim2_dim1[25:0]
//      cfg_size_dim2[25:13] :   配置传输的张量Dim2大小
//      cfg_size_dim1[12:0]  :   配置传输的张量Dim1大小
__STATIC_FORCEINLINE void tld_cfg_size_dim2_dim1(uint32_t cfg_size_dim2_dim1){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_SIZE_DIM2_DIM1), "r"(cfg_size_dim2_dim1));
}

// cfg_stride_dim1_gmem[12:0]
//      cfg_stride_dim1_gmem[12:0]:   配置从存储在global memory的dim1步长
__STATIC_FORCEINLINE void tld_cfg_stride_dim1_gmem(uint32_t cfg_stride_dim1_gmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM1_GMEM), "r"(cfg_stride_dim1_gmem));
}

// cfg_stride_dim2_gmem[24:0]
//      cfg_stride_dim2_gmem[24:0]:   配置从存储在global memory的dim2步长
__STATIC_FORCEINLINE void tld_cfg_stride_dim2_gmem(uint32_t cfg_stride_dim2_gmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM2_GMEM), "r"(cfg_stride_dim2_gmem));
}

// cfg_stride_dim1_lmem[12:0]
//      cfg_stride_dim1_lmem[12:0]:   配置从存储在local memory的dim1步长
__STATIC_FORCEINLINE void tld_cfg_stride_dim1_lmem(uint32_t cfg_stride_dim1_lmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM1_LMEM), "r"(cfg_stride_dim1_lmem));
}

// cfg_stride_dim2_lmem[24:0]
//      cfg_stride_dim2_lmem[24:0]:   配置从存储在local memory的dim2步长
__STATIC_FORCEINLINE void tld_cfg_stride_dim2_lmem(uint32_t cfg_stride_dim2_lmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM2_LMEM), "r"(cfg_stride_dim2_lmem));
}

///////////////////////////////////////////////////////////////   
//1.2.2 Tensor Store Configuration Register
// cfg_wd_type[4:0]
//      cfg_wd[4:2]     : 配置传输的每个数据宽度
//      cfg_type[1:0]   : 配置传输的每个数据类型
__STATIC_FORCEINLINE void tst_cfg_width_type(uint32_t cfg_width_type){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_WIDTH_TYPE), "r"(cfg_width_type));
}

// cfg_size_dim0b_rem_dim0[16:0]
//      cfg_size_dim0b[16:6]: 配置传输的张量Dim0b大小
//      cfg_rem_dim0[5:0]   : 配置传输的张量Dim0余数大小
__STATIC_FORCEINLINE void tst_cfg_size_dim0b_rem_dim0(uint32_t cfg_size_dim0b_rem_dim0){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
}

// cfg_size_dim2_dim1[25:0]
//      cfg_size_dim2[25:13]:   配置传输的张量Dim2大小
//      cfg_size_dim1[12:0] :   配置传输的张量Dim1大小
__STATIC_FORCEINLINE void tst_cfg_size_dim2_dim1(uint32_t cfg_size_dim2_dim1){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_SIZE_DIM2_DIM1), "r"(cfg_size_dim2_dim1));
}


// cfg_stride_dim1_gmem[12:0]
//      cfg_stride_dim1_gmem[12:0]:   配置从存储在global memory的dim1步长
__STATIC_FORCEINLINE void tst_cfg_stride_dim1_gmem(uint32_t cfg_stride_dim1_gmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM1_GMEM), "r"(cfg_stride_dim1_gmem));
}


// cfg_stride_dim2_gmem[24:0]
//      cfg_stride_dim2_gmem[24:0]:   配置从存储在global memory的dim2步长
__STATIC_FORCEINLINE void tst_cfg_stride_dim2_gmem(uint32_t cfg_stride_dim2_gmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM2_GMEM), "r"(cfg_stride_dim2_gmem));
}


// cfg_stride_dim1_lmem[12:0]
//      cfg_stride_dim1_lmem[12:0]:   配置从存储在local memory的dim1步长
__STATIC_FORCEINLINE void tst_cfg_stride_dim1_lmem(uint32_t cfg_stride_dim1_lmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM1_LMEM), "r"(cfg_stride_dim1_lmem));
}


// cfg_stride_dim2_lmem[24:0]
//      cfg_stride_dim2_lmem[24:0]:   配置从存储在local memory的dim2步长
__STATIC_FORCEINLINE void tst_cfg_stride_dim2_lmem(uint32_t cfg_stride_dim2_lmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM2_LMEM), "r"(cfg_stride_dim2_lmem));
}

///////////////////////////////////////////////////////////////////
//1.2.3 Tensor Movement Configuration Register
// cfg_wd_type[4:0] 
//      cfg_wd[4:2]:   配置传输的每个数据宽度
//      cfg_type[1:0]: 配置传输的每个数据类型
__STATIC_FORCEINLINE void tm_cfg_width_type(uint32_t cfg_width_type){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_WIDTH_TYPE), "r"(cfg_width_type));
}

// cfg_size_dim0b_rem_dim0_in[16:0]
//      cfg_size_dim0b[16:6]: 配置传输的输入张量dim0余数
//      cfg_rem_dim0[5:0]:    配置传输的输入张量dim0b大小
__STATIC_FORCEINLINE void tm_cfg_size_dim0b_rem_dim0_in(uint32_t cfg_size_dim0b_rem_dim0_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_IN), "r"(cfg_size_dim0b_rem_dim0_in));
}

// cfg_size_dim0b_rem_dim0_out[16:0]
//      cfg_size_dim0b[16:6]: 配置传输的输出张量dim0余数
//      cfg_rem_dim0[5:0]:    配置传输的输出张量dim0b大小
__STATIC_FORCEINLINE void tm_cfg_size_dim0b_rem_dim0_out(uint32_t cfg_size_dim0b_rem_dim0_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
}

// cfg_size_dim2_dim1[4:0] 
//      cfg_size_dim2[25:13]:   配置传输的张量dim2大小
//      cfg_size_dim1[12:0] :   配置传输的张量dim1大小
__STATIC_FORCEINLINE void tm_cfg_size_dim2_dim1(uint32_t cfg_size_dim2_dim1){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM2_DIM1), "r"(cfg_size_dim2_dim1));
}

// cfg_stride_dim1_out[12:0]
//      cfg_stride_dim1_out[12:0]:   配置传输的输出张量dim1步长
__STATIC_FORCEINLINE void tm_cfg_stride_dim1_out(uint32_t cfg_stride_dim1_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_OUT), "r"(cfg_stride_dim1_out));
}

// cfg_stride_dim2_out[24:0]
//      cfg_stride_dim2_out[24:0]:   配置传输的输出张量dim2步长
__STATIC_FORCEINLINE void tm_cfg_stride_dim2_out(uint32_t cfg_stride_dim2_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_OUT), "r"(cfg_stride_dim2_out));
}

// cfg_stride_dim1_in[12:0]
//      cfg_stride_dim1_in[12:0]:   配置传输的输入张量dim1步长
__STATIC_FORCEINLINE void tm_cfg_stride_dim1_in(uint32_t cfg_stride_dim1_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_IN), "r"(cfg_stride_dim1_in));
}

// cfg_stride_dim2_in[24:0]
//      cfg_stride_dim2_in[24:0]:   配置传输的输入张量dim0b步长
__STATIC_FORCEINLINE void tm_cfg_stride_dim2_in(uint32_t cfg_stride_dim2_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_IN), "r"(cfg_stride_dim2_in));
}

//////////////////////////////////////////////////////////
//1.2.4 Tensor Processing （CIM） Configuration Register
// cfg_op_width_type[21:0]:
//      cfg_shift       [26:22]     :配置右移位
//      cfg_act         [21]        :配置激活函数，’1‘执行relu功能
//      cfg_accu        [20]        :配置累计和，’1‘存在需要的部分历史累计和
//      cfg_wd_wt       [19:17]     :配置待参考的数据宽度
//      cfg_wd_in       [16:14]     :配置待参考的数据宽度
//      cfg_wd_orig     [13:11]     :配置待参考的数据宽度
//      cfg_wd_out      [10:8]      :配置待参考的数据宽度
//      cfg_type_wt     [7:6]	    :配置权重矩阵的数据类型
//      cfg_type_in     [5:4]       :配置输入张量的数据类型
//      cfg_type_orig   [3:2]       :配置输出张量的数据类型
//      cfg_type_out    [1:0]       :配置输出张量的数据类型
__STATIC_FORCEINLINE void mp_cfg_op_width_type(uint32_t cfg_op_width_type){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_OP_WIDTH_TYPE), "r"(cfg_op_width_type));
}

// cfg_size_dim0b_rem_dim0_out[16:0]:
//      cfg_size_dim0b_out  [16:6]  :配置输出张量的dim0b大小
//      cfg_rem_dim0_out    [5:0]   :配置输入张量的dim0余数
__STATIC_FORCEINLINE void mp_cfg_size_dim0b_rem_dim0_out(uint32_t cfg_size_dim0b_rem_dim0_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
}

// cfg_size_dim2_dim1_out[25:0]:
//      cfg_size_dim2_out   [25:13] :配置输出张量的dim2大小
//      cfg_size_dim1_out   [12:0]  :配置输出张量的dim1大小
__STATIC_FORCEINLINE void mp_cfg_size_dim2_dim1_out(uint32_t cfg_size_dim2_dim1_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM2_DIM1_OUT), "r"(cfg_size_dim2_dim1_out));
}

// cfg_stride_dim1_out[12:0]
//      cfg_stride_dim1_out [12:0]  :配置输出张量的dim1步长
__STATIC_FORCEINLINE void mp_cfg_stride_dim1_out(uint32_t cfg_stride_dim1_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM1_OUT), "r"(cfg_stride_dim1_out));
}

// cfg_stride_dim2_out[24:0]
//      cfg_stride_dim2_out [24:0]  :配置输出张量dim2的步长
__STATIC_FORCEINLINE void mp_cfg_stride_dim2_out(uint32_t cfg_stride_dim2_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM2_OUT), "r"(cfg_stride_dim2_out));
}

// cfg_size_dim0b_rem_dim0_in[16:0]
//      cfg_size_dim0b_in   [16:6]  :配置输入张量dim0b的大小
//      cfg_rem_dim0_in     [5:0]   :配置输入张量dim0的余数
__STATIC_FORCEINLINE void mp_cfg_size_dim0b_rem_dim0_in(uint32_t cfg_size_dim0b_rem_dim0_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM0B_REM_DIM0_IN), "r"(cfg_size_dim0b_rem_dim0_in));
}

// cfg_size_dim2_dim1_in[25:0]
//      cfg_size_dim2_in    [25:23] :配置输入张量dim2的大小
//      cfg_size_dim1_in    [12:0]  :配置输入张量dim1的大小
__STATIC_FORCEINLINE void mp_cfg_size_dim2_dim1_in(uint32_t cfg_size_dim2_dim1_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM2_DIM1_IN), "r"(cfg_size_dim2_dim1_in));
}

// cfg_stride_dim1_in[12:0]
//      cfg_stride_dim1_in  [12:0]  :配置输入张量dim1的步长
__STATIC_FORCEINLINE void mp_cfg_stride_dim1_in(uint32_t cfg_stride_dim1_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM1_IN), "r"(cfg_stride_dim1_in));
}

// cfg_stride_dim2_in[24:0]
//      cfg_stride_dim2_in  [24:0]  :配置输入张量dim2的步长
__STATIC_FORCEINLINE void mp_cfg_stride_dim2_in(uint32_t cfg_stride_dim2_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM2_IN), "r"(cfg_stride_dim2_in));
}

///////////////////////////////////////////////////////////////////
//1.2.5 Vector Processing Configuration Register
// cfg_op_width_type:
//    cfg_op            [20:15],
//    cfg_wd_in2        [14:12],
//    cfg_wd_in1        [11:9],
//    cfg_wd_out        [8:6],
//    cfg_type_in2      [5:4],
//    cfg_type_in1      [3:2],
//    cfg_type_out      [1:0]
__STATIC_FORCEINLINE void vp_cfg_op_width_type(uint32_t cfg_op_width_type){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_OP_WIDTH_TYPE), "r"(cfg_op_width_type));
}

// cfg_size_dim0b_rem_dim0_in1
//    cfg_size_dim0b_ref[16:6],
//    cfg_rem_dim0_ref[5:0]
__STATIC_FORCEINLINE void vp_cfg_size_dim0b_rem_dim0_in1(uint32_t cfg_size_dim0b_rem_dim0_in1){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN1), "r"(cfg_size_dim0b_rem_dim0_in1));
}
// cfg_size_dim0b_rem_dim0_in2
//    cfg_size_dim0b_ref[16:6],
//    cfg_rem_dim0_ref[5:0]
__STATIC_FORCEINLINE void vp_cfg_size_dim0b_rem_dim0_in2(uint32_t cfg_size_dim0b_rem_dim0_in2){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN2), "r"(cfg_size_dim0b_rem_dim0_in2));
}
// cfg_size_dim0b_rem_dim0_out
//    cfg_size_dim0b_ref[16:6],
//    cfg_rem_dim0_ref[5:0]
__STATIC_FORCEINLINE void vp_cfg_size_dim0b_rem_dim0_out(uint32_t cfg_size_dim0b_rem_dim0_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
}
// cfg_special_case
//    cfg_safu[0],
//    cfg_dis0[1],
//    cfg_rm[4:2]
__STATIC_FORCEINLINE void vp_cfg_special_case(uint32_t cfg_special_case){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SPECIAL_CASE), "r"(cfg_special_case));
}

///////////////////////////////////////////////////////////////////
//1.2.6 NoC Configuration Register
// cfg_width_type[4:0] 
//      cfg_wd[4:2]:   配置传输的每个数据宽度
//      cfg_type[1:0]: 配置传输的每个数据类型
__STATIC_FORCEINLINE void noc_cfg_width_type(uint32_t cfg_width_type){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_WIDTH_TYPE), "r"(cfg_width_type));
}

// cfg_size_dim0b_rem_dim0[16:0]
//      cfg_size_dim0b[16:6]: 配置传输的张量dim0余数
//      cfg_rem_dim0[5:0]:    配置传输的张量dim0b大小
__STATIC_FORCEINLINE void noc_cfg_size_dim0b_rem_dim0(uint32_t cfg_size_dim0b_rem_dim0){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
}

// cfg_size_dim2_dim1[4:0] 
//      cfg_size_dim2[25:13]:   配置传输的张量dim2大小
//      cfg_size_dim1[12:0] :   配置传输的张量dim1大小
__STATIC_FORCEINLINE void noc_cfg_size_dim2_dim1(uint32_t cfg_size_dim2_dim1){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_SIZE_DIM2_DIM1), "r"(cfg_size_dim2_dim1));
}

// cfg_stride_dim1[12:0]
//      cfg_stride_dim1[12:0]:   配置传输的输出张量dim1步长
__STATIC_FORCEINLINE void noc_cfg_stride_dim1(uint32_t cfg_stride_dim1){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_STRIDE_DIM1), "r"(cfg_stride_dim1));
}

// cfg_stride_dim2[24:0]  
//      cfg_stride_dim2[24:0]:   配置传输的输出张量dim2步长
__STATIC_FORCEINLINE void noc_cfg_stride_dim2(uint32_t cfg_stride_dim2){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_STRIDE_DIM2), "r"(cfg_stride_dim2));
}

//////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////
//1.3 RISC-V Write Local Memory Primitive
//      base_addr_lmem:          配置要写入的Local Memory的起始地址
//      wr_data_lmem:            配置要写入的Local Memory的数据
__STATIC_FORCEINLINE void wr_lmem_drv(uint32_t base_addr_lmem, uint32_t wr_data_lmem){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(WR_LMEM_DRV), "r"(base_addr_lmem), "r"(wr_data_lmem));
}

//1.4 RISC-V Read Local Memory Primitive
//      base_addr_lmem:          配置要读取的Local Memory的起始地址
//      return:                  函数返回要读取的Local Memory的数据，数据类型默认INT32
__STATIC_FORCEINLINE uint32_t rd_lmem_drv(uint32_t base_addr_lmem){
    uint32_t rd_data_lmem;
    asm volatile(".insn r 0x0b, 6, %1, %0, %2, x0" : "=r"(rd_data_lmem) :"i"(RD_LMEM_DRV), "r"(base_addr_lmem));
    return rd_data_lmem;
}

//1.5 Switch CIMC Primitive 
// 		 byte_base_cimc: 		byte base address of cim cluster
// 		 cimc_addr_mode: 		cim cluster addressing mode
__STATIC_FORCEINLINE void switch_cimc_drv(uint32_t base_addr_lmem, uint32_t cimc_addr_mode){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(SWITCH_CIMC_DRV), "r"(base_addr_lmem), "r"(cimc_addr_mode));
}

//1.6 Sync Primitive   默认返回’1‘
__STATIC_FORCEINLINE uint32_t sync_drv(){
	uint32_t status;
    asm volatile(".insn r 0x0b, 4, %1, %0, x0, x0" : "=r"(status) : "i"(SYNC_DRV));
    return status;
}

////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
//2     NoC Primitive
//      base_addr_srcmem:          配置要读取的源NPU Core Local/Global Memory的起始地址
//      dest_idx:               配置要写入的目的NPU Core的索引
__STATIC_FORCEINLINE void noc_src_drv(uint32_t base_addr_srcmem, uint32_t dest_idx){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NOC_SRC_DRV), "r"(base_addr_srcmem), "r"(dest_idx));
}
//      base_addr_destmem:          配置要写入的目的Local/Global Memory的起始地址
//      src_idx:                配置要读取的源NPU Core的索引
__STATIC_FORCEINLINE void noc_dest_drv(uint32_t base_addr_destmem, uint32_t src_idx){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NOC_DST_DRV), "r"(base_addr_destmem), "r"(src_idx));
}
//      默认返回’1‘
__STATIC_FORCEINLINE uint32_t noc_fence_drv(){
    uint32_t status;
    asm volatile(".insn r 0x0b, 4, %1, %0, x0, x0" : "=r"(status) : "i"(NOC_FENCE_DRV));
    return status;
}

////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
//3 Tensor Load/Store Primitive
//3.1 Tensor Load Primitive
// base_addr_lmem:存储在Local Memory的张量地址
// base_addr_gmem:存储在Global Memory的张量地址
__STATIC_FORCEINLINE void tld_drv(uint32_t base_addr_gmem, uint32_t base_addr_lmem){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TLD_DRV), "r"(base_addr_gmem), "r"(base_addr_lmem));
}

//3.2 Tensor transpose-load Primitive
// base_addr_lmem:存储在Local Memory的张量地址
// base_addr_gmem:存储在Global Memory的张量地址
__STATIC_FORCEINLINE void ttld_drv(uint32_t base_addr_gmem, uint32_t base_addr_lmem){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TTLD_DRV), "r"(base_addr_gmem), "r"(base_addr_lmem));
}

//3.3 Tensor Index-Load Primitive（暂时不需要支持）
//3.4 Tensor Store Primitive
// base_addr_lmem:存储在Local Memory的张量地址
// base_addr_gmem:存储在Global Memory的张量地址
__STATIC_FORCEINLINE void tst_drv(uint32_t base_addr_lmem, uint32_t base_addr_gmem){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TST_DRV), "r"(base_addr_gmem), "r"(base_addr_lmem));
}
//3.5 Tensor Index-Store Primtive（暂时不需要支持）

////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
//4 Tensor Movement Primitive   
//4.1 BC Primitive
// scalar_in:输入标量的数值
// base_addr_lmem:存储在local Memory的输出张量地址
__STATIC_FORCEINLINE void bc_drv(uint32_t base_addr_out, uint32_t scalar_in){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(BC_DRV), "r"(base_addr_out), "r"(scalar_in));
}
//4.2 Tensor Move Primitive
// base_addr_in:存储在local Memory的输入张量地址
// base_addr_out:存储在local Memory的输出张量地址
__STATIC_FORCEINLINE void mov_drv(uint32_t base_addr_in, uint32_t base_addr_out){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(MOV_DRV), "r"(base_addr_in), "r"(base_addr_out));
}
//4.3 Tensor Transpose Primitive
// base_addr_in:存储在local Memory的输入矩阵地址
// base_addr_out:存储在local Memory的输出矩阵地址
__STATIC_FORCEINLINE void trans_drv(uint32_t  base_addr_in, uint32_t  base_addr_out){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TRAN_DRV), "r"(base_addr_in), "r"(base_addr_out));
}
//4.4 setCIMExp Primitive
// base_addr_spad_in:存储在local Memory Scratchpad的输入矩阵地址
// base_addr_cim_out:存储在local Memory CIME的输出矩阵地址
__STATIC_FORCEINLINE void setcimexp_drv(uint32_t  base_addr_spad_in, uint32_t  base_addr_cim_out){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(SETCIMEXP_DRV), "r"(base_addr_spad_in), "r"(base_addr_cim_out));
}
//4.5 getCIMExp Primitive
// base_addr_spad_out:存储在local Memory Scratchpad的输出矩阵地址
// base_addr_cim_in  :存储在local Memory CIME的输入矩阵地址
__STATIC_FORCEINLINE void getcimexp_drv(uint32_t  base_addr_spad_out, uint32_t  base_addr_cim_in){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GETCIMEXP_DRV), "r"(base_addr_spad_out), "r"(base_addr_cim_in));
}

////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////
//5 Tensor Processing（CIM）Primitive
//5.1 CONV Primitive（暂时不需要支持）
//5.2 GEMV Primitive
// base_addr_wt ：存储在CIM的矩阵地址
// base_addr_in ：存储在local Memory的输入张量地址
// base_addr_out：存储在local Memory的输出张量地址
// base_addr_ori：存储在local Memory的原始张量地址
__STATIC_FORCEINLINE void gemv_pre(uint32_t base_addr_wt, uint32_t base_addr_in){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMV_PRE), "r"(base_addr_wt), "r"(base_addr_in));
}
__STATIC_FORCEINLINE void gemv_drv(uint32_t base_addr_out, uint32_t base_addr_orig){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMV_DRV), "r"(base_addr_out), "r"(base_addr_orig));
}
//5.3 GEMM Primitive
// base_addr_wt ：存储在CIM的矩阵地址
// base_addr_in ：存储在local Memory的输入张量地址
// base_addr_out：存储在local Memory的输出张量地址
// base_addr_orig：存储在local Memory的原始张量地址
__STATIC_FORCEINLINE void gemm_pre(uint32_t base_addr_wt, uint32_t base_addr_in){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMM_PRE), "r"(base_addr_wt), "r"(base_addr_in));
}
__STATIC_FORCEINLINE void gemm_drv(uint32_t  base_addr_out, uint32_t base_addr_orig){
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMM_DRV), "r"(base_addr_out), "r"(base_addr_orig));
}

////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////
//6 Vector Processing Primitive
//6.1 VV_V Primitive
//base_addr_in1：存储在local Memory的输入向量1地址
//base_addr_in2：存储在local Memory的输入向量2地址
//base_addr_out：存储在local Memory的输出向量地址
__STATIC_FORCEINLINE void vv_v_pre(uint32_t base_addr_in1, uint32_t base_addr_in2) {
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : : "i"(VV_V_PRE), "r"(base_addr_in1), "r"(base_addr_in2)); 
}
__STATIC_FORCEINLINE void vv_v_drv(uint32_t base_addr_out) {
    asm volatile(".insn r 0x0b, 2, %0, x0, %1, x0" : : "i"(VV_V_DRV), "r"(base_addr_out)); 
}

//6.2 VV_V Primitive
//base_addr_in ：存储在local Memory的输入向量地址
//scalar_in    : RISC-V寄存器中传入的标量值
//base_addr_out：存储在local Memory的输出向量地址
__STATIC_FORCEINLINE void vs_v_pre(uint32_t base_addr_in, uint32_t scalar_in) {
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : : "i"(VS_V_PRE), "r"(base_addr_in), "r"(scalar_in)); 
}
__STATIC_FORCEINLINE void vs_v_drv(uint32_t base_addr_out) {
    asm volatile(".insn r 0x0b, 2, %0, x0, %1, x0" : : "i"(VS_V_DRV), "r"(base_addr_out)); 
}

//6.3 V_S Primitive
//base_addr_in ：存储在local Memory的输入向量地址
//scalar_out   : NPU返回至RISC-V寄存器中的标量值
__STATIC_FORCEINLINE uint32_t v_s_drv(uint32_t base_addr_in) {
    uint32_t scalar_out = 0;
    asm volatile(".insn r 0x0b, 6, %1, %0, %2, x0" : "=r"(scalar_out) : "i"(V_S_DRV), "r"(base_addr_in)); 
    return scalar_out;
}

//6.4 V_V Primitive
//base_addr_in ：存储在local Memory的输入向量地址
//base_addr_out：存储在local Memory的输出向量地址
__STATIC_FORCEINLINE void v_v_drv(uint32_t base_addr_in, uint32_t base_addr_out) {
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : : "i"(V_V_DRV), "r"(base_addr_in), "r"(base_addr_out));  
}

#endif /* OPERATORS_LIBRARY_INTRINSIC_NICE_INST_SINGLE_H_ */
