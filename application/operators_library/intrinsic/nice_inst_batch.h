/*
 * nice_inst_batch.h
 *
 *  Created on: 2025年6月19日
 *      Author: zqguo
 */

#ifndef OPERATORS_LIBRARY_INTRINSIC_NICE_INST_BATCH_H_
#define OPERATORS_LIBRARY_INTRINSIC_NICE_INST_BATCH_H_

#include "nuclei_sdk_soc.h"
#include "hardware_inst_data.h"
#include <stdint.h>


//Tensor Load Primitive Configuration Register
__STATIC_FORCEINLINE void tld_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0, uint32_t cfg_size_dim2_dim1,
										uint32_t cfg_stride_dim1_gmem, uint32_t cfg_stride_dim2_gmem,
										uint32_t cfg_stride_dim1_lmem, uint32_t cfg_stride_dim2_lmem,
										uint32_t base_addr_gmem, uint32_t base_addr_lmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_WIDTH_TYPE ),         "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_SIZE_DIM2_DIM1),      "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM1_GMEM),    "r"(cfg_stride_dim1_gmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM2_GMEM),    "r"(cfg_stride_dim2_gmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM1_LMEM),    "r"(cfg_stride_dim1_lmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM2_LMEM),    "r"(cfg_stride_dim2_lmem));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TLD_DRV),     "r"(base_addr_gmem),              "r"(base_addr_lmem));
}

//Transpose-Load Primitive Configuration Register
__STATIC_FORCEINLINE void ttld_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0, uint32_t cfg_size_dim2_dim1,
										uint32_t cfg_stride_dim1_gmem, uint32_t cfg_stride_dim2_gmem,
										uint32_t cfg_stride_dim1_lmem, uint32_t cfg_stride_dim2_lmem,
										uint32_t base_addr_gmem, uint32_t base_addr_lmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_WIDTH_TYPE),          "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_SIZE_DIM2_DIM1),      "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM1_GMEM),    "r"(cfg_stride_dim1_gmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM2_GMEM),    "r"(cfg_stride_dim2_gmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM1_LMEM),    "r"(cfg_stride_dim1_lmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TLD_CFG_STRIDE_DIM2_LMEM),    "r"(cfg_stride_dim2_lmem));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TTLD_DRV),    "r"(base_addr_gmem),              "r"(base_addr_lmem));
}

//Tensor Store Primitive Configuration Register
__STATIC_FORCEINLINE void tst_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0, uint32_t cfg_size_dim2_dim1,
										uint32_t cfg_stride_dim1_gmem, uint32_t cfg_stride_dim2_gmem,
										uint32_t cfg_stride_dim1_lmem, uint32_t cfg_stride_dim2_lmem,
										uint32_t base_addr_gmem, uint32_t base_addr_lmem){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_WIDTH_TYPE),          "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_SIZE_DIM2_DIM1),      "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM1_GMEM),    "r"(cfg_stride_dim1_gmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM2_GMEM),    "r"(cfg_stride_dim2_gmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM1_LMEM),    "r"(cfg_stride_dim1_lmem));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TST_CFG_STRIDE_DIM2_LMEM),    "r"(cfg_stride_dim2_lmem));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TST_DRV),     "r"(base_addr_gmem),              "r"(base_addr_lmem));
}

//BC Primitive Configuration Register
__STATIC_FORCEINLINE void bc_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0_out, uint32_t cfg_size_dim2_dim1, 
										uint32_t cfg_stride_dim1_out, uint32_t cfg_stride_dim2_out, 
										uint32_t scalar_in, uint32_t base_addr_out){										
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_WIDTH_TYPE),               "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_OUT),  "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM2_DIM1),           "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_OUT),          "r"(cfg_stride_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_OUT),          "r"(cfg_stride_dim2_out));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(BC_DRV),      "r"(base_addr_out),                   "r"(scalar_in));
}


//MOV Primitive Configuration Register
__STATIC_FORCEINLINE void mov_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0_in, uint32_t cfg_size_dim0b_rem_dim0_out,
										uint32_t cfg_stride_dim1_out, uint32_t cfg_stride_dim2_out, 
										uint32_t cfg_stride_dim1_in, uint32_t cfg_stride_dim2_in, 
										uint32_t cfg_size_dim2_dim1, uint32_t base_addr_in, uint32_t base_addr_out){										
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_WIDTH_TYPE),              "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_IN),  "r"(cfg_size_dim0b_rem_dim0_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM2_DIM1),          "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_OUT),         "r"(cfg_stride_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_OUT),         "r"(cfg_stride_dim2_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_IN),          "r"(cfg_stride_dim1_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_IN),          "r"(cfg_stride_dim2_in));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(MOV_DRV),     "r"(base_addr_in),                   "r"(base_addr_out));
}

//Trans Primitive Configuration Register
__STATIC_FORCEINLINE void trans_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0_in, uint32_t cfg_size_dim0b_rem_dim0_out,
										uint32_t cfg_stride_dim1_out, uint32_t cfg_stride_dim2_out, 
										uint32_t cfg_stride_dim1_in, uint32_t cfg_stride_dim2_in, 
										uint32_t cfg_size_dim2_dim1, uint32_t base_addr_in, uint32_t base_addr_out){										
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_WIDTH_TYPE),          	 "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_IN),  "r"(cfg_size_dim0b_rem_dim0_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM2_DIM1),      	 "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_OUT),    	 "r"(cfg_stride_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_OUT),    	 "r"(cfg_stride_dim2_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_IN),     	 "r"(cfg_stride_dim1_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM2_IN),     	 "r"(cfg_stride_dim2_in));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(TRAN_DRV),    "r"(base_addr_in), 					 "r"(base_addr_out));
}

//setCIMExp Primitive Configuration Register
__STATIC_FORCEINLINE void setcimexp_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0_in, uint32_t cfg_size_dim0b_rem_dim0_out,
										uint32_t cfg_stride_dim1_out, uint32_t cfg_stride_dim1_in, uint32_t cfg_size_dim2_dim1,
										uint32_t base_addr_spad_in, uint32_t base_addr_cim_out){										
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_WIDTH_TYPE),         	 "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_IN),  "r"(cfg_size_dim0b_rem_dim0_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM2_DIM1),      	 "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_OUT),    	 "r"(cfg_stride_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_IN),     	 "r"(cfg_stride_dim1_in));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(SETCIMEXP_DRV), "r"(base_addr_spad_in),       	 "r"(base_addr_cim_out));
}

//getCIMExp Primitive Configuration Register
__STATIC_FORCEINLINE void getcimexp_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0_in, uint32_t cfg_size_dim0b_rem_dim0_out,  
										uint32_t cfg_stride_dim1_out, uint32_t cfg_stride_dim1_in, uint32_t cfg_size_dim2_dim1,
										uint32_t base_addr_spad_out, uint32_t base_addr_cim_in){										
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_WIDTH_TYPE),         	 "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_IN),  "r"(cfg_size_dim0b_rem_dim0_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_SIZE_DIM2_DIM1),      	 "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_OUT),    	 "r"(cfg_stride_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(TM_CFG_STRIDE_DIM1_IN),     	 "r"(cfg_stride_dim1_in));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GETCIMEXP_DRV), "r"(base_addr_spad_out),      	 "r"(base_addr_cim_in));
}

//GEMM Primitive Configuration Register
__STATIC_FORCEINLINE void gemm_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_out, uint32_t cfg_size_dim2_dim1_out,
										uint32_t cfg_stride_dim1_out, uint32_t cfg_stride_dim2_out, uint32_t cfg_size_dim0b_rem_dim0_in, 
										uint32_t cfg_size_dim2_dim1_in, uint32_t cfg_stride_dim1_in, uint32_t cfg_stride_dim2_in,
										uint32_t base_addr_wt, uint32_t base_addr_in,
										uint32_t  base_addr_out, uint32_t base_addr_orig){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM2_DIM1_OUT),      "r"(cfg_size_dim2_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM1_OUT),         "r"(cfg_stride_dim1_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM2_OUT),         "r"(cfg_stride_dim2_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM0B_REM_DIM0_IN),  "r"(cfg_size_dim0b_rem_dim0_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM2_DIM1_IN),       "r"(cfg_size_dim2_dim1_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM1_IN),          "r"(cfg_stride_dim1_in));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_STRIDE_DIM2_IN),          "r"(cfg_stride_dim2_in));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMM_PRE),    "r"(base_addr_wt),                   "r"(base_addr_in));
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMM_DRV),    "r"(base_addr_out),                  "r"(base_addr_orig));
}

//GEMV Primitive Configuration Register
__STATIC_FORCEINLINE void gemv_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_out, uint32_t cfg_size_dim0b_rem_dim0_in, 
										uint32_t base_addr_wt, uint32_t base_addr_in,
										uint32_t  base_addr_out, uint32_t base_addr_orig){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(MP_CFG_SIZE_DIM0B_REM_DIM0_IN),  "r"(cfg_size_dim0b_rem_dim0_in));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMV_PRE),    "r"(base_addr_wt),                   "r"(base_addr_in));
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(GEMV_DRV),    "r"(base_addr_out),                  "r"(base_addr_orig));
}

//VV_V Primitive Configuration Register
__STATIC_FORCEINLINE void vv_v_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_in1, uint32_t cfg_size_dim0b_rem_dim0_in2, 
										uint32_t cfg_size_dim0b_rem_dim0_out, uint32_t cfg_special_case,
										uint32_t base_addr_in1, uint32_t base_addr_in2, uint32_t base_addr_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN1), "r"(cfg_size_dim0b_rem_dim0_in1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN2), "r"(cfg_size_dim0b_rem_dim0_in2));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SPECIAL_CASE),            "r"(cfg_special_case));

	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : : "i"(VV_V_PRE),   "r"(base_addr_in1), 			     "r"(base_addr_in2)); 
    asm volatile(".insn r 0x0b, 2, %0, x0, %1, x0" : : "i"(VV_V_DRV),   "r"(base_addr_out)); 
}

//VS_V Primitive Configuration Register
__STATIC_FORCEINLINE void vs_v_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_in1, 
										uint32_t cfg_size_dim0b_rem_dim0_out, uint32_t cfg_special_case,
										uint32_t base_addr_in1, uint32_t scalar_in2, uint32_t base_addr_out){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN1), "r"(cfg_size_dim0b_rem_dim0_in1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SPECIAL_CASE),            "r"(cfg_special_case));

	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : : "i"(VS_V_PRE),   "r"(base_addr_in1), 			     "r"(scalar_in2)); 
    asm volatile(".insn r 0x0b, 2, %0, x0, %1, x0" : : "i"(VS_V_DRV),   "r"(base_addr_out)); 
}

//V_S Primitive Configuration Register
__STATIC_FORCEINLINE uint32_t v_s_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_in1,
										uint32_t cfg_special_case, uint32_t base_addr_in){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN1), "r"(cfg_size_dim0b_rem_dim0_in1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SPECIAL_CASE),            "r"(cfg_special_case));

    uint32_t scalar_out = 0;
    asm volatile(".insn r 0x0b, 6, %1, %0, %2, x0" : "=r"(scalar_out) : "i"(V_S_DRV), "r"(base_addr_in)); 
    return scalar_out;
}


//V_V Primitive Configuration Register
__STATIC_FORCEINLINE void v_v_cfg_pre_drv(uint32_t cfg_op_width_type, uint32_t cfg_size_dim0b_rem_dim0_in1, uint32_t cfg_size_dim0b_rem_dim0_out, 
										uint32_t base_addr_in, uint32_t base_addr_out, uint32_t cfg_special_case){
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_OP_WIDTH_TYPE),           "r"(cfg_op_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_OUT), "r"(cfg_size_dim0b_rem_dim0_out));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SIZE_DIM0B_REM_DIM0_IN1), "r"(cfg_size_dim0b_rem_dim0_in1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(VP_CFG_SPECIAL_CASE),            "r"(cfg_special_case));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : : "i"(V_V_DRV),    "r"(base_addr_in),                   "r"(base_addr_out));  
}


//NOC Primitive Configuration Register
__STATIC_FORCEINLINE uint32_t noc_cfg_pre_drv(uint32_t cfg_width_type, uint32_t cfg_size_dim0b_rem_dim0, uint32_t cfg_size_dim2_dim1,
										uint32_t cfg_stride_dim1, uint32_t cfg_stride_dim2, 
										uint32_t base_addr_srcmem, uint32_t dest_idx, 
										uint32_t base_addr_destmem, uint32_t src_idx){  
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_WIDTH_TYPE),          "r"(cfg_width_type));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_SIZE_DIM0B_REM_DIM0), "r"(cfg_size_dim0b_rem_dim0));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_SIZE_DIM2_DIM1),      "r"(cfg_size_dim2_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_STRIDE_DIM1),         "r"(cfg_stride_dim1));
	asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NPU_CFG_DRV), "r"(NOC_CFG_STRIDE_DIM2),         "r"(cfg_stride_dim2));

    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NOC_SRC_DRV), "r"(base_addr_srcmem), 			  "r"(dest_idx));
    asm volatile(".insn r 0x0b, 3, %0, x0, %1, %2" : :"i"(NOC_DST_DRV), "r"(base_addr_destmem), 		  "r"(src_idx));

	uint32_t status;
    asm volatile(".insn r 0x0b, 4, %1, %0, x0, x0" : "=r"(status) : "i"(NOC_FENCE_DRV));
    return status;
}





#endif /* OPERATORS_LIBRARY_INTRINSIC_NICE_INST_BATCH_H_ */
