/*
 * primitive.h
 *
 *  Created on: 2025年6月23日
 *      Author: zqguo
 */

#ifndef OPERATORS_LIBRARY_PRIMITIVE_INCLUDE_PRIMITIVE_H_
#define OPERATORS_LIBRARY_PRIMITIVE_INCLUDE_PRIMITIVE_H_

#include <riscv_vector.h>
#include "software_port_data.h"
#include "nice_inst_batch.h"
#include "nice_inst_single.h"
#include "vnice_inst_batch.h"
#include "vnice_inst_single.h"
#include "hardware_inst_data.h"







//Software And Hardware Conversion
uint32_t dim0b_stride1_conversion(Tensor *tensor_in);
uint32_t rem_dim0_conversion(Tensor *tensor_in);
uint32_t bytes_stride2(Tensor *tensor_in);
uint32_t page_addr(CIM_Option *cim_option);




//Primitive Batch Function
////////NICE/////////////////////////////////////////////
void wr_lmem_primitive(uint32_t base_addr_lmem, uint32_t wr_data_lmem, int *npu_mask);
uint32_t rd_lmem_primitive(uint32_t base_addr_lmem, int group_id, int npu_mask);
int sync_primitive();
void switch_cimc_primitive(uint32_t base_addr_lmem, uint32_t cimc_addr_mode, int *npu_mask);
void tld_primitive(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask);
void ttld_primitive(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask);
void tst_primitive(Tensor *tensor_lmem, Tensor *tensor_gmem, int *npu_mask);
void bc_primitive(uint32_t scalar_in, Tensor *tensor_out, int *npu_mask);
void mov_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void trans_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void setcimexp_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void getcimexp_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void gemm_primitive(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask);
void gemv_primitive(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask);
void vv_v_primitive(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void vs_v_primitive(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void v_s_primitive(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask, uint32_t *v_s_return);
void v_v_primitive(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
// void noc_primitive(Tensor *tensor_in1, NOC_Option *noc_option, int *npu_mask, int *noc_return);
/////////VNICE/////////////////////////////////////////////
void v_rd_lmem_primitive(uint32_t base_addr_lmem, uint32_t *rd_lmem_out_16, int *npu_mask);
void v_wr_lmem_primitive(uint32_t base_addr_lmem, uint32_t *wr_data_in_16, int *npu_mask);
void v_bc_primitive(uint32_t *scalar_in_16, Tensor *tensor_out, int *npu_mask);
void v_vs_v_primitive(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask);
void v_v_s_primitive(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask, uint32_t *scalar_out_16);
// void v_noc_primitive(Tensor *tensor_in1, NOC_Option *noc_option, int *npu_mask, uint32_t *dest_idx_16, uint32_t *src_idx_16, uint32_t *noc_return_16);

//Primitive Single Function
/////////////////////////////////////NICE//////////////////////////////////////////////
void tld_primitive_cfg(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask);
void tld_primitive_drv(uint32_t base_addr_gmem, uint32_t base_addr_lmem, int *npu_mask);
void ttld_primitive_cfg(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask);
void ttld_primitive_drv(uint32_t base_addr_gmem, uint32_t base_addr_lmem, int *npu_mask);
void tst_primitive_cfg(Tensor *tensor_lmem, Tensor *tensor_gmem, int *npu_mask);
void tst_primitive_drv(uint32_t base_addr_lmem, uint32_t base_addr_gmem, int *npu_mask);
void gemm_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask);
void gemm_primitive_pre(uint32_t page_index, uint32_t base_addr_in, int *npu_mask);
void gemm_primitive_drv(uint32_t base_addr_out, uint32_t base_addr_orig, int *npu_mask);
void gemv_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask);
void gemv_primitive_pre(uint32_t page_index, uint32_t base_addr_in, int *npu_mask);
void gemv_primitive_drv(uint32_t base_addr_out, uint32_t base_addr_orig, int *npu_mask);
void bc_primitive_cfg(Tensor *tensor_out, int *npu_mask);
void bc_primitive_drv(uint32_t scalar_in, uint32_t base_addr_out, int *npu_mask);
void mov_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void mov_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask);
void trans_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void trans_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask);
void setcimexp_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void setcimexp_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask);
void getcimexp_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask);
void getcimexp_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask);
void vp_cfg_primitive_op(uint32_t operation, int *npu_mask);
void vp_cfg_primitive_dim0(Tensor *tensor_in1, int *npu_mask);
void vv_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void vv_v_primitive_pre(uint32_t base_addr_in1, uint32_t base_addr_in2, int *npu_mask);
void vv_v_primitive_drv(uint32_t base_addr_out, int *npu_mask);
void vs_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void vs_v_primitive_pre(uint32_t base_addr_in1, uint32_t scalar_in2, int *npu_mask);
void vs_v_primitive_drv(uint32_t base_addr_out, int *npu_mask);
void v_s_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void v_s_primitive_drv(uint32_t base_addr_in1, int *npu_mask, uint32_t *v_s_return);
void v_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void v_v_primitive_drv(uint32_t base_addr_in1, uint32_t base_addr_out, int *npu_mask);
void noc_primitive_cfg(Tensor *tensor_in, int *npu_mask);
void noc_primitive_src_drv(uint32_t base_addr_srcmem, uint32_t dest_idx, int *npu_mask);
void noc_primitive_dest_drv(uint32_t base_addr_destmem, uint32_t src_idx, int *npu_mask);
void noc_primitive_fence_drv(uint32_t *noc_return, int *npu_mask);

  

/////////////////////////////////////VNICE/////////////////////////////////////////////
void v_bc_primitive_cfg(Tensor *tensor_out, int *npu_mask);
void v_bc_primitive_drv(uint32_t *scalar_in_16, uint32_t base_addr_out, int *npu_mask);
void v_vs_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void v_vs_v_primitive_pre(uint32_t base_addr_in1, uint32_t *scalar_in_16, int *npu_mask);
void v_vs_v_primitive_drv(uint32_t base_addr_out, int *npu_mask);
void v_v_s_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask);
void v_v_s_primitive_drv(uint32_t base_addr_in1, int *npu_mask, uint32_t *scalar_out_16);
void v_noc_primitive_cfg(Tensor *tensor_in, int *npu_mask);
void v_noc_primitive_src_drv(uint32_t *base_addr_srcmem_16, uint32_t *dest_idx_16, int *npu_mask);
void v_noc_primitive_dest_drv(uint32_t *base_addr_destmem_16, uint32_t *src_idx_16, int *npu_mask);
int v_noc_primitive_fence_drv();





#endif /* OPERATORS_LIBRARY_PRIMITIVE_INCLUDE_PRIMITIVE_H_ */
