/*
 * primitive_single.c
 *
 *  Created on: 2025年6月28日
 *      Author: zqguo
 */


#include "primitive.h"
#include <riscv_vector.h>



/////////////////////////////////////NICE//////////////////////////////////////////////
void tld_primitive_cfg(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask) {
    ls_config_reg.width_type.type  = tensor_gmem->type;
    ls_config_reg.width_type.width = tensor_gmem->width;
    ls_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_gmem);
    ls_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_gmem);
    ls_config_reg.size_dim2_dim1.size_dim1 = tensor_gmem->dim1;
    ls_config_reg.size_dim2_dim1.size_dim2 = tensor_gmem->dim2;
    ls_config_reg.stride_dim1_gmem = tensor_gmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_gmem = tensor_gmem->byte_stride2.stride2;
    ls_config_reg.stride_dim1_lmem = tensor_lmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_lmem = tensor_lmem->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tld_cfg_width_type(ls_config_reg.width_type_u);
            tld_cfg_size_dim0b_rem_dim0(ls_config_reg.size_dim0b_rem_dim0_u);
            tld_cfg_size_dim2_dim1(ls_config_reg.size_dim2_dim1_u);
            tld_cfg_stride_dim1_gmem(ls_config_reg.stride_dim1_gmem);
            tld_cfg_stride_dim2_gmem(ls_config_reg.stride_dim2_gmem);
            tld_cfg_stride_dim1_lmem(ls_config_reg.stride_dim1_lmem);
            tld_cfg_stride_dim2_lmem(ls_config_reg.stride_dim2_lmem);
        }
    }
}

void tld_primitive_drv(uint32_t base_addr_gmem, uint32_t base_addr_lmem, int *npu_mask) {
    ls_config_reg.base_addr_gmem = base_addr_gmem;
    ls_config_reg.base_addr_lmem = base_addr_lmem;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tld_drv(ls_config_reg.base_addr_gmem, ls_config_reg.base_addr_lmem);
        }
    }
}

void ttld_primitive_cfg(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask) {
    ls_config_reg.width_type.type  = tensor_gmem->type;
    ls_config_reg.width_type.width = tensor_gmem->width;
    ls_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_gmem);
    ls_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_gmem);
    ls_config_reg.size_dim2_dim1.size_dim1 = tensor_gmem->dim1;
    ls_config_reg.size_dim2_dim1.size_dim2 = tensor_gmem->dim2;
    ls_config_reg.stride_dim1_gmem = tensor_gmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_gmem = tensor_gmem->byte_stride2.stride2;
    ls_config_reg.stride_dim1_lmem = tensor_lmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_lmem = tensor_lmem->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tld_cfg_width_type(ls_config_reg.width_type_u);
            tld_cfg_size_dim0b_rem_dim0(ls_config_reg.size_dim0b_rem_dim0_u);
            tld_cfg_size_dim2_dim1(ls_config_reg.size_dim2_dim1_u);
            tld_cfg_stride_dim1_gmem(ls_config_reg.stride_dim1_gmem);
            tld_cfg_stride_dim2_gmem(ls_config_reg.stride_dim2_gmem);
            tld_cfg_stride_dim1_lmem(ls_config_reg.stride_dim1_lmem);
            tld_cfg_stride_dim2_lmem(ls_config_reg.stride_dim2_lmem);
        }
    }
}

void ttld_primitive_drv(uint32_t base_addr_gmem, uint32_t base_addr_lmem, int *npu_mask) {
    ls_config_reg.base_addr_gmem = base_addr_gmem;
    ls_config_reg.base_addr_lmem = base_addr_lmem;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            ttld_drv(ls_config_reg.base_addr_gmem, ls_config_reg.base_addr_lmem);
        }
    }
}

void tst_primitive_cfg(Tensor *tensor_lmem, Tensor *tensor_gmem, int *npu_mask) {
    ls_config_reg.width_type.type  = tensor_lmem->type;
    ls_config_reg.width_type.width = tensor_lmem->width;
    ls_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_lmem);
    ls_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_lmem);
    ls_config_reg.size_dim2_dim1.size_dim1 = tensor_lmem->dim1;
    ls_config_reg.size_dim2_dim1.size_dim2 = tensor_lmem->dim2;
    ls_config_reg.stride_dim1_gmem = tensor_gmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_gmem = tensor_gmem->byte_stride2.stride2;
    ls_config_reg.stride_dim1_lmem = tensor_lmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_lmem = tensor_lmem->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tst_cfg_width_type(ls_config_reg.width_type_u);
            tst_cfg_size_dim0b_rem_dim0(ls_config_reg.size_dim0b_rem_dim0_u);
            tst_cfg_size_dim2_dim1(ls_config_reg.size_dim2_dim1_u);
            tst_cfg_stride_dim1_gmem(ls_config_reg.stride_dim1_gmem);
            tst_cfg_stride_dim2_gmem(ls_config_reg.stride_dim2_gmem);
            tst_cfg_stride_dim1_lmem(ls_config_reg.stride_dim1_lmem);
            tst_cfg_stride_dim2_lmem(ls_config_reg.stride_dim2_lmem);
        }
    }
}

void tst_primitive_drv(uint32_t base_addr_lmem, uint32_t base_addr_gmem, int *npu_mask) {
    ls_config_reg.base_addr_lmem = base_addr_lmem;
    ls_config_reg.base_addr_gmem = base_addr_gmem;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tst_drv(ls_config_reg.base_addr_gmem, ls_config_reg.base_addr_lmem);
        }
    }
}

void gemm_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask) {
    mp_config_reg.op_width_type.type_out   = tensor_out->type;
    mp_config_reg.op_width_type.type_orig  = tensor_orig->type;
    mp_config_reg.op_width_type.type_in    = tensor_in->type;
    mp_config_reg.op_width_type.type_wt    = cim_option->type;
    mp_config_reg.op_width_type.width_out  = tensor_out->width;
    mp_config_reg.op_width_type.width_orig = tensor_orig->width;
    mp_config_reg.op_width_type.width_in   = tensor_in->width;
    mp_config_reg.op_width_type.width_wt   = cim_option->width;
    mp_config_reg.op_width_type.accu       = cim_option->accumulate;
    mp_config_reg.op_width_type.act        = cim_option->activate;
    mp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    mp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    mp_config_reg.size_dim2_dim1_out.size_dim1_out = tensor_out->dim1;
    mp_config_reg.size_dim2_dim1_out.size_dim2_out = tensor_out->dim2;
    mp_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    mp_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;
    mp_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    mp_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    mp_config_reg.size_dim2_dim1_in.size_dim1_in = tensor_in->dim1;
    mp_config_reg.size_dim2_dim1_in.size_dim2_in = tensor_in->dim2;
    mp_config_reg.stride_dim1_in = tensor_in->byte_stride1.stride1;
    mp_config_reg.stride_dim2_in = tensor_in->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            mp_cfg_op_width_type(mp_config_reg.op_width_type_u);
            mp_cfg_size_dim0b_rem_dim0_out(mp_config_reg.size_dim0b_rem_dim0_out_u);
            mp_cfg_size_dim2_dim1_out(mp_config_reg.size_dim2_dim1_out_u);
            mp_cfg_stride_dim1_out(mp_config_reg.stride_dim1_out);
            mp_cfg_stride_dim2_out(mp_config_reg.stride_dim2_out);            
            mp_cfg_size_dim0b_rem_dim0_in(mp_config_reg.size_dim0b_rem_dim0_in_u);
            mp_cfg_size_dim2_dim1_in(mp_config_reg.size_dim2_dim1_in_u);
            mp_cfg_stride_dim1_in(mp_config_reg.stride_dim1_in);
            mp_cfg_stride_dim2_in(mp_config_reg.stride_dim2_in);
        }
    }
}

void gemm_primitive_pre(uint32_t page_index, uint32_t base_addr_in, int *npu_mask) {
    mp_config_reg.base_addr_in = base_addr_in;
    mp_config_reg.base_addr_wt = CIMC_PAGE_BASE_ADDR + page_index * CIMC_PAGE_OFFSET;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            gemm_pre(mp_config_reg.base_addr_wt, mp_config_reg.base_addr_in);

        }
    }
}

void gemm_primitive_drv(uint32_t base_addr_out, uint32_t base_addr_orig, int *npu_mask) {
    mp_config_reg.base_addr_out = base_addr_out;
    mp_config_reg.base_addr_orig = base_addr_orig;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            gemm_drv(mp_config_reg.base_addr_out, mp_config_reg.base_addr_orig);
        }
    }
}

void gemv_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask) {
    mp_config_reg.op_width_type.type_out   = tensor_out->type;
    mp_config_reg.op_width_type.type_orig  = tensor_orig->type;
    mp_config_reg.op_width_type.type_in    = tensor_in->type;
    mp_config_reg.op_width_type.type_wt    = cim_option->type;
    mp_config_reg.op_width_type.width_out  = tensor_out->width;
    mp_config_reg.op_width_type.width_orig = tensor_orig->width;
    mp_config_reg.op_width_type.width_in   = tensor_in->width;
    mp_config_reg.op_width_type.width_wt   = cim_option->width;
    mp_config_reg.op_width_type.accu       = cim_option->accumulate;
    mp_config_reg.op_width_type.act        = cim_option->activate;
    mp_config_reg.op_width_type.shift      = cim_option->shift;
    mp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    mp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    mp_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    mp_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);


    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            mp_cfg_op_width_type(mp_config_reg.op_width_type_u);
            mp_cfg_size_dim0b_rem_dim0_out(mp_config_reg.size_dim0b_rem_dim0_out_u);
            mp_cfg_size_dim0b_rem_dim0_in(mp_config_reg.size_dim0b_rem_dim0_in_u);
        }
    }
}

void gemv_primitive_pre(uint32_t page_index, uint32_t base_addr_in, int *npu_mask) {
    mp_config_reg.base_addr_in = base_addr_in;
    mp_config_reg.base_addr_wt = CIMC_PAGE_BASE_ADDR + page_index * CIMC_PAGE_OFFSET;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            gemv_pre(mp_config_reg.base_addr_wt, mp_config_reg.base_addr_in);
        }
    }
}

void gemv_primitive_drv(uint32_t base_addr_out, uint32_t base_addr_orig, int *npu_mask){
    mp_config_reg.base_addr_out = base_addr_out;
    mp_config_reg.base_addr_orig = base_addr_orig;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            gemv_drv(mp_config_reg.base_addr_out, mp_config_reg.base_addr_orig);
        }
    }
}




void mov_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_in->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_in->dim2;
    tm_config_reg.stride_dim1_in  = tensor_in->byte_stride1.stride1;
    tm_config_reg.stride_dim2_in  = tensor_in->byte_stride2.stride2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tm_cfg_width_type(tm_config_reg.width_type_u);
            tm_cfg_size_dim0b_rem_dim0_out(tm_config_reg.size_dim0b_rem_dim0_out_u);
            tm_cfg_size_dim2_dim1(tm_config_reg.size_dim2_dim1_u);
            tm_cfg_stride_dim1_out(tm_config_reg.stride_dim1_out);
            tm_cfg_stride_dim2_out(tm_config_reg.stride_dim2_out);
            tm_cfg_size_dim0b_rem_dim0_in(tm_config_reg.size_dim0b_rem_dim0_in_u);
            tm_cfg_stride_dim1_in(tm_config_reg.stride_dim1_in);
            tm_cfg_stride_dim2_in(tm_config_reg.stride_dim2_in);     
        }
    }
}

void mov_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask){
    tm_config_reg.base_addr_in  = base_addr_in;
    tm_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            mov_drv(base_addr_in, base_addr_out);
        }
    }
}

void trans_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_in->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_in->dim2;
    tm_config_reg.stride_dim1_in  = tensor_in->byte_stride1.stride1;
    tm_config_reg.stride_dim2_in  = tensor_in->byte_stride2.stride2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tm_cfg_width_type(tm_config_reg.width_type_u);
            tm_cfg_size_dim0b_rem_dim0_out(tm_config_reg.size_dim0b_rem_dim0_out_u);
            tm_cfg_size_dim2_dim1(tm_config_reg.size_dim2_dim1_u);
            tm_cfg_stride_dim1_out(tm_config_reg.stride_dim1_out);
            tm_cfg_stride_dim2_out(tm_config_reg.stride_dim2_out);
            tm_cfg_size_dim0b_rem_dim0_in(tm_config_reg.size_dim0b_rem_dim0_in_u);
            tm_cfg_stride_dim1_in(tm_config_reg.stride_dim1_in);
            tm_cfg_stride_dim2_in(tm_config_reg.stride_dim2_in);     
        }
    }
}

void trans_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask){
    tm_config_reg.base_addr_in  = base_addr_in;
    tm_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            trans_drv(base_addr_in, base_addr_out);
        }
    }
}

void bc_primitive_cfg(Tensor *tensor_out, int *npu_mask){
    tm_config_reg.width_type.type  = tensor_out->type;
    tm_config_reg.width_type.width = tensor_out->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_out->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_out->dim2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tm_cfg_width_type(tm_config_reg.width_type_u);
            tm_cfg_size_dim0b_rem_dim0_out(tm_config_reg.size_dim0b_rem_dim0_out_u);
            tm_cfg_size_dim2_dim1(tm_config_reg.size_dim2_dim1_u);
            tm_cfg_stride_dim1_out(tm_config_reg.stride_dim1_out);
            tm_cfg_stride_dim2_out(tm_config_reg.stride_dim2_out);
    
        }
    }
}

void bc_primitive_drv(uint32_t scalar_in, uint32_t base_addr_out, int *npu_mask){
    tm_config_reg.scalar_in     = scalar_in;
    tm_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            bc_drv(base_addr_out, scalar_in);
        }
    }
}

void setcimexp_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = 0;
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = 8;
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = 0;
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = 8;
    tm_config_reg.size_dim2_dim1.size_dim1 = 1;
    tm_config_reg.size_dim2_dim1.size_dim2 = 1;    
    tm_config_reg.stride_dim1_in  = 8;
    tm_config_reg.stride_dim1_out = 8;


    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tm_cfg_width_type(tm_config_reg.width_type_u);
            tm_cfg_size_dim0b_rem_dim0_out(tm_config_reg.size_dim0b_rem_dim0_out_u);
            tm_cfg_size_dim2_dim1(tm_config_reg.size_dim2_dim1_u);
            tm_cfg_stride_dim1_out(tm_config_reg.stride_dim1_out);
            tm_cfg_size_dim0b_rem_dim0_in(tm_config_reg.size_dim0b_rem_dim0_in_u);
            tm_cfg_stride_dim1_in(tm_config_reg.stride_dim1_in);
        }
    }
}

void setcimexp_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask){
    tm_config_reg.base_addr_in  = base_addr_in;
    tm_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            setcimexp_drv(base_addr_in, base_addr_out);
        }
    }
}

void getcimexp_primitive_cfg(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask){
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = 0;
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = 8;
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = 0;
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = 8;
    tm_config_reg.size_dim2_dim1.size_dim1 = 1;
    tm_config_reg.size_dim2_dim1.size_dim2 = 1;    
    tm_config_reg.stride_dim1_in  = 8;
    tm_config_reg.stride_dim1_out = 8;


    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tm_cfg_width_type(tm_config_reg.width_type_u);
            tm_cfg_size_dim0b_rem_dim0_out(tm_config_reg.size_dim0b_rem_dim0_out_u);
            tm_cfg_size_dim2_dim1(tm_config_reg.size_dim2_dim1_u);
            tm_cfg_stride_dim1_out(tm_config_reg.stride_dim1_out);
            tm_cfg_size_dim0b_rem_dim0_in(tm_config_reg.size_dim0b_rem_dim0_in_u);
            tm_cfg_stride_dim1_in(tm_config_reg.stride_dim1_in);
        }
    }
}

void getcimexp_primitive_drv(uint32_t base_addr_in, uint32_t base_addr_out, int *npu_mask){
    tm_config_reg.base_addr_in  = base_addr_in;
    tm_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            getcimexp_drv(base_addr_in, base_addr_out);
        }
    }
}

void vp_cfg_primitive_op(uint32_t operation, int *npu_mask){
    vp_config_reg.op_width_type.operation  = operation;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_op_width_type(vp_config_reg.op_width_type_u);
        }
    }
}

void vp_cfg_primitive_dim0(Tensor *tensor_in1, int *npu_mask){
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_size_dim0b_rem_dim0_in1(vp_config_reg.size_dim0b_rem_dim0_in1_u);
        }
    }
}

void vv_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_in2   = tensor_in2->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_in2  = tensor_in2->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in2.rem_dim0_in2   = rem_dim0_conversion(tensor_in2);
    vp_config_reg.size_dim0b_rem_dim0_in2.size_dim0b_in2 = dim0b_stride1_conversion(tensor_in2);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_op_width_type(vp_config_reg.op_width_type_u);
            vp_cfg_size_dim0b_rem_dim0_out(vp_config_reg.size_dim0b_rem_dim0_out_u);
            vp_cfg_size_dim0b_rem_dim0_in1(vp_config_reg.size_dim0b_rem_dim0_in1_u);
            vp_cfg_size_dim0b_rem_dim0_in2(vp_config_reg.size_dim0b_rem_dim0_in2_u);
            vp_cfg_special_case(vp_config_reg.special_case_u);
        }
    }
}

void vv_v_primitive_pre(uint32_t base_addr_in1, uint32_t base_addr_in2, int *npu_mask){
    vp_config_reg.base_addr_in1 = base_addr_in1;
    vp_config_reg.base_addr_in2 = base_addr_in2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vv_v_pre(vp_config_reg.base_addr_in1, vp_config_reg.base_addr_in2);
        }
    }
}

void vv_v_primitive_drv(uint32_t base_addr_out, int *npu_mask){
    vp_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vv_v_drv(vp_config_reg.base_addr_out);
        }
    }
}

void vs_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_in2   = tensor_in2->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_in2  = tensor_in2->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_op_width_type(vp_config_reg.op_width_type_u);
            vp_cfg_size_dim0b_rem_dim0_in1(vp_config_reg.size_dim0b_rem_dim0_in1_u);
            vp_cfg_size_dim0b_rem_dim0_out(vp_config_reg.size_dim0b_rem_dim0_out_u);
            vp_cfg_special_case(vp_config_reg.special_case_u);
        }
    }  
}

void vs_v_primitive_pre(uint32_t base_addr_in1, uint32_t scalar_in2, int *npu_mask){
    vp_config_reg.base_addr_in1 = base_addr_in1;
    vp_config_reg.scalar_in2    = scalar_in2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vs_v_pre(vp_config_reg.base_addr_in1, vp_config_reg.scalar_in2);
        }
    }  
}

void vs_v_primitive_drv(uint32_t base_addr_out, int *npu_mask){
    vp_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vs_v_drv(vp_config_reg.base_addr_out);
        }
    }  
}

void v_s_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_op_width_type(vp_config_reg.op_width_type_u);
            vp_cfg_size_dim0b_rem_dim0_in1(vp_config_reg.size_dim0b_rem_dim0_in1_u);
            vp_cfg_special_case(vp_config_reg.special_case_u);
        }
    }  
}

void v_s_primitive_drv(uint32_t base_addr_in1, int *npu_mask, uint32_t *v_s_return){
    vp_config_reg.base_addr_in1 = base_addr_in1;

    for(int group_id = 0; group_id < MAX_GROUP; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_s_return[group_id] = v_s_drv(vp_config_reg.base_addr_in1);
        }
    }  
}

void v_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_op_width_type(vp_config_reg.op_width_type_u);
            vp_cfg_size_dim0b_rem_dim0_in1(vp_config_reg.size_dim0b_rem_dim0_in1_u);
            vp_cfg_size_dim0b_rem_dim0_out(vp_config_reg.size_dim0b_rem_dim0_out_u);
            vp_cfg_special_case(vp_config_reg.special_case_u);
        }
    }  
}

void v_v_primitive_drv(uint32_t base_addr_in1, uint32_t base_addr_out, int *npu_mask){
    vp_config_reg.base_addr_in1   = base_addr_in1;
    vp_config_reg.base_addr_out   = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_v_drv(vp_config_reg.base_addr_in1, vp_config_reg.base_addr_out);
        }
    }  
}



void noc_primitive_cfg(Tensor *tensor_in, int *npu_mask){
    noc_config_reg.width_type.type   = tensor_in->type;
    noc_config_reg.width_type.width  = tensor_in->width;
    noc_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_in);
    noc_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_in);
    noc_config_reg.size_dim2_dim1.size_dim1 = tensor_in->dim1;
    noc_config_reg.size_dim2_dim1.size_dim2 = tensor_in->dim2;
    noc_config_reg.stride_dim1 = tensor_in->byte_stride1.stride1;
    noc_config_reg.stride_dim2 = tensor_in->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            noc_cfg_width_type(noc_config_reg.width_type_u);
            noc_cfg_size_dim0b_rem_dim0(noc_config_reg.size_dim0b_rem_dim0_u);
            noc_cfg_size_dim2_dim1(noc_config_reg.size_dim2_dim1_u);
            noc_cfg_stride_dim1(noc_config_reg.stride_dim1);
            noc_cfg_stride_dim2(noc_config_reg.stride_dim2);
        }
    }  
}

void noc_primitive_src_drv(uint32_t base_addr_srcmem, uint32_t dest_idx, int *npu_mask){
    noc_config_reg.base_addr_srcmem = base_addr_srcmem;
    noc_config_reg.dest_idx = dest_idx;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            noc_src_drv(noc_config_reg.base_addr_srcmem, noc_config_reg.dest_idx);
        }
    }  
}

void noc_primitive_dest_drv(uint32_t base_addr_destmem, uint32_t src_idx, int *npu_mask){
    noc_config_reg.base_addr_destmem = base_addr_destmem;
    noc_config_reg.src_idx = src_idx;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            noc_dest_drv(noc_config_reg.base_addr_destmem, noc_config_reg.src_idx);
        }
    }
}

void noc_primitive_fence_drv(uint32_t *noc_return, int *npu_mask){
    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            noc_return[group_id] = noc_fence_drv();
        }
    }
}


/////////////////////////////////////VNICE/////////////////////////////////////////////
/////////BC Primitive/////////////////////
void v_bc_primitive_cfg(Tensor *tensor_out, int *npu_mask) {
    tm_config_reg.width_type.type  = tensor_out->type;
    tm_config_reg.width_type.width = tensor_out->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_out->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_out->dim2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tm_cfg_width_type(tm_config_reg.width_type_u);
            tm_cfg_size_dim0b_rem_dim0_out(tm_config_reg.size_dim0b_rem_dim0_out_u);
            tm_cfg_size_dim2_dim1(tm_config_reg.size_dim2_dim1_u);
            tm_cfg_stride_dim1_out(tm_config_reg.stride_dim1_out);
            tm_cfg_stride_dim2_out(tm_config_reg.stride_dim2_out);
        }
    }
}

void v_bc_primitive_drv(uint32_t *scalar_in_16, uint32_t base_addr_out, int *npu_mask) {
    tm_config_reg.base_addr_out   = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_bc_drv(tm_config_reg.base_addr_out, &scalar_in_16[group_id * MAX_MASK]);
        }
    }
}

void v_vs_v_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_in2   = tensor_in2->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_in2  = tensor_in2->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_op_width_type(vp_config_reg.op_width_type_u);
            vp_cfg_size_dim0b_rem_dim0_in1(vp_config_reg.size_dim0b_rem_dim0_in1_u);
            vp_cfg_size_dim0b_rem_dim0_out(vp_config_reg.size_dim0b_rem_dim0_out_u);
            vp_cfg_special_case(vp_config_reg.special_case_u);
        }
    }  
}

void v_vs_v_primitive_pre(uint32_t base_addr_in1, uint32_t *scalar_in_16, int *npu_mask){
    vp_config_reg.base_addr_in1   = base_addr_in1;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_vs_v_pre(vp_config_reg.base_addr_in1, &scalar_in_16[group_id * MAX_MASK]);
        }
    }  
}

void v_vs_v_primitive_drv(uint32_t base_addr_out, int *npu_mask){
    vp_config_reg.base_addr_out = base_addr_out;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vs_v_drv(vp_config_reg.base_addr_out);
        }
    }  
}

void v_v_s_primitive_cfg(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vp_cfg_op_width_type(vp_config_reg.op_width_type_u);
            vp_cfg_size_dim0b_rem_dim0_in1(vp_config_reg.size_dim0b_rem_dim0_in1_u);
            vp_cfg_special_case(vp_config_reg.special_case_u);
        }
    }  
}


void v_v_s_primitive_drv(uint32_t base_addr_in1, int *npu_mask, uint32_t *scalar_out_16){
    vp_config_reg.base_addr_in1 = base_addr_in1;
    
    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_v_s_drv(vp_config_reg.base_addr_in1, &scalar_out_16[group_id * MAX_MASK]);
        }
    }  
}

void v_noc_primitive_cfg(Tensor *tensor_in, int *npu_mask){
    noc_config_reg.width_type.type   = tensor_in->type;
    noc_config_reg.width_type.width  = tensor_in->width;
    noc_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_in);
    noc_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_in);
    noc_config_reg.size_dim2_dim1.size_dim1 = tensor_in->dim1;
    noc_config_reg.size_dim2_dim1.size_dim2 = tensor_in->dim2;
    noc_config_reg.stride_dim1 = tensor_in->byte_stride1.stride1;
    noc_config_reg.stride_dim2 = tensor_in->byte_stride2.stride2;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            noc_cfg_width_type(noc_config_reg.width_type_u);
            noc_cfg_size_dim0b_rem_dim0(noc_config_reg.size_dim0b_rem_dim0_u);
            noc_cfg_size_dim2_dim1(noc_config_reg.size_dim2_dim1_u);
            noc_cfg_stride_dim1(noc_config_reg.stride_dim1);
            noc_cfg_stride_dim2(noc_config_reg.stride_dim2);
        }
    }  
}

void v_noc_primitive_src_drv(uint32_t *base_addr_srcmem_16,  uint32_t *dest_idx_16, int *npu_mask){

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_noc_src_drv(&base_addr_srcmem_16[group_id * MAX_MASK], &dest_idx_16[group_id * MAX_MASK]);
        }
    }  
}

void v_noc_primitive_dest_drv(uint32_t *base_addr_destmem_16, uint32_t *src_idx_16, int *npu_mask){

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_noc_dest_drv(&base_addr_destmem_16[group_id * MAX_MASK], &src_idx_16[group_id * MAX_MASK]);
        }
    }  
}

int v_noc_primitive_fence_drv(){
    return noc_fence_drv();
}


