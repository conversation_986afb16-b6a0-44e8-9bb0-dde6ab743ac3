/*
 * primitive_batch.c
 *
 *  Created on: 2025年6月28日
 *      Author: 1572
 */


#include "primitive.h"
#include <riscv_vector.h>
#include "hardware_inst_data.h"


//Software And Hardware Conversion
uint32_t dim0b_stride1_conversion(Tensor *tensor_in){
    uint32_t size_dim0a = 256 >> tensor_in->width; 
    return (tensor_in->dim0 + size_dim0a - 1) / size_dim0a;
}
uint32_t rem_dim0_conversion(Tensor *tensor_in){
    uint32_t size_dim0a = 256 >> tensor_in->width; 
    return tensor_in->dim0 % size_dim0a;
}
uint32_t bytes_stride2(Tensor *tensor_in){
    uint32_t bytes_stride1 = dim0b_stride1_conversion(tensor_in);
    return bytes_stride1 * tensor_in->dim1;
}
uint32_t page_addr(CIM_Option *cim_option){
    uint32_t page_addr = CIMC_PAGE_BASE_ADDR + cim_option->page_index * CIMC_PAGE_OFFSET;
    return page_addr;
}


//////////////////////////////////////NICE/////////////////////////////////////////////
//Primitive Batch Function
void wr_lmem_primitive(uint32_t base_addr_lmem, uint32_t wr_data_lmem, int *npu_mask){
    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            wr_lmem_drv(base_addr_lmem, wr_data_lmem);
        }
    }
}

uint32_t rd_lmem_primitive(uint32_t base_addr_lmem, int group_id, int npu_mask){
	npu_group_mask_drv(group_id, npu_mask);
	return rd_lmem_drv(base_addr_lmem);
}

int sync_primitive(){
    return sync_drv();
}

void switch_cimc_primitive(uint32_t base_addr_lmem, uint32_t cimc_addr_mode, int *npu_mask){
    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            switch_cimc_drv(base_addr_lmem, cimc_addr_mode);
        }
    }
}

void tld_primitive(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask){
    ls_config_reg.width_type.type  = tensor_gmem->type;
    ls_config_reg.width_type.width = tensor_gmem->width;
    ls_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_gmem);
    ls_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_gmem);
    ls_config_reg.size_dim2_dim1.size_dim1 = tensor_gmem->dim1;
    ls_config_reg.size_dim2_dim1.size_dim2 = tensor_gmem->dim2;
    ls_config_reg.stride_dim1_gmem = tensor_gmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_gmem = tensor_gmem->byte_stride2.stride2;
    ls_config_reg.stride_dim1_lmem = tensor_lmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_lmem = tensor_lmem->byte_stride2.stride2;
    ls_config_reg.base_addr_gmem = tensor_gmem->base_addr;
    ls_config_reg.base_addr_lmem = tensor_lmem->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tld_cfg_pre_drv(ls_config_reg.width_type_u, ls_config_reg.size_dim0b_rem_dim0_u, ls_config_reg.size_dim2_dim1_u,
                            ls_config_reg.stride_dim1_gmem, ls_config_reg.stride_dim2_gmem,
                            ls_config_reg.stride_dim1_lmem, ls_config_reg.stride_dim2_lmem,
                            ls_config_reg.base_addr_gmem, ls_config_reg.base_addr_lmem);
        }
    }
}

void ttld_primitive(Tensor *tensor_gmem, Tensor *tensor_lmem, int *npu_mask) {
    ls_config_reg.width_type.type  = tensor_gmem->type;
    ls_config_reg.width_type.width = tensor_gmem->width;
    ls_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_gmem);
    ls_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_gmem);
    ls_config_reg.size_dim2_dim1.size_dim1 = tensor_gmem->dim1;
    ls_config_reg.size_dim2_dim1.size_dim2 = tensor_gmem->dim2;
    ls_config_reg.stride_dim1_gmem = tensor_gmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_gmem = tensor_gmem->byte_stride2.stride2;
    ls_config_reg.stride_dim1_lmem = tensor_lmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_lmem = tensor_lmem->byte_stride2.stride2;
    ls_config_reg.base_addr_gmem = tensor_gmem->base_addr;
    ls_config_reg.base_addr_lmem = tensor_lmem->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            ttld_cfg_pre_drv(ls_config_reg.width_type_u, ls_config_reg.size_dim0b_rem_dim0_u, ls_config_reg.size_dim2_dim1_u,
                            ls_config_reg.stride_dim1_gmem, ls_config_reg.stride_dim2_gmem,
                            ls_config_reg.stride_dim1_lmem, ls_config_reg.stride_dim2_lmem,
                            ls_config_reg.base_addr_gmem, ls_config_reg.base_addr_lmem);
        }
    }
}

void tst_primitive(Tensor *tensor_lmem, Tensor *tensor_gmem, int *npu_mask) {
    ls_config_reg.width_type.type  = tensor_gmem->type;
    ls_config_reg.width_type.width = tensor_gmem->width;
    ls_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_gmem);
    ls_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_gmem);
    ls_config_reg.size_dim2_dim1.size_dim1 = tensor_gmem->dim1;
    ls_config_reg.size_dim2_dim1.size_dim2 = tensor_gmem->dim2;
    ls_config_reg.stride_dim1_gmem = tensor_gmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_gmem = tensor_gmem->byte_stride2.stride2;
    ls_config_reg.stride_dim1_lmem = tensor_lmem->byte_stride1.stride1;
    ls_config_reg.stride_dim2_lmem = tensor_lmem->byte_stride2.stride2;
    ls_config_reg.base_addr_gmem = tensor_gmem->base_addr;
    ls_config_reg.base_addr_lmem = tensor_lmem->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            tst_cfg_pre_drv(ls_config_reg.width_type_u, ls_config_reg.size_dim0b_rem_dim0_u, ls_config_reg.size_dim2_dim1_u,
                            ls_config_reg.stride_dim1_gmem, ls_config_reg.stride_dim2_gmem,
                            ls_config_reg.stride_dim1_lmem, ls_config_reg.stride_dim2_lmem,
                            ls_config_reg.base_addr_gmem, ls_config_reg.base_addr_lmem);
        }
    }
}

void bc_primitive(uint32_t scalar_in, Tensor *tensor_out, int *npu_mask) {
    tm_config_reg.width_type.type  = tensor_out->type;
    tm_config_reg.width_type.width = tensor_out->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_out->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_out->dim2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;
    tm_config_reg.base_addr_out   = tensor_out->base_addr;
    tm_config_reg.scalar_in       = scalar_in;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            bc_cfg_pre_drv(tm_config_reg.width_type_u, tm_config_reg.size_dim0b_rem_dim0_out_u, tm_config_reg.size_dim2_dim1_u, 
                            tm_config_reg.stride_dim1_out, tm_config_reg.stride_dim2_out, 
                            tm_config_reg.scalar_in, tm_config_reg.base_addr_out);
        }
    }
}

void mov_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask) {
    tm_config_reg.width_type.type  = tensor_out->type;
    tm_config_reg.width_type.width = tensor_out->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_out->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_out->dim2;
    tm_config_reg.stride_dim1_in  = tensor_in->byte_stride1.stride1;
    tm_config_reg.stride_dim2_in  = tensor_in->byte_stride2.stride2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;
    tm_config_reg.base_addr_in    = tensor_in->base_addr;
    tm_config_reg.base_addr_out   = tensor_out->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            mov_cfg_pre_drv(tm_config_reg.width_type_u, tm_config_reg.size_dim0b_rem_dim0_in_u, tm_config_reg.size_dim0b_rem_dim0_out_u, 
                            tm_config_reg.stride_dim1_out, tm_config_reg.stride_dim2_out, 
                            tm_config_reg.stride_dim1_in, tm_config_reg.stride_dim2_in,
                            tm_config_reg.size_dim2_dim1_u, tm_config_reg.base_addr_in, tm_config_reg.base_addr_out);
        }
    }
}

void trans_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask) {
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_in->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_in->dim2;
    tm_config_reg.stride_dim1_in  = tensor_in->byte_stride1.stride1;
    tm_config_reg.stride_dim2_in  = tensor_in->byte_stride2.stride2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;
    tm_config_reg.base_addr_in    = tensor_in->base_addr;
    tm_config_reg.base_addr_out   = tensor_out->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            trans_cfg_pre_drv(tm_config_reg.width_type_u, tm_config_reg.size_dim0b_rem_dim0_in_u, tm_config_reg.size_dim0b_rem_dim0_out_u, 
                            tm_config_reg.stride_dim1_out, tm_config_reg.stride_dim2_out, 
                            tm_config_reg.stride_dim1_in, tm_config_reg.stride_dim2_in,
                            tm_config_reg.size_dim2_dim1_u, tm_config_reg.base_addr_in, tm_config_reg.base_addr_out);
        }
    }
}

void setcimexp_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask) {
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = 0;
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = 8;
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = 0;
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = 8;
    tm_config_reg.size_dim2_dim1.size_dim1 = 1;
    tm_config_reg.size_dim2_dim1.size_dim2 = 1;    
    tm_config_reg.stride_dim1_in  = 8;
    tm_config_reg.stride_dim1_out = 8;
    tm_config_reg.base_addr_in    = tensor_in->base_addr;
    tm_config_reg.base_addr_out   = tensor_out->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            setcimexp_cfg_pre_drv(tm_config_reg.width_type_u, tm_config_reg.size_dim0b_rem_dim0_in_u, tm_config_reg.size_dim0b_rem_dim0_out_u, 
                                tm_config_reg.stride_dim1_out, tm_config_reg.stride_dim1_in, tm_config_reg.size_dim2_dim1_u,
                                tm_config_reg.base_addr_in, tm_config_reg.base_addr_out);
        }
    }
}

void getcimexp_primitive(Tensor *tensor_in, Tensor *tensor_out, int *npu_mask) {
    tm_config_reg.width_type.type  = tensor_in->type;
    tm_config_reg.width_type.width = tensor_in->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = 0;
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = 8;
    tm_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = 0;
    tm_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = 8;
    tm_config_reg.size_dim2_dim1.size_dim1 = 1;
    tm_config_reg.size_dim2_dim1.size_dim2 = 1; 
    tm_config_reg.stride_dim1_in  = 8;
    tm_config_reg.stride_dim1_out = 8;
    tm_config_reg.base_addr_in    = tensor_in->base_addr;
    tm_config_reg.base_addr_out   = tensor_out->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            getcimexp_cfg_pre_drv(tm_config_reg.width_type_u, tm_config_reg.size_dim0b_rem_dim0_in_u, tm_config_reg.size_dim0b_rem_dim0_out_u, 
                                tm_config_reg.stride_dim1_out, tm_config_reg.stride_dim1_in, tm_config_reg.size_dim2_dim1_u,
                                tm_config_reg.base_addr_in, tm_config_reg.base_addr_out);
        }
    }
}



void  gemm_primitive(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask) {
    mp_config_reg.op_width_type.type_out   = tensor_out->type;
    mp_config_reg.op_width_type.type_orig  = tensor_orig->type;
    mp_config_reg.op_width_type.type_in    = tensor_in->type;
    mp_config_reg.op_width_type.type_wt    = cim_option->type;
    mp_config_reg.op_width_type.width_out  = tensor_out->width;
    mp_config_reg.op_width_type.width_orig = tensor_orig->width;
    mp_config_reg.op_width_type.width_in   = tensor_in->width;
    mp_config_reg.op_width_type.width_wt   = cim_option->width;
    mp_config_reg.op_width_type.accu       = cim_option->accumulate;
    mp_config_reg.op_width_type.act        = cim_option->activate;
    mp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    mp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    mp_config_reg.size_dim2_dim1_out.size_dim1_out = tensor_out->dim1;
    mp_config_reg.size_dim2_dim1_out.size_dim2_out = tensor_out->dim2;
    mp_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    mp_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;
    mp_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    mp_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    mp_config_reg.size_dim2_dim1_in.size_dim1_in = tensor_in->dim1;
    mp_config_reg.size_dim2_dim1_in.size_dim2_in = tensor_in->dim2;
    mp_config_reg.stride_dim1_in = tensor_in->byte_stride1.stride1;
    mp_config_reg.stride_dim2_in = tensor_in->byte_stride2.stride2;
    mp_config_reg.base_addr_wt   = page_addr(cim_option);
    mp_config_reg.base_addr_in   = tensor_in->base_addr;
    mp_config_reg.base_addr_out  = tensor_out->base_addr;
    mp_config_reg.base_addr_orig = tensor_orig->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            gemm_cfg_pre_drv(mp_config_reg.op_width_type_u, mp_config_reg.size_dim0b_rem_dim0_out_u, mp_config_reg.size_dim2_dim1_out_u,
                            mp_config_reg.stride_dim1_out, mp_config_reg.stride_dim2_out, mp_config_reg.size_dim0b_rem_dim0_in_u, 
                            mp_config_reg.size_dim2_dim1_out_u, mp_config_reg.stride_dim1_in, mp_config_reg.stride_dim2_in,
                            mp_config_reg.base_addr_wt, mp_config_reg.base_addr_in,
                            mp_config_reg.base_addr_out, mp_config_reg.base_addr_orig);
        }
    }
}

void gemv_primitive(Tensor *tensor_in, Tensor *tensor_out, Tensor *tensor_orig, CIM_Option *cim_option, int *npu_mask) {
    mp_config_reg.op_width_type.type_out   = tensor_out->type;
    mp_config_reg.op_width_type.type_orig  = tensor_orig->type;
    mp_config_reg.op_width_type.type_in    = tensor_in->type;
    mp_config_reg.op_width_type.type_wt    = cim_option->type;
    mp_config_reg.op_width_type.width_out  = tensor_out->width;
    mp_config_reg.op_width_type.width_orig = tensor_orig->width;
    mp_config_reg.op_width_type.width_in   = tensor_in->width;
    mp_config_reg.op_width_type.width_wt   = cim_option->width;
    mp_config_reg.op_width_type.accu       = cim_option->accumulate;
    mp_config_reg.op_width_type.act        = cim_option->activate;
    mp_config_reg.op_width_type.shift      = cim_option->shift;
    mp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    mp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    mp_config_reg.size_dim0b_rem_dim0_in.rem_dim0_in     = rem_dim0_conversion(tensor_in);
    mp_config_reg.size_dim0b_rem_dim0_in.size_dim0b_in   = dim0b_stride1_conversion(tensor_in);
    mp_config_reg.base_addr_wt   = page_addr(cim_option);
    mp_config_reg.base_addr_in   = tensor_in->base_addr;
    mp_config_reg.base_addr_out  = tensor_out->base_addr;
    mp_config_reg.base_addr_orig = tensor_orig->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            gemv_cfg_pre_drv(mp_config_reg.op_width_type_u, mp_config_reg.size_dim0b_rem_dim0_out_u, mp_config_reg.size_dim0b_rem_dim0_in_u, 
                            mp_config_reg.base_addr_wt, mp_config_reg.base_addr_in,
                            mp_config_reg.base_addr_out, mp_config_reg.base_addr_orig);
        }
    }
}


void vv_v_primitive(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_in2   = tensor_in2->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_in2  = tensor_in2->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in2.rem_dim0_in2   = rem_dim0_conversion(tensor_in2);
    vp_config_reg.size_dim0b_rem_dim0_in2.size_dim0b_in2 = dim0b_stride1_conversion(tensor_in2);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.base_addr_in1   = tensor_in1->base_addr;
    vp_config_reg.base_addr_in2   = tensor_in2->base_addr;
    vp_config_reg.base_addr_out   = tensor_out->base_addr;
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vv_v_cfg_pre_drv(vp_config_reg.op_width_type_u, vp_config_reg.size_dim0b_rem_dim0_in1_u, vp_config_reg.size_dim0b_rem_dim0_in2_u,
                            vp_config_reg.size_dim0b_rem_dim0_out_u, vp_config_reg.special_case_u,  
                            vp_config_reg.base_addr_in1, vp_config_reg.base_addr_in2, vp_config_reg.base_addr_out);
        }
    }
}

void vs_v_primitive(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_in2   = tensor_in2->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_in2  = tensor_in2->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.base_addr_in1   = tensor_in1->base_addr;
    vp_config_reg.scalar_in2 = vp_option->scalar_in2;
    vp_config_reg.base_addr_out   = tensor_out->base_addr;
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            vs_v_cfg_pre_drv(vp_config_reg.op_width_type_u, vp_config_reg.size_dim0b_rem_dim0_in1_u, 
                            vp_config_reg.size_dim0b_rem_dim0_out_u, vp_config_reg.special_case_u,  
                            vp_config_reg.base_addr_in1, vp_config_reg.scalar_in2, vp_config_reg.base_addr_out);
        }
    }  
}

void v_s_primitive(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask, uint32_t *v_s_return){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;
    vp_config_reg.base_addr_in1   = tensor_in1->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_s_return[group_id] = v_s_cfg_pre_drv(vp_config_reg.op_width_type_u, vp_config_reg.size_dim0b_rem_dim0_in1_u, 
                                vp_config_reg.special_case_u, vp_config_reg.base_addr_in1);
        }
    }  
}

void v_v_primitive(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;
    vp_config_reg.base_addr_in1   = tensor_in1->base_addr;
    vp_config_reg.base_addr_out   = tensor_out->base_addr;

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_v_cfg_pre_drv(vp_config_reg.op_width_type_u, vp_config_reg.size_dim0b_rem_dim0_in1_u, vp_config_reg.size_dim0b_rem_dim0_out_u,   
                            vp_config_reg.base_addr_in1, vp_config_reg.base_addr_out, vp_config_reg.special_case_u);
        }
    }  
}

// void noc_primitive(Tensor *tensor_in1, NOC_Option *noc_option, int *npu_mask, int *noc_return){
//     noc_config_reg.width_type.type   = tensor_in1->type;
//     noc_config_reg.width_type.width  = tensor_in1->width;
//     noc_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_in1);
//     noc_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_in1);
//     noc_config_reg.size_dim2_dim1.size_dim1 = tensor_in1->dim1;
//     noc_config_reg.size_dim2_dim1.size_dim2 = tensor_in1->dim2;
//     noc_config_reg.stride_dim1 = tensor_in1->byte_stride1.stride1;
//     noc_config_reg.stride_dim2 = tensor_in1->byte_stride2.stride2;
//     noc_config_reg.base_addr_destmem = noc_option->base_addr_destmem;
//     noc_config_reg.base_addr_srcmem  = noc_option->base_addr_srcmem;
//     noc_config_reg.dest_idx = noc_option->dest_idx;
//     noc_config_reg.src_idx  = noc_option->src_idx;

//     for(int group_id = 0; group_id < MAX_MASK; group_id++) {
//         if(npu_mask[group_id] != 0) {
//             npu_group_mask_drv(group_id, npu_mask[group_id]);
//             noc_return[group_id] = noc_cfg_pre_drv(noc_config_reg.width_type_u, noc_config_reg.size_dim0b_rem_dim0_u, noc_config_reg.size_dim2_dim1_u,
//                                                 noc_config_reg.stride_dim1, noc_config_reg.stride_dim2, 
//                                                 noc_config_reg.base_addr_destmem, noc_config_reg.dest_idx,
//                                                 noc_config_reg.base_addr_srcmem, noc_config_reg.src_idx);
//         }
//     }  
// }


/////////////////////////////////////VNICE/////////////////////////////////////////////
void v_rd_lmem_primitive(uint32_t base_addr_lmem, uint32_t *rd_lmem_out_16, int *npu_mask){

    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_rd_lmem_drv(base_addr_lmem, &rd_lmem_out_16[group_id * MAX_MASK]);
        }
    }
}

void v_wr_lmem_primitive(uint32_t base_addr_lmem, uint32_t *wr_data_in_16, int *npu_mask){
  
    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_wr_lmem_drv(base_addr_lmem, &wr_data_in_16[group_id * MAX_MASK]);
        }
    }
}

void v_bc_primitive(uint32_t *scalar_in_16, Tensor *tensor_out, int *npu_mask) {
    tm_config_reg.width_type.type  = tensor_out->type;
    tm_config_reg.width_type.width = tensor_out->width;
    tm_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    tm_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    tm_config_reg.size_dim2_dim1.size_dim1 = tensor_out->dim1;
    tm_config_reg.size_dim2_dim1.size_dim2 = tensor_out->dim2;
    tm_config_reg.stride_dim1_out = tensor_out->byte_stride1.stride1;
    tm_config_reg.stride_dim2_out = tensor_out->byte_stride2.stride2;
    tm_config_reg.base_addr_out   = tensor_out->base_addr;
    
    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_bc_cfg_pre_drv(tm_config_reg.width_type_u, tm_config_reg.size_dim0b_rem_dim0_out_u, tm_config_reg.size_dim2_dim1_u, 
                            tm_config_reg.stride_dim1_out, tm_config_reg.stride_dim2_out, 
                            &scalar_in_16[group_id * MAX_MASK], tm_config_reg.base_addr_out);
        }
    }
}


void v_vs_v_primitive(Tensor *tensor_in1, Tensor *tensor_in2, Tensor *tensor_out, VP_Option *vp_option, uint32_t *scalar_in_16, int *npu_mask){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_in2   = tensor_in2->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_in2  = tensor_in2->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_out.rem_dim0_out   = rem_dim0_conversion(tensor_out);
    vp_config_reg.size_dim0b_rem_dim0_out.size_dim0b_out = dim0b_stride1_conversion(tensor_out);
    vp_config_reg.base_addr_in1      = tensor_in1->base_addr;
    vp_config_reg.base_addr_out      = tensor_out->base_addr;
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;
 
    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_vs_v_cfg_pre_drv(vp_config_reg.op_width_type_u, vp_config_reg.size_dim0b_rem_dim0_in1_u, 
                            vp_config_reg.size_dim0b_rem_dim0_out_u, vp_config_reg.special_case_u,  
                            vp_config_reg.base_addr_in1, &scalar_in_16[group_id * MAX_MASK], vp_config_reg.base_addr_out);
        }
    }  
}


void v_v_s_primitive(Tensor *tensor_in1, Tensor *tensor_out, VP_Option *vp_option, int *npu_mask, uint32_t *scalar_out_16){
    vp_config_reg.op_width_type.type_in1   = tensor_in1->type;
    vp_config_reg.op_width_type.type_out   = tensor_out->type;
    vp_config_reg.op_width_type.width_in1  = tensor_in1->width;
    vp_config_reg.op_width_type.width_out  = tensor_out->width;
    vp_config_reg.op_width_type.operation  = vp_option->operation;
    vp_config_reg.size_dim0b_rem_dim0_in1.rem_dim0_in1   = rem_dim0_conversion(tensor_in1);
    vp_config_reg.size_dim0b_rem_dim0_in1.size_dim0b_in1 = dim0b_stride1_conversion(tensor_in1);
    vp_config_reg.special_case.disable0   = vp_option->special_case.disable0;
    vp_config_reg.special_case.saturate   = vp_option->special_case.saturate;
    vp_config_reg.special_case.round_mode = vp_option->special_case.round_mode;
    vp_config_reg.base_addr_in1   = tensor_in1->base_addr;



    for(int group_id = 0; group_id < MAX_MASK; group_id++) {
        if(npu_mask[group_id] != 0) {
            npu_group_mask_drv(group_id, npu_mask[group_id]);
            v_v_s_cfg_pre_drv(vp_config_reg.op_width_type_u, vp_config_reg.size_dim0b_rem_dim0_in1_u, vp_config_reg.special_case_u, vp_config_reg.base_addr_in1, &scalar_out_16[group_id * MAX_MASK]);
        }
    }  
}

// void v_noc_primitive(Tensor *tensor_in1, NOC_Option *noc_option, int *npu_mask, uint32_t *dest_idx_16, uint32_t *src_idx_16, uint32_t *noc_return_16){
//     noc_config_reg.width_type.type   = tensor_in1->type;
//     noc_config_reg.width_type.width  = tensor_in1->width;
//     noc_config_reg.size_dim0b_rem_dim0.rem_dim0   = rem_dim0_conversion(tensor_in1);
//     noc_config_reg.size_dim0b_rem_dim0.size_dim0b = dim0b_stride1_conversion(tensor_in1);
//     noc_config_reg.size_dim2_dim1.size_dim1 = tensor_in1->dim1;
//     noc_config_reg.size_dim2_dim1.size_dim2 = tensor_in1->dim2;
//     noc_config_reg.stride_dim1 = tensor_in1->byte_stride1.stride1;
//     noc_config_reg.stride_dim2 = tensor_in1->byte_stride2.stride2;
//     noc_config_reg.base_addr_destmem = tensor_out->base_addr;
//     noc_config_reg.base_addr_srcmem  = tensor_in1->base_addr;

//     vuint32m1_t dest_idx_vec;
//     vuint32m1_t src_idx_vec;
//     size_t vl = __riscv_vsetvl_e32m1(MAX_MASK); 

//     for(int group_id = 0; group_id < MAX_MASK; group_id++) {
//         if(npu_mask[group_id] != 0) {
//             npu_group_mask_drv(group_id, npu_mask[group_id]);
//             dest_idx_vec = __riscv_vle32_v_u32m1(&dest_idx_16[group_id * MAX_MASK], vl);
//             src_idx_vec = __riscv_vle32_v_u32m1(&src_idx_16[group_id * MAX_MASK], vl);
//             noc_return_16[group_id * MAX_MASK] = v_noc_cfg_pre_drv(noc_config_reg.width_type_u, noc_config_reg.size_dim0b_rem_dim0_u, noc_config_reg.size_dim2_dim1_u,
//                                                 noc_config_reg.stride_dim1, noc_config_reg.stride_dim2, 
//                                                 noc_config_reg.base_addr_destmem, dest_idx_vec,
//                                                 noc_config_reg.base_addr_srcmem, src_idx_vec);
//         }
//     }  
// }

