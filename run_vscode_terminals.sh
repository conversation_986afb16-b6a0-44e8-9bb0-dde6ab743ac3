#!/bin/bash

# VSCode集成终端脚本执行器
# 此脚本专为VSCode集成终端设计

BASE_DIR="/data/users/jxchen/mosim_workspace/work"
NPU_SCRIPT_DIR="${BASE_DIR}/python"
SOC_SCRIPT_DIR="${BASE_DIR}/soc/n900_vnice_npu_soc"

echo "========================================="
echo "VSCode集成终端双脚本执行器"
echo "========================================="
echo ""

# 检查脚本文件是否存在
if [ ! -f "${NPU_SCRIPT_DIR}/npu_run.sh" ]; then
    echo "❌ 错误: ${NPU_SCRIPT_DIR}/npu_run.sh 不存在"
    exit 1
fi

if [ ! -f "${SOC_SCRIPT_DIR}/run_with_log.sh" ]; then
    echo "❌ 错误: ${SOC_SCRIPT_DIR}/run_with_log.sh 不存在"
    exit 1
fi

# 确保脚本有执行权限
chmod +x "${NPU_SCRIPT_DIR}/npu_run.sh" 2>/dev/null
chmod +x "${SOC_SCRIPT_DIR}/run_with_log.sh" 2>/dev/null

echo "📋 请选择执行方式："
echo "1. 顺序执行（先运行npu_run.sh，完成后运行run_with_log.sh）"
echo "2. 并行执行（同时后台运行两个脚本）"
echo "3. 仅运行 npu_run.sh"
echo "4. 仅运行 run_with_log.sh"
echo "5. 显示VSCode多终端手动操作指令"
echo ""

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🚀 顺序执行模式"
        echo "----------------------------------------"
        echo "📍 正在运行 npu_run.sh..."
        cd "${NPU_SCRIPT_DIR}"
        ./npu_run.sh
        echo ""
        echo "✅ npu_run.sh 执行完成"
        echo "📍 正在运行 run_with_log.sh..."
        cd "${SOC_SCRIPT_DIR}"
        ./run_with_log.sh
        echo "✅ 所有脚本执行完成"
        ;;
    2)
        echo ""
        echo "🚀 并行执行模式"
        echo "----------------------------------------"
        echo "📍 启动 npu_run.sh (后台运行)..."
        (cd "${NPU_SCRIPT_DIR}" && ./npu_run.sh) &
        NPU_PID=$!
        
        echo "📍 启动 run_with_log.sh (后台运行)..."
        (cd "${SOC_SCRIPT_DIR}" && ./run_with_log.sh) &
        SOC_PID=$!
        
        echo ""
        echo "两个脚本正在后台运行："
        echo "  - npu_run.sh (PID: $NPU_PID)"
        echo "  - run_with_log.sh (PID: $SOC_PID)"
        echo ""
        echo "正在等待脚本完成... (Ctrl+C 可中止)"
        
        # 等待两个进程完成
        wait $NPU_PID
        NPU_EXIT=$?
        wait $SOC_PID
        SOC_EXIT=$?
        
        echo ""
        echo "✅ 执行结果："
        echo "  - npu_run.sh: $([ $NPU_EXIT -eq 0 ] && echo "成功" || echo "失败 (退出码: $NPU_EXIT)")"
        echo "  - run_with_log.sh: $([ $SOC_EXIT -eq 0 ] && echo "成功" || echo "失败 (退出码: $SOC_EXIT)")"
        ;;
    3)
        echo ""
        echo "🚀 运行 npu_run.sh"
        echo "----------------------------------------"
        cd "${NPU_SCRIPT_DIR}"
        ./npu_run.sh
        echo "✅ npu_run.sh 执行完成"
        ;;
    4)
        echo ""
        echo "🚀 运行 run_with_log.sh"
        echo "----------------------------------------"
        cd "${SOC_SCRIPT_DIR}"
        ./run_with_log.sh
        echo "✅ run_with_log.sh 执行完成"
        ;;
    5)
        echo ""
        echo "📖 VSCode多终端手动操作指令"
        echo "========================================="
        echo ""
        echo "在VSCode中打开多个终端标签页："
        echo ""
        echo "1️⃣ 打开第一个终端 (快捷键: Ctrl + \`)"
        echo "   在终端中运行："
        echo "   cd ${NPU_SCRIPT_DIR}"
        echo "   ./npu_run.sh"
        echo ""
        echo "2️⃣ 打开第二个终端 (快捷键: Ctrl + Shift + \`)"
        echo "   在新终端中运行："
        echo "   cd ${SOC_SCRIPT_DIR}"
        echo "   ./run_with_log.sh"
        echo ""
        echo "🔧 VSCode终端操作技巧："
        echo "   • Ctrl + \`           : 显示/隐藏终端"
        echo "   • Ctrl + Shift + \`   : 创建新终端"
        echo "   • 点击终端标签页      : 切换终端"
        echo "   • 右键终端标签页      : 更多选项"
        echo ""
        echo "📁 或者您也可以："
        echo "   • 在VSCode资源管理器中右键python文件夹 → '在集成终端中打开'"
        echo "   • 在VSCode资源管理器中右键soc/n900_vnice_npu_soc文件夹 → '在集成终端中打开'"
        ;;
    *)
        echo "❌ 无效选择，请重新运行脚本"
        exit 1
        ;;
esac

echo ""
echo "========================================="
echo "脚本执行完成"
echo "========================================="
