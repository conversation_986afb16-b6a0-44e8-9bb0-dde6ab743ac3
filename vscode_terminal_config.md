# VSCode终端配置指南

## 快速启动方法

### 方法1: 使用交互式脚本
```bash
./run_vscode_terminals.sh
```
提供多种执行选项，包括顺序执行、并行执行等。

### 方法2: 快速后台运行
```bash
./quick_dual_run.sh
```
快速启动两个脚本在后台运行。

### 方法3: 手动多终端操作

#### 在VSCode中打开多个终端：

1. **打开第一个终端**
   - 快捷键: `Ctrl + \``
   - 或者: 菜单栏 → 终端 → 新建终端

2. **打开第二个终端**
   - 快捷键: `Ctrl + Shift + \``
   - 或者: 终端面板右上角的 `+` 按钮

3. **分别运行脚本**
   ```bash
   # 终端1
   cd /data/users/jxchen/mosim_workspace/work/python
   ./npu_run.sh
   
   # 终端2
   cd /data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc
   ./run_with_log.sh
   ```

## VSCode终端优化配置

在VSCode中按 `Ctrl + ,` 打开设置，搜索以下配置项：

### 1. 设置默认工作目录
```json
{
  "terminal.integrated.cwd": "/data/users/jxchen/mosim_workspace/work"
}
```

### 2. 终端字体和大小
```json
{
  "terminal.integrated.fontSize": 14,
  "terminal.integrated.fontFamily": "Consolas, 'Courier New', monospace"
}
```

### 3. 终端行为配置
```json
{
  "terminal.integrated.confirmOnExit": "hasChildProcesses",
  "terminal.integrated.enableBell": false,
  "terminal.integrated.scrollback": 10000
}
```

## 快捷键速查

| 操作 | 快捷键 |
|------|--------|
| 显示/隐藏终端 | `Ctrl + \`` |
| 新建终端 | `Ctrl + Shift + \`` |
| 切换终端 | `Ctrl + PageUp/PageDown` |
| 关闭当前终端 | `Ctrl + Shift + W` |
| 清屏 | `Ctrl + L` |
| 中断进程 | `Ctrl + C` |

## 日志监控

两个脚本的日志文件位置：
- NPU Demo日志: `/data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log`
- SOC日志: `/data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log`

监控命令：
```bash
# 实时查看NPU日志
tail -f /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log

# 实时查看SOC日志  
tail -f /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log
```

## 故障排除

1. **权限问题**
   ```bash
   chmod +x *.sh
   ```

2. **路径问题**
   确保在正确的工作目录中运行脚本

3. **进程管理**
   ```bash
   # 查看后台进程
   ps aux | grep -E "(npu_demo|mosim-top)"
   
   # 终止进程
   pkill -f npu_demo
   pkill -f mosim-top
   ```
