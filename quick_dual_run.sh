#!/bin/bash

# 快速双脚本后台运行器 - 适用于VSCode集成终端
# 使用方法: ./quick_dual_run.sh

BASE_DIR="/data/users/jxchen/mosim_workspace/work"

echo "🚀 快速启动双脚本..."

# 后台运行第一个脚本
echo "📍 启动 npu_run.sh..."
(cd "${BASE_DIR}/python" && ./npu_run.sh) &
NPU_PID=$!

# 后台运行第二个脚本  
echo "📍 启动 run_with_log.sh..."
(cd "${BASE_DIR}/soc/n900_vnice_npu_soc" && ./run_with_log.sh) &
SOC_PID=$!

echo ""
echo "✅ 两个脚本已启动 (PID: $NPU_PID, $SOC_PID)"
echo "💡 日志文件位置："
echo "   - NPU Demo: ${BASE_DIR}/python/logs/npu_demo_output.log"
echo "   - SOC Log:  ${BASE_DIR}/python/logs/mosim_output.log"
echo ""
echo "📊 监控命令："
echo "   tail -f ${BASE_DIR}/python/logs/npu_demo_output.log"
echo "   tail -f ${BASE_DIR}/python/logs/mosim_output.log"
echo ""
echo "🛑 停止脚本: kill $NPU_PID $SOC_PID"

# 可选：等待完成
read -p "是否等待脚本完成? (y/n): " wait_choice
if [[ $wait_choice =~ ^[Yy]$ ]]; then
    echo "等待脚本完成..."
    wait $NPU_PID $SOC_PID
    echo "✅ 所有脚本执行完成"
fi
